name: CD Pipeline

on:
  push:
    branches: [main]
    tags:
      - 'v*'

env:
  CLUSTER_NAME: yemen-market-prod
  NAMESPACE: yemen-market-v2

jobs:
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name ${{ env.CLUSTER_NAME }}-staging --region ${{ secrets.AWS_REGION }}
      
      - name: Deploy to staging
        run: |
          # Update image tags
          kubectl set image deployment/yemen-market-api \
            api=yemenmarket/api:sha-${GITHUB_SHA:0:7} \
            -n ${{ env.NAMESPACE }}-staging
          
          kubectl set image deployment/yemen-market-worker \
            worker=yemenmarket/api:sha-${GITHUB_SHA:0:7}-worker \
            -n ${{ env.NAMESPACE }}-staging
          
          # Wait for rollout
          kubectl rollout status deployment/yemen-market-api -n ${{ env.NAMESPACE }}-staging
          kubectl rollout status deployment/yemen-market-worker -n ${{ env.NAMESPACE }}-staging
      
      - name: Run smoke tests
        run: |
          API_URL=$(kubectl get ingress yemen-market-ingress -n ${{ env.NAMESPACE }}-staging -o jsonpath='{.spec.rules[0].host}')
          
          # Health check
          curl -f https://$API_URL/health || exit 1
          
          # Basic API test
          curl -f https://$API_URL/api/v1/markets?limit=1 || exit 1

  integration-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    environment: staging
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
      
      - name: Install test dependencies
        run: |
          pip install pytest httpx
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Get staging URL
        id: staging-url
        run: |
          aws eks update-kubeconfig --name ${{ env.CLUSTER_NAME }}-staging --region ${{ secrets.AWS_REGION }}
          API_URL=$(kubectl get ingress yemen-market-ingress -n ${{ env.NAMESPACE }}-staging -o jsonpath='{.spec.rules[0].host}')
          echo "url=https://$API_URL" >> $GITHUB_OUTPUT
      
      - name: Run E2E tests
        env:
          API_URL: ${{ steps.staging-url.outputs.url }}
          API_KEY: ${{ secrets.STAGING_API_KEY }}
        run: |
          pytest tests/e2e -v --api-url=$API_URL

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [integration-tests]
    environment: production
    if: startsWith(github.ref, 'refs/tags/v')
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --name ${{ env.CLUSTER_NAME }} --region ${{ secrets.AWS_REGION }}
      
      - name: Create backup
        run: |
          # Backup current deployment
          kubectl get deployment yemen-market-api -n ${{ env.NAMESPACE }} -o yaml > api-backup.yaml
          kubectl get deployment yemen-market-worker -n ${{ env.NAMESPACE }} -o yaml > worker-backup.yaml
          
          # Upload to S3
          aws s3 cp api-backup.yaml s3://yemen-market-backups/deployments/$(date +%Y%m%d-%H%M%S)/
          aws s3 cp worker-backup.yaml s3://yemen-market-backups/deployments/$(date +%Y%m%d-%H%M%S)/
      
      - name: Deploy to production
        run: |
          # Update image tags
          kubectl set image deployment/yemen-market-api \
            api=yemenmarket/api:${GITHUB_REF#refs/tags/} \
            -n ${{ env.NAMESPACE }}
          
          kubectl set image deployment/yemen-market-worker \
            worker=yemenmarket/api:${GITHUB_REF#refs/tags/}-worker \
            -n ${{ env.NAMESPACE }}
          
          # Wait for rollout with timeout
          kubectl rollout status deployment/yemen-market-api -n ${{ env.NAMESPACE }} --timeout=10m
          kubectl rollout status deployment/yemen-market-worker -n ${{ env.NAMESPACE }} --timeout=10m
      
      - name: Verify deployment
        run: |
          # Check pod status
          kubectl get pods -n ${{ env.NAMESPACE }} -l app=yemen-market-api
          kubectl get pods -n ${{ env.NAMESPACE }} -l app=yemen-market-worker
          
          # Run health checks
          API_URL=$(kubectl get ingress yemen-market-ingress -n ${{ env.NAMESPACE }} -o jsonpath='{.spec.rules[0].host}')
          
          for i in {1..5}; do
            if curl -f https://$API_URL/health; then
              echo "Health check passed"
              break
            fi
            echo "Health check failed, retrying in 10s..."
            sleep 10
          done
      
      - name: Update monitoring
        run: |
          # Annotate Grafana with deployment
          curl -X POST https://grafana.yemen-market.example.com/api/annotations \
            -H "Authorization: Bearer ${{ secrets.GRAFANA_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d "{
              \"dashboardId\": 1,
              \"time\": $(date +%s)000,
              \"tags\": [\"deployment\", \"production\"],
              \"text\": \"Deployed version ${GITHUB_REF#refs/tags/}\"
            }"
      
      - name: Notify on success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            Production deployment successful! :rocket:
            Version: ${GITHUB_REF#refs/tags/}
            Deployed by: ${{ github.actor }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      
      - name: Rollback on failure
        if: failure()
        run: |
          echo "Deployment failed, initiating rollback..."
          
          # Restore from backup
          kubectl apply -f api-backup.yaml
          kubectl apply -f worker-backup.yaml
          
          # Wait for rollback
          kubectl rollout status deployment/yemen-market-api -n ${{ env.NAMESPACE }}
          kubectl rollout status deployment/yemen-market-worker -n ${{ env.NAMESPACE }}
          
          # Notify
          curl -X POST ${{ secrets.SLACK_WEBHOOK }} \
            -H 'Content-type: application/json' \
            -d '{"text":"Production deployment failed and was rolled back! :warning:"}'

  post-deployment:
    name: Post-Deployment Tasks
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: success()
    steps:
      - uses: actions/checkout@v4
      
      - name: Update documentation
        run: |
          # Update API docs
          curl -X POST https://api.yemen-market.example.com/api/v1/docs/refresh \
            -H "Authorization: Bearer ${{ secrets.API_KEY }}"
      
      - name: Warm up cache
        run: |
          # Pre-populate cache with common queries
          curl https://api.yemen-market.example.com/api/v1/markets
          curl https://api.yemen-market.example.com/api/v1/commodities
      
      - name: Run performance baseline
        run: |
          # Run performance tests against production
          # Results will be stored for comparison