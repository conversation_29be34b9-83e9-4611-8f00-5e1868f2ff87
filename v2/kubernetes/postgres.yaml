apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: yemen-market-v2
  labels:
    app: postgres
spec:
  ports:
    - port: 5432
      targetPort: 5432
  selector:
    app: postgres
  clusterIP: None

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: yemen-market-v2
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: yemen-market-v2
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:15-alpine
          ports:
            - containerPort: 5432
              name: postgres
          env:
            - name: POSTGRES_DB
              value: yemen_market_v2
            - name: POSTGRES_USER
              value: yemen_market
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: DB_PASSWORD
            - name: PGDATA
              value: /var/lib/postgresql/data/pgdata
          volumeMounts:
            - name: postgres-storage
              mountPath: /var/lib/postgresql/data
            - name: init-scripts
              mountPath: /docker-entrypoint-initdb.d
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - pg_isready -U yemen_market
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - pg_isready -U yemen_market
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "2000m"
      volumes:
        - name: postgres-storage
          persistentVolumeClaim:
            claimName: postgres-pvc
        - name: init-scripts
          configMap:
            name: postgres-init-scripts

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-scripts
  namespace: yemen-market-v2
data:
  01-extensions.sql: |
    -- Enable required extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "postgis";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm";
    CREATE EXTENSION IF NOT EXISTS "btree_gist";
    
  02-performance.sql: |
    -- Performance settings
    ALTER SYSTEM SET shared_buffers = '256MB';
    ALTER SYSTEM SET effective_cache_size = '1GB';
    ALTER SYSTEM SET maintenance_work_mem = '64MB';
    ALTER SYSTEM SET work_mem = '4MB';
    ALTER SYSTEM SET max_connections = '200';
    
    -- Logging
    ALTER SYSTEM SET log_statement = 'mod';
    ALTER SYSTEM SET log_duration = 'on';
    ALTER SYSTEM SET log_min_duration_statement = '1000';
    
    -- Autovacuum
    ALTER SYSTEM SET autovacuum_vacuum_scale_factor = '0.1';
    ALTER SYSTEM SET autovacuum_analyze_scale_factor = '0.05';