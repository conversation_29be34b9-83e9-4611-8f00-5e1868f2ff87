apiVersion: apps/v1
kind: Deployment
metadata:
  name: yemen-market-worker
  namespace: yemen-market-v2
  labels:
    app: yemen-market-worker
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: yemen-market-worker
  template:
    metadata:
      labels:
        app: yemen-market-worker
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: yemen-market-worker
      initContainers:
        - name: wait-for-services
          image: busybox:1.35
          command: ['sh', '-c', 'until nc -z postgres-service 5432 && nc -z redis-service 6379; do echo waiting for services; sleep 2; done']
      containers:
        - name: worker
          image: yemen-market-v2-worker:latest
          imagePullPolicy: Always
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: DATABASE_URL
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_URL
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: yemen-market-config
                  key: LOG_LEVEL
            - name: WORKER_CONCURRENCY
              valueFrom:
                configMapKeyRef:
                  name: yemen-market-config
                  key: WORKER_CONCURRENCY
            - name: WORKER_MAX_TASKS_PER_CHILD
              valueFrom:
                configMapKeyRef:
                  name: yemen-market-config
                  key: WORKER_MAX_TASKS_PER_CHILD
            - name: WORKER_TASK_TIME_LIMIT
              valueFrom:
                configMapKeyRef:
                  name: yemen-market-config
                  key: WORKER_TASK_TIME_LIMIT
          livenessProbe:
            exec:
              command:
                - python
                - -c
                - "import sys; from src.infrastructure.workers.health import check_health; sys.exit(0 if check_health() else 1)"
            initialDelaySeconds: 30
            periodSeconds: 60
            timeoutSeconds: 10
            failureThreshold: 3
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "2000m"
          volumeMounts:
            - name: logs
              mountPath: /app/logs
            - name: tmp
              mountPath: /tmp
      volumes:
        - name: logs
          emptyDir: {}
        - name: tmp
          emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - yemen-market-worker
                topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: yemen-market-worker
  namespace: yemen-market-v2

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: yemen-market-worker-pdb
  namespace: yemen-market-v2
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: yemen-market-worker

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: daily-data-sync
  namespace: yemen-market-v2
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: data-sync
              image: yemen-market-v2-worker:latest
              command: ["python", "-m", "src.infrastructure.jobs.daily_sync"]
              env:
                - name: DATABASE_URL
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: DATABASE_URL
                - name: WFP_API_KEY
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: WFP_API_KEY
                - name: ACLED_API_KEY
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: ACLED_API_KEY
              resources:
                requests:
                  memory: "512Mi"
                  cpu: "250m"
                limits:
                  memory: "1Gi"
                  cpu: "500m"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: weekly-reports
  namespace: yemen-market-v2
spec:
  schedule: "0 9 * * 1"  # Weekly on Monday at 9 AM
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: report-generator
              image: yemen-market-v2-worker:latest
              command: ["python", "-m", "src.infrastructure.jobs.weekly_reports"]
              env:
                - name: DATABASE_URL
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: DATABASE_URL
                - name: REDIS_URL
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: REDIS_URL
              resources:
                requests:
                  memory: "1Gi"
                  cpu: "500m"
                limits:
                  memory: "2Gi"
                  cpu: "1000m"