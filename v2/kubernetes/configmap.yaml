apiVersion: v1
kind: ConfigMap
metadata:
  name: yemen-market-config
  namespace: yemen-market-v2
data:
  # Application configuration
  LOG_LEVEL: "INFO"
  API_ENVIRONMENT: "production"
  
  # Feature flags
  ENABLE_CACHE: "true"
  ENABLE_METRICS: "true"
  ENABLE_TRACING: "true"
  
  # Database configuration (non-sensitive)
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "yemen_market_v2"
  
  # Redis configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  
  # API configuration
  API_RATE_LIMIT: "100"
  API_RATE_LIMIT_PERIOD: "60"
  API_MAX_PAGE_SIZE: "1000"
  API_DEFAULT_PAGE_SIZE: "50"
  
  # Analysis configuration
  ANALYSIS_TIMEOUT: "300"
  ANALYSIS_MAX_MARKETS: "50"
  ANALYSIS_MAX_DATE_RANGE_DAYS: "1095"
  
  # Worker configuration
  WORKER_CONCURRENCY: "4"
  WORKER_MAX_TASKS_PER_CHILD: "1000"
  WORKER_TASK_TIME_LIMIT: "3600"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: yemen-market-v2
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 2048;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        # Performance optimizations
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 100M;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css text/xml text/javascript 
                   application/json application/javascript application/xml+rss;

        # Rate limiting zones
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

        # Upstream servers
        upstream api_backend {
            least_conn;
            server api-service:8000 max_fails=3 fail_timeout=30s;
        }

        # SSL configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Main server block
        server {
            listen 80;
            server_name _;

            # Force HTTPS
            return 301 https://$host$request_uri;
        }

        server {
            listen 443 ssl http2;
            server_name _;

            ssl_certificate /etc/nginx/ssl/cert.pem;
            ssl_certificate_key /etc/nginx/ssl/key.pem;

            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;
            add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;

            # API endpoints
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                
                proxy_pass http://api_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Timeouts for long-running requests
                proxy_read_timeout 300s;
                proxy_connect_timeout 30s;
                proxy_send_timeout 300s;
            }

            # Auth endpoints with stricter rate limiting
            location /api/v1/auth/ {
                limit_req zone=auth burst=5 nodelay;
                
                proxy_pass http://api_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Health checks
            location /health {
                proxy_pass http://api_backend/health;
                access_log off;
            }

            # Metrics endpoint (internal only)
            location /metrics {
                proxy_pass http://api_backend/metrics;
                allow 10.0.0.0/8;
                deny all;
            }

            # Static files (if any)
            location /static/ {
                alias /usr/share/nginx/html/static/;
                expires 30d;
                add_header Cache-Control "public, immutable";
            }

            # Documentation
            location /docs {
                proxy_pass http://api_backend/docs;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
            }

            # Default 404
            location / {
                return 404;
            }
        }
    }