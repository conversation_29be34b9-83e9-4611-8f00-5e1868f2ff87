apiVersion: v1
kind: Secret
metadata:
  name: yemen-market-secrets
  namespace: yemen-market-v2
type: Opaque
stringData:
  # Database credentials
  DB_PASSWORD: "changeme-in-production"
  DATABASE_URL: "**********************************************************************/yemen_market_v2"
  
  # Redis password
  REDIS_PASSWORD: "changeme-in-production"
  REDIS_URL: "redis://:changeme-in-production@redis-service:6379/0"
  
  # API secrets
  API_KEY: "your-api-key-here"
  JWT_SECRET: "your-jwt-secret-here"
  
  # External service credentials
  WFP_API_KEY: "your-wfp-api-key"
  ACLED_API_KEY: "your-acled-api-key"
  
  # AWS credentials (for S3 storage)
  AWS_ACCESS_KEY_ID: "your-aws-access-key"
  AWS_SECRET_ACCESS_KEY: "your-aws-secret-key"
  
  # Monitoring credentials
  GRAFANA_ADMIN_PASSWORD: "changeme-in-production"
  
---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: yemen-market-v2
type: kubernetes.io/tls
data:
  # Base64 encoded TLS certificate and key
  # In production, use cert-manager or similar
  tls.crt: LS0tLS1CRUdJTi... # Replace with actual certificate
  tls.key: LS0tLS1CRUdJTi... # Replace with actual key