apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: yemen-market-v2
  labels:
    app: prometheus
spec:
  ports:
    - port: 9090
      targetPort: 9090
  selector:
    app: prometheus

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: yemen-market-v2
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'yemen-market-prod'
        
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
                - alertmanager:9093

    rule_files:
      - '/etc/prometheus/rules/*.yml'

    scrape_configs:
      # API metrics
      - job_name: 'yemen-market-api'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - yemen-market-v2
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: replace
            target_label: app
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name

      # Node metrics
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)

      # PostgreSQL exporter
      - job_name: 'postgres'
        static_configs:
          - targets: ['postgres-exporter:9187']

      # Redis exporter
      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: yemen-market-v2
data:
  alerts.yml: |
    groups:
      - name: yemen_market_alerts
        interval: 30s
        rules:
          # API alerts
          - alert: HighRequestLatency
            expr: histogram_quantile(0.95, http_request_duration_seconds_bucket{job="yemen-market-api"}) > 0.5
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: High request latency detected
              description: "95th percentile latency is {{ $value }}s"

          - alert: HighErrorRate
            expr: rate(http_requests_total{job="yemen-market-api",status=~"5.."}[5m]) > 0.05
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: High error rate detected
              description: "Error rate is {{ $value | humanizePercentage }}"

          - alert: PodDown
            expr: up{job="yemen-market-api"} == 0
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: API pod is down
              description: "Pod {{ $labels.kubernetes_pod_name }} is down"

          # Database alerts
          - alert: DatabaseConnectionsHigh
            expr: pg_stat_database_numbackends{datname="yemen_market_v2"} > 150
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: High number of database connections
              description: "{{ $value }} connections to database"

          - alert: DatabaseDeadlocks
            expr: rate(pg_stat_database_deadlocks{datname="yemen_market_v2"}[5m]) > 0
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: Database deadlocks detected
              description: "Deadlock rate: {{ $value }}"

          # Redis alerts
          - alert: RedisMemoryHigh
            expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: Redis memory usage high
              description: "Redis memory usage is {{ $value | humanizePercentage }}"

          # Worker alerts
          - alert: WorkerQueueBacklog
            expr: celery_queue_length{queue="default"} > 1000
            for: 10m
            labels:
              severity: warning
            annotations:
              summary: Worker queue backlog detected
              description: "{{ $value }} tasks in queue"

          - alert: WorkerTaskFailures
            expr: rate(celery_task_failed_total[5m]) > 0.1
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: High worker task failure rate
              description: "Task failure rate: {{ $value }}"

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prometheus
  namespace: yemen-market-v2
spec:
  serviceName: prometheus
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
        - name: prometheus
          image: prom/prometheus:v2.45.0
          args:
            - '--config.file=/etc/prometheus/prometheus.yml'
            - '--storage.tsdb.path=/prometheus'
            - '--storage.tsdb.retention.time=30d'
            - '--web.console.libraries=/usr/share/prometheus/console_libraries'
            - '--web.console.templates=/usr/share/prometheus/consoles'
            - '--web.enable-lifecycle'
          ports:
            - containerPort: 9090
          volumeMounts:
            - name: config
              mountPath: /etc/prometheus/prometheus.yml
              subPath: prometheus.yml
            - name: rules
              mountPath: /etc/prometheus/rules
            - name: storage
              mountPath: /prometheus
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
      volumes:
        - name: config
          configMap:
            name: prometheus-config
        - name: rules
          configMap:
            name: prometheus-rules
  volumeClaimTemplates:
    - metadata:
        name: storage
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 50Gi

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: yemen-market-v2

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
  - apiGroups: [""]
    resources:
      - nodes
      - nodes/proxy
      - services
      - endpoints
      - pods
    verbs: ["get", "list", "watch"]
  - apiGroups:
      - extensions
    resources:
      - ingresses
    verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
  - kind: ServiceAccount
    name: prometheus
    namespace: yemen-market-v2

---
# Grafana
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: yemen-market-v2
spec:
  ports:
    - port: 3000
      targetPort: 3000
  selector:
    app: grafana

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: yemen-market-v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
        - name: grafana
          image: grafana/grafana:10.0.0
          ports:
            - containerPort: 3000
          env:
            - name: GF_SECURITY_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: GRAFANA_ADMIN_PASSWORD
            - name: GF_INSTALL_PLUGINS
              value: "grafana-piechart-panel,grafana-worldmap-panel"
          volumeMounts:
            - name: datasources
              mountPath: /etc/grafana/provisioning/datasources
            - name: dashboards-config
              mountPath: /etc/grafana/provisioning/dashboards
            - name: dashboards
              mountPath: /var/lib/grafana/dashboards
            - name: storage
              mountPath: /var/lib/grafana
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: datasources
          configMap:
            name: grafana-datasources
        - name: dashboards-config
          configMap:
            name: grafana-dashboards-config
        - name: dashboards
          configMap:
            name: grafana-dashboards
        - name: storage
          emptyDir: {}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: yemen-market-v2
data:
  prometheus.yml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://prometheus:9090
        isDefault: true