apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: yemen-market-v2
  labels:
    app: redis
spec:
  ports:
    - port: 6379
      targetPort: 6379
  selector:
    app: redis

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: yemen-market-v2
data:
  redis.conf: |
    # Redis configuration
    port 6379
    bind 0.0.0.0
    protected-mode yes
    requirepass changeme-in-production
    
    # Persistence
    appendonly yes
    appendfsync everysec
    
    # Memory management
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    
    # Performance
    tcp-keepalive 60
    timeout 300
    
    # Logging
    loglevel notice
    
    # Slow log
    slowlog-log-slower-than 10000
    slowlog-max-len 128

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: yemen-market-v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7-alpine
          command:
            - redis-server
            - /usr/local/etc/redis/redis.conf
          ports:
            - containerPort: 6379
          volumeMounts:
            - name: redis-config
              mountPath: /usr/local/etc/redis
            - name: redis-data
              mountPath: /data
          livenessProbe:
            tcpSocket:
              port: 6379
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            exec:
              command:
                - redis-cli
                - ping
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "2Gi"
              cpu: "500m"
      volumes:
        - name: redis-config
          configMap:
            name: redis-config
        - name: redis-data
          emptyDir: {}  # Use PVC in production

---
# For production, use Redis Sentinel or Redis Cluster
apiVersion: v1
kind: Service
metadata:
  name: redis-sentinel
  namespace: yemen-market-v2
  labels:
    app: redis-sentinel
spec:
  ports:
    - port: 26379
      targetPort: 26379
  selector:
    app: redis-sentinel
  clusterIP: None