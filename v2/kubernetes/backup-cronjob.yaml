apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: yemen-market-v2
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: DB_PASSWORD
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: AWS_ACCESS_KEY_ID
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: AWS_SECRET_ACCESS_KEY
            - name: S3_BUCKET
              value: yemen-market-backups
            - name: S3_PREFIX
              value: postgres-backups
            command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "Starting backup..."
              
              # Generate backup filename
              BACKUP_DATE=$(date +%Y%m%d-%H%M%S)
              BACKUP_FILE="yemen_market_v2_${BACKUP_DATE}.sql.gz"
              
              # Perform backup
              echo "Backing up database..."
              pg_dump -h postgres-service -U yemen_market -d yemen_market_v2 | gzip > /tmp/${BACKUP_FILE}
              
              # Install AWS CLI
              apk add --no-cache aws-cli
              
              # Upload to S3
              echo "Uploading to S3..."
              aws s3 cp /tmp/${BACKUP_FILE} s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_FILE}
              
              # Verify upload
              aws s3 ls s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_FILE}
              
              # Clean up old backups (keep last 30)
              echo "Cleaning up old backups..."
              aws s3 ls s3://${S3_BUCKET}/${S3_PREFIX}/ | \
                sort -r | \
                tail -n +31 | \
                awk '{print $4}' | \
                xargs -I {} aws s3 rm s3://${S3_BUCKET}/${S3_PREFIX}/{}
              
              echo "Backup completed successfully"
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "500m"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-verification
  namespace: yemen-market-v2
spec:
  schedule: "0 6 * * *"  # Daily at 6 AM
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: verify-backup
            image: postgres:15-alpine
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: DB_PASSWORD
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: AWS_ACCESS_KEY_ID
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: AWS_SECRET_ACCESS_KEY
            - name: S3_BUCKET
              value: yemen-market-backups
            - name: S3_PREFIX
              value: postgres-backups
            command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "Starting backup verification..."
              
              # Install AWS CLI
              apk add --no-cache aws-cli
              
              # Get latest backup
              LATEST_BACKUP=$(aws s3 ls s3://${S3_BUCKET}/${S3_PREFIX}/ | \
                sort | tail -n 1 | awk '{print $4}')
              
              if [ -z "$LATEST_BACKUP" ]; then
                echo "ERROR: No backups found!"
                exit 1
              fi
              
              echo "Latest backup: $LATEST_BACKUP"
              
              # Download backup
              aws s3 cp s3://${S3_BUCKET}/${S3_PREFIX}/${LATEST_BACKUP} /tmp/
              
              # Create test database
              createdb -h postgres-service -U yemen_market test_restore || true
              
              # Test restore
              echo "Testing restore..."
              gunzip -c /tmp/${LATEST_BACKUP} | \
                psql -h postgres-service -U yemen_market -d test_restore
              
              # Verify restore
              TABLE_COUNT=$(psql -h postgres-service -U yemen_market -d test_restore \
                -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'")
              
              if [ "$TABLE_COUNT" -lt 10 ]; then
                echo "ERROR: Restore verification failed - only $TABLE_COUNT tables found"
                exit 1
              fi
              
              # Cleanup
              dropdb -h postgres-service -U yemen_market test_restore
              
              echo "Backup verification completed successfully"
              echo "Tables restored: $TABLE_COUNT"