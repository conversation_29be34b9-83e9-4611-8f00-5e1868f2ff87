apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: yemen-market-v2
  labels:
    app: yemen-market-api
spec:
  ports:
    - port: 8000
      targetPort: 8000
      name: http
  selector:
    app: yemen-market-api

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yemen-market-api
  namespace: yemen-market-v2
  labels:
    app: yemen-market-api
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: yemen-market-api
  template:
    metadata:
      labels:
        app: yemen-market-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: yemen-market-api
      initContainers:
        - name: wait-for-db
          image: busybox:1.35
          command: ['sh', '-c', 'until nc -z postgres-service 5432; do echo waiting for db; sleep 2; done']
        - name: run-migrations
          image: yemen-market-v2:latest
          imagePullPolicy: Always
          command: ['python', '-m', 'alembic', 'upgrade', 'head']
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: DATABASE_URL
      containers:
        - name: api
          image: yemen-market-v2:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8000
              name: http
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: DATABASE_URL
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_URL
            - name: API_KEY
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: API_KEY
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: JWT_SECRET
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: yemen-market-config
                  key: LOG_LEVEL
            - name: API_ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: yemen-market-config
                  key: API_ENVIRONMENT
          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: 8000
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
          volumeMounts:
            - name: logs
              mountPath: /app/logs
      volumes:
        - name: logs
          emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - yemen-market-api
                topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: yemen-market-api
  namespace: yemen-market-v2

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: yemen-market-api-pdb
  namespace: yemen-market-v2
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: yemen-market-api

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: yemen-market-api-hpa
  namespace: yemen-market-v2
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-market-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    - type: Pods
      pods:
        metric:
          name: http_requests_per_second
        target:
          type: AverageValue
          averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 50
          periodSeconds: 60