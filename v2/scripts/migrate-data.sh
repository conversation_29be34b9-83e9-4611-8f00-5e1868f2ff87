#!/bin/bash
# Data migration script from v1 to v2

set -euo pipefail

# Configuration
V1_PATH="${V1_PATH:-../../v1}"
NAMESPACE="yemen-market-v2"
BATCH_SIZE="${BATCH_SIZE:-1000}"
DRY_RUN="${DRY_RUN:-false}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Pre-migration checks
pre_migration_checks() {
    log_section "Pre-Migration Checks"
    
    # Check v1 directory exists
    if [ ! -d "$V1_PATH" ]; then
        log_error "v1 directory not found at: $V1_PATH"
        exit 1
    fi
    
    # Check v1 has data
    if [ ! -d "$V1_PATH/data" ]; then
        log_error "No data directory found in v1"
        exit 1
    fi
    
    # Check database connection
    log_info "Checking v2 database connection..."
    if kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
        psql -U yemen_market -d yemen_market_v2 -c "SELECT 1" &> /dev/null; then
        log_info "Database connection successful"
    else
        log_error "Cannot connect to v2 database"
        exit 1
    fi
    
    # Backup v2 database
    if [ "$DRY_RUN" = "false" ]; then
        log_info "Creating database backup..."
        timestamp=$(date +%Y%m%d-%H%M%S)
        kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
            pg_dump -U yemen_market yemen_market_v2 > "backup-pre-migration-$timestamp.sql"
        log_info "Backup saved to: backup-pre-migration-$timestamp.sql"
    fi
}

# Export v1 data
export_v1_data() {
    log_section "Exporting v1 Data"
    
    # Create temporary directory
    export_dir="/tmp/yemen-market-migration-$(date +%s)"
    mkdir -p "$export_dir"
    
    log_info "Exporting data to: $export_dir"
    
    # Run v1 export script
    cd "$V1_PATH"
    
    # Export markets
    if [ -f "scripts/export_markets.py" ]; then
        log_info "Exporting markets..."
        python scripts/export_markets.py --output "$export_dir/markets.json"
    fi
    
    # Export prices
    if [ -f "scripts/export_prices.py" ]; then
        log_info "Exporting prices..."
        python scripts/export_prices.py --output "$export_dir/prices.json"
    fi
    
    # Export conflict data
    if [ -f "scripts/export_conflicts.py" ]; then
        log_info "Exporting conflicts..."
        python scripts/export_conflicts.py --output "$export_dir/conflicts.json"
    fi
    
    # If no export scripts, try direct data access
    if [ ! -f "$export_dir/markets.json" ]; then
        log_warn "No export scripts found, attempting direct data export..."
        
        # Copy CSV/JSON files directly
        find data -name "*.csv" -o -name "*.json" | while read -r file; do
            cp "$file" "$export_dir/"
        done
    fi
    
    cd - > /dev/null
    echo "$export_dir"
}

# Run migration job
run_migration_job() {
    local export_dir="$1"
    
    log_section "Running Migration Job"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_warn "DRY RUN MODE - No data will be migrated"
    fi
    
    # Copy data to pod
    log_info "Copying data to migration pod..."
    
    # Create migration job
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: migration-pod
  namespace: $NAMESPACE
spec:
  restartPolicy: Never
  containers:
  - name: migration
    image: yemen-market-v2:latest
    command: ["sleep", "3600"]
    env:
    - name: DATABASE_URL
      valueFrom:
        secretKeyRef:
          name: yemen-market-secrets
          key: DATABASE_URL
    volumeMounts:
    - name: migration-data
      mountPath: /migration-data
  volumes:
  - name: migration-data
    emptyDir: {}
EOF
    
    # Wait for pod to be ready
    kubectl wait --for=condition=ready pod/migration-pod -n "$NAMESPACE" --timeout=120s
    
    # Copy data files
    for file in "$export_dir"/*; do
        if [ -f "$file" ]; then
            log_info "Copying $(basename "$file")..."
            kubectl cp "$file" "$NAMESPACE/migration-pod:/migration-data/"
        fi
    done
    
    # Run migration script
    log_info "Starting data migration..."
    
    migration_args=(
        "python" "-m" "tools.migration.migrate_data"
        "--data-dir" "/migration-data"
        "--batch-size" "$BATCH_SIZE"
    )
    
    if [ "$DRY_RUN" = "true" ]; then
        migration_args+=("--dry-run")
    fi
    
    kubectl exec -it migration-pod -n "$NAMESPACE" -- "${migration_args[@]}"
    
    # Cleanup
    kubectl delete pod migration-pod -n "$NAMESPACE" --wait=false
}

# Validate migration
validate_migration() {
    log_section "Validating Migration"
    
    # Create validation job
    cat <<EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: migration-validation-$(date +%s)
  namespace: $NAMESPACE
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: validate
        image: yemen-market-v2:latest
        command: ["python", "-m", "tools.migration.validate_migration"]
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-market-secrets
              key: DATABASE_URL
EOF
    
    # Wait for validation to complete
    log_info "Running validation checks..."
    kubectl wait --for=condition=complete job \
        -l job-name=migration-validation -n "$NAMESPACE" \
        --timeout=600s
    
    # Get validation results
    pod_name=$(kubectl get pods -n "$NAMESPACE" \
        -l job-name=migration-validation \
        -o jsonpath='{.items[0].metadata.name}')
    
    kubectl logs "$pod_name" -n "$NAMESPACE"
}

# Data comparison report
generate_comparison_report() {
    log_section "Generating Comparison Report"
    
    report_file="migration-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "Yemen Market Integration - Migration Report"
        echo "=========================================="
        echo "Date: $(date)"
        echo "Source: $V1_PATH"
        echo "Target: v2 ($NAMESPACE)"
        echo
        
        # Get counts from v2
        echo "Data Counts:"
        echo "-----------"
        
        # Markets count
        market_count=$(kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
            psql -U yemen_market -d yemen_market_v2 -t -c \
            "SELECT COUNT(*) FROM markets" | tr -d ' ')
        echo "Markets: $market_count"
        
        # Prices count
        price_count=$(kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
            psql -U yemen_market -d yemen_market_v2 -t -c \
            "SELECT COUNT(*) FROM price_observations" | tr -d ' ')
        echo "Price Observations: $price_count"
        
        # Date range
        echo
        echo "Date Ranges:"
        echo "-----------"
        date_range=$(kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
            psql -U yemen_market -d yemen_market_v2 -t -c \
            "SELECT MIN(observed_date), MAX(observed_date) FROM price_observations")
        echo "Price observations: $date_range"
        
        # Commodity coverage
        echo
        echo "Commodity Coverage:"
        echo "-----------------"
        kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
            psql -U yemen_market -d yemen_market_v2 -c \
            "SELECT commodity_code, COUNT(*) as observations 
             FROM price_observations 
             GROUP BY commodity_code 
             ORDER BY observations DESC 
             LIMIT 10"
        
        # Market coverage
        echo
        echo "Market Coverage:"
        echo "---------------"
        kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
            psql -U yemen_market -d yemen_market_v2 -c \
            "SELECT m.name, COUNT(p.id) as observations 
             FROM markets m 
             LEFT JOIN price_observations p ON m.id = p.market_id 
             GROUP BY m.id, m.name 
             ORDER BY observations DESC 
             LIMIT 10"
        
    } > "$report_file"
    
    log_info "Report saved to: $report_file"
    
    # Display summary
    echo
    cat "$report_file"
}

# Post-migration tasks
post_migration_tasks() {
    log_section "Post-Migration Tasks"
    
    # Update statistics
    log_info "Updating database statistics..."
    kubectl exec -it statefulset/postgres -n "$NAMESPACE" -- \
        psql -U yemen_market -d yemen_market_v2 -c "ANALYZE;"
    
    # Clear cache
    log_info "Clearing Redis cache..."
    redis_password=$(kubectl get secret yemen-market-secrets -n "$NAMESPACE" \
        -o jsonpath='{.data.REDIS_PASSWORD}' | base64 -d)
    kubectl exec -it deployment/redis -n "$NAMESPACE" -- \
        redis-cli -a "$redis_password" FLUSHALL
    
    # Restart API to pick up new data
    log_info "Restarting API pods..."
    kubectl rollout restart deployment/yemen-market-api -n "$NAMESPACE"
    kubectl rollout status deployment/yemen-market-api -n "$NAMESPACE"
    
    # Run initial analysis
    log_info "Running initial analysis to verify data..."
    kubectl exec -it deployment/yemen-market-api -n "$NAMESPACE" -- \
        python -m src.scripts.verify_migration
}

# Main execution
main() {
    echo "Yemen Market Integration - Data Migration"
    echo "========================================"
    echo "Source: $V1_PATH"
    echo "Target: v2 ($NAMESPACE)"
    echo "Batch Size: $BATCH_SIZE"
    echo "Dry Run: $DRY_RUN"
    echo
    
    # Confirm before proceeding
    if [ "$DRY_RUN" = "false" ]; then
        echo -n "This will migrate data to the v2 system. Continue? (yes/no): "
        read -r confirmation
        if [ "$confirmation" != "yes" ]; then
            log_error "Migration cancelled"
            exit 1
        fi
    fi
    
    # Run migration steps
    pre_migration_checks
    export_dir=$(export_v1_data)
    run_migration_job "$export_dir"
    
    if [ "$DRY_RUN" = "false" ]; then
        validate_migration
        generate_comparison_report
        post_migration_tasks
    fi
    
    # Cleanup
    rm -rf "$export_dir"
    
    log_info "Migration completed successfully!"
    
    if [ "$DRY_RUN" = "true" ]; then
        echo
        log_warn "This was a dry run. To perform actual migration, run:"
        log_warn "DRY_RUN=false $0"
    fi
}

# Run main function
main "$@"