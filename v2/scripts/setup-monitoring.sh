#!/bin/bash
# Setup comprehensive monitoring and alerting

set -euo pipefail

NAMESPACE="yemen-market-v2"
GRAFANA_VERSION="10.0.0"
PROMETHEUS_VERSION="v2.45.0"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

# Install Prometheus Operator
install_prometheus_operator() {
    log_info "Installing Prometheus Operator..."
    
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    helm install prometheus-operator prometheus-community/kube-prometheus-stack \
        --namespace "$NAMESPACE" \
        --set prometheus.prometheusSpec.serviceMonitorSelectorNilUsesHelmValues=false \
        --set prometheus.prometheusSpec.retention=30d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
        --set grafana.adminPassword="${GRAFANA_PASSWORD:-admin}" \
        --set grafana.persistence.enabled=true \
        --set grafana.persistence.size=10Gi
}

# Create custom dashboards
create_dashboards() {
    log_info "Creating Grafana dashboards..."
    
    # Yemen Market Overview Dashboard
    cat <<'EOF' | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: yemen-market-dashboard
  namespace: yemen-market-v2
  labels:
    grafana_dashboard: "1"
data:
  yemen-market-overview.json: |
    {
      "dashboard": {
        "title": "Yemen Market Integration - Overview",
        "panels": [
          {
            "title": "API Request Rate",
            "targets": [
              {
                "expr": "rate(http_requests_total{job=\"yemen-market-api\"}[5m])"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "title": "API Latency (p95)",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"yemen-market-api\"}[5m]))"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "title": "Active Markets",
            "targets": [
              {
                "expr": "yemen_market_active_markets_total"
              }
            ],
            "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}
          },
          {
            "title": "Price Observations (24h)",
            "targets": [
              {
                "expr": "increase(yemen_market_price_observations_total[24h])"
              }
            ],
            "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}
          },
          {
            "title": "Analysis Jobs Queue",
            "targets": [
              {
                "expr": "yemen_market_analysis_queue_length"
              }
            ],
            "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}
          }
        ]
      }
    }
EOF

    # Database Performance Dashboard
    cat <<'EOF' | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: yemen-market-db-dashboard
  namespace: yemen-market-v2
  labels:
    grafana_dashboard: "1"
data:
  database-performance.json: |
    {
      "dashboard": {
        "title": "Yemen Market - Database Performance",
        "panels": [
          {
            "title": "Active Connections",
            "targets": [
              {
                "expr": "pg_stat_database_numbackends{datname=\"yemen_market_v2\"}"
              }
            ]
          },
          {
            "title": "Query Duration",
            "targets": [
              {
                "expr": "rate(pg_stat_statements_mean_exec_time{datname=\"yemen_market_v2\"}[5m])"
              }
            ]
          },
          {
            "title": "Cache Hit Ratio",
            "targets": [
              {
                "expr": "pg_stat_database_blks_hit{datname=\"yemen_market_v2\"} / (pg_stat_database_blks_hit{datname=\"yemen_market_v2\"} + pg_stat_database_blks_read{datname=\"yemen_market_v2\"})"
              }
            ]
          }
        ]
      }
    }
EOF
}

# Setup alerts
create_alerts() {
    log_info "Creating alert rules..."
    
    cat <<'EOF' | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: yemen-market-alerts
  namespace: yemen-market-v2
spec:
  groups:
  - name: yemen_market
    interval: 30s
    rules:
    # API Alerts
    - alert: HighErrorRate
      expr: |
        (
          sum(rate(http_requests_total{job="yemen-market-api",status=~"5.."}[5m]))
          /
          sum(rate(http_requests_total{job="yemen-market-api"}[5m]))
        ) > 0.05
      for: 5m
      labels:
        severity: critical
        team: backend
      annotations:
        summary: "High API error rate detected"
        description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
        
    - alert: HighLatency
      expr: |
        histogram_quantile(0.95, 
          rate(http_request_duration_seconds_bucket{job="yemen-market-api"}[5m])
        ) > 0.5
      for: 5m
      labels:
        severity: warning
        team: backend
      annotations:
        summary: "High API latency detected"
        description: "95th percentile latency is {{ $value }}s"
        
    # Database Alerts
    - alert: DatabaseConnectionsHigh
      expr: |
        pg_stat_database_numbackends{datname="yemen_market_v2"} 
        > 
        pg_settings_max_connections * 0.8
      for: 5m
      labels:
        severity: warning
        team: database
      annotations:
        summary: "Database connections near limit"
        description: "{{ $value }} connections used"
        
    - alert: DatabaseDeadlocks
      expr: |
        rate(pg_stat_database_deadlocks{datname="yemen_market_v2"}[5m]) > 0
      for: 5m
      labels:
        severity: warning
        team: database
      annotations:
        summary: "Database deadlocks detected"
        description: "{{ $value }} deadlocks per second"
        
    # Business Alerts
    - alert: NoPriceDataReceived
      expr: |
        increase(yemen_market_price_observations_total[1h]) == 0
      for: 2h
      labels:
        severity: warning
        team: data
      annotations:
        summary: "No new price data received"
        description: "No price observations in the last 2 hours"
        
    - alert: MarketDataStale
      expr: |
        (time() - yemen_market_last_price_update_timestamp) > 86400
      for: 10m
      labels:
        severity: warning
        team: data
      annotations:
        summary: "Market data is stale"
        description: "Market {{ $labels.market_id }} hasn't updated in {{ $value | humanizeDuration }}"
EOF
}

# Setup Slack notifications
setup_slack_notifications() {
    log_info "Setting up Slack notifications..."
    
    if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-slack
  namespace: $NAMESPACE
stringData:
  slack-webhook-url: "$SLACK_WEBHOOK_URL"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: $NAMESPACE
data:
  alertmanager.yaml: |
    global:
      slack_api_url_file: /etc/alertmanager/secrets/slack-webhook-url
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'slack-notifications'
      routes:
      - match:
          severity: critical
        receiver: slack-critical
    receivers:
    - name: 'slack-notifications'
      slack_configs:
      - channel: '#yemen-market-alerts'
        title: 'Yemen Market Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
    - name: 'slack-critical'
      slack_configs:
      - channel: '#yemen-market-critical'
        title: '🚨 CRITICAL: Yemen Market Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
EOF
    else
        log_info "SLACK_WEBHOOK_URL not set, skipping Slack configuration"
    fi
}

# Setup log aggregation
setup_log_aggregation() {
    log_info "Setting up log aggregation..."
    
    # Install Loki for log aggregation
    helm repo add grafana https://grafana.github.io/helm-charts
    helm install loki grafana/loki-stack \
        --namespace "$NAMESPACE" \
        --set promtail.enabled=true \
        --set loki.persistence.enabled=true \
        --set loki.persistence.size=50Gi
    
    # Configure Grafana data source for Loki
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasource-loki
  namespace: $NAMESPACE
  labels:
    grafana_datasource: "1"
data:
  loki.yaml: |
    apiVersion: 1
    datasources:
    - name: Loki
      type: loki
      access: proxy
      url: http://loki:3100
      jsonData:
        maxLines: 1000
EOF
}

# Create SLO dashboards
create_slo_dashboards() {
    log_info "Creating SLO dashboards..."
    
    cat <<'EOF' | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: yemen-market-slo-dashboard
  namespace: yemen-market-v2
  labels:
    grafana_dashboard: "1"
data:
  slo-dashboard.json: |
    {
      "dashboard": {
        "title": "Yemen Market - SLO Dashboard",
        "panels": [
          {
            "title": "API Availability SLO (99.9%)",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{job=\"yemen-market-api\",status!~\"5..\"}[30d])) / sum(rate(http_requests_total{job=\"yemen-market-api\"}[30d])) * 100"
              }
            ]
          },
          {
            "title": "API Latency SLO (p99 < 1s)",
            "targets": [
              {
                "expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job=\"yemen-market-api\"}[30d]))"
              }
            ]
          },
          {
            "title": "Data Freshness SLO (< 24h)",
            "targets": [
              {
                "expr": "(time() - yemen_market_last_price_update_timestamp) / 3600"
              }
            ]
          }
        ]
      }
    }
EOF
}

# Main execution
main() {
    log_info "Setting up monitoring for Yemen Market Integration v2..."
    
    # Install monitoring stack
    install_prometheus_operator
    
    # Wait for Grafana to be ready
    kubectl wait --for=condition=ready pod \
        -l app.kubernetes.io/name=grafana -n "$NAMESPACE" \
        --timeout=300s
    
    # Create dashboards and alerts
    create_dashboards
    create_alerts
    setup_slack_notifications
    setup_log_aggregation
    create_slo_dashboards
    
    # Get Grafana URL
    GRAFANA_URL=$(kubectl get ingress -n "$NAMESPACE" \
        -l app.kubernetes.io/name=grafana \
        -o jsonpath='{.items[0].spec.rules[0].host}')
    
    echo
    log_info "Monitoring setup complete!"
    echo
    echo "Grafana URL: http://$GRAFANA_URL"
    echo "Default credentials: admin / ${GRAFANA_PASSWORD:-admin}"
    echo
    echo "To access Grafana locally:"
    echo "kubectl port-forward -n $NAMESPACE svc/prometheus-operator-grafana 3000:80"
    echo
    echo "To access Prometheus:"
    echo "kubectl port-forward -n $NAMESPACE svc/prometheus-operator-prometheus 9090:9090"
}

main "$@"