# Yemen Market Integration v2 - Production Deployment Guide

## Prerequisites

### 1. Infrastructure Requirements

- **Kubernetes Cluster**: v1.25+ (EKS, GKE, or AKS recommended)
- **Nodes**: Minimum 3 nodes with 4 vCPUs and 16GB RAM each
- **Storage**: 500GB SSD storage for database and persistent volumes
- **Network**: Load balancer support, ingress controller

### 2. Required Tools

```bash
# Install required tools
brew install kubectl helm aws-cli jq

# Verify versions
kubectl version --client
helm version
aws --version
```

### 3. Access Requirements

- Kubernetes cluster admin access
- AWS/Cloud provider credentials
- Domain name for API access
- SSL certificates (or use cert-manager)

## Step-by-Step Deployment

### Step 1: Prepare Environment

```bash
# Clone the repository
git clone https://github.com/your-org/yemen-market-integration.git
cd yemen-market-integration/v2

# Set environment variables
export CLUSTER_NAME="yemen-market-prod"
export AWS_REGION="us-east-1"
export ENVIRONMENT="production"

# Configure kubectl
aws eks update-kubeconfig --name $CLUSTER_NAME --region $AWS_REGION
```

### Step 2: Set Required Secrets

```bash
# Export required secrets
export DB_PASSWORD="your-secure-database-password"
export JWT_SECRET="your-secure-jwt-secret"
export API_KEY="your-api-key"
export REDIS_PASSWORD="your-redis-password"

# Optional: External service credentials
export WFP_API_KEY="your-wfp-api-key"
export ACLED_API_KEY="your-acled-api-key"

# Optional: Monitoring
export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/..."
export GRAFANA_PASSWORD="your-grafana-admin-password"
```

### Step 3: Build and Push Docker Images

```bash
# Build images
docker build -t yemen-market-v2:latest .
docker build -f Dockerfile.worker -t yemen-market-v2-worker:latest .

# Tag for your registry
docker tag yemen-market-v2:latest your-registry/yemen-market-v2:latest
docker tag yemen-market-v2-worker:latest your-registry/yemen-market-v2-worker:latest

# Push to registry
docker push your-registry/yemen-market-v2:latest
docker push your-registry/yemen-market-v2-worker:latest
```

### Step 4: Deploy Infrastructure

```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run deployment
./scripts/deploy.sh
```

The deployment script will:
1. Create namespace
2. Set up secrets
3. Deploy PostgreSQL and Redis
4. Run database migrations
5. Deploy API and worker pods
6. Configure ingress
7. Set up monitoring

### Step 5: Configure DNS

After deployment, get the load balancer URL:

```bash
kubectl get ingress yemen-market-ingress -n yemen-market-v2 -o jsonpath='{.status.loadBalancer.ingress[0].hostname}'
```

Update your DNS records:
- Create CNAME record pointing to the load balancer
- Wait for DNS propagation (5-15 minutes)

### Step 6: SSL Certificate Setup

#### Option A: Using cert-manager (Recommended)

```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.12.0/cert-manager.yaml

# Create ClusterIssuer
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF

# Update ingress annotation
kubectl annotate ingress yemen-market-ingress \
  cert-manager.io/cluster-issuer=letsencrypt-prod \
  -n yemen-market-v2
```

#### Option B: Manual Certificate

```bash
# Create TLS secret
kubectl create secret tls yemen-market-tls \
  --cert=path/to/tls.crt \
  --key=path/to/tls.key \
  -n yemen-market-v2
```

### Step 7: Set Up Monitoring

```bash
# Run monitoring setup
chmod +x scripts/setup-monitoring.sh
./scripts/setup-monitoring.sh
```

Access monitoring:
```bash
# Grafana
kubectl port-forward -n yemen-market-v2 svc/prometheus-operator-grafana 3000:80
# Access at http://localhost:3000

# Prometheus
kubectl port-forward -n yemen-market-v2 svc/prometheus-operator-prometheus 9090:9090
# Access at http://localhost:9090
```

### Step 8: Data Migration

If migrating from v1:

```bash
# Run migration script
chmod +x scripts/migrate-data.sh

# Dry run first
DRY_RUN=true ./scripts/migrate-data.sh

# If everything looks good, run actual migration
DRY_RUN=false ./scripts/migrate-data.sh
```

### Step 9: Validation

Run comprehensive validation:

```bash
# Run validation script
chmod +x scripts/validate-deployment.sh
./scripts/validate-deployment.sh
```

The validation script will check:
- Pod health
- Service endpoints
- Database connectivity
- API responsiveness
- Monitoring status

### Step 10: Configure Backups

Backups are automatically configured via CronJob. Verify:

```bash
kubectl get cronjobs -n yemen-market-v2
```

To manually trigger a backup:
```bash
kubectl create job --from=cronjob/postgres-backup manual-backup-$(date +%s) -n yemen-market-v2
```

## Post-Deployment Tasks

### 1. Configure Alerts

Edit alert rules in `kubernetes/monitoring.yaml` to match your requirements:
- Slack webhooks
- Email notifications
- PagerDuty integration

### 2. Performance Tuning

Monitor initial performance and adjust:

```bash
# Scale API pods
kubectl scale deployment yemen-market-api --replicas=5 -n yemen-market-v2

# Adjust resource limits
kubectl edit deployment yemen-market-api -n yemen-market-v2
```

### 3. Security Hardening

- [ ] Enable network policies
- [ ] Configure pod security policies
- [ ] Set up RBAC for team access
- [ ] Enable audit logging
- [ ] Configure secrets rotation

### 4. Documentation

Update your internal documentation with:
- API endpoint URL
- Monitoring dashboard links
- Runbook for common issues
- Contact information

## Troubleshooting

### Common Issues

#### 1. Pods not starting
```bash
# Check pod logs
kubectl logs -f deployment/yemen-market-api -n yemen-market-v2

# Describe pod for events
kubectl describe pod <pod-name> -n yemen-market-v2
```

#### 2. Database connection issues
```bash
# Test database connection
kubectl exec -it statefulset/postgres -n yemen-market-v2 -- psql -U yemen_market

# Check secrets
kubectl get secret yemen-market-secrets -n yemen-market-v2 -o yaml
```

#### 3. Ingress not working
```bash
# Check ingress controller
kubectl get pods -n ingress-nginx

# Check ingress status
kubectl describe ingress yemen-market-ingress -n yemen-market-v2
```

### Emergency Procedures

#### Rollback Deployment
```bash
# Rollback to previous version
kubectl rollout undo deployment/yemen-market-api -n yemen-market-v2

# Check rollout history
kubectl rollout history deployment/yemen-market-api -n yemen-market-v2
```

#### Database Recovery
```bash
# List available backups
aws s3 ls s3://yemen-market-backups/postgres-backups/

# Restore from backup
kubectl exec -it statefulset/postgres -n yemen-market-v2 -- \
  psql -U yemen_market -c "DROP DATABASE yemen_market_v2"
  
# Download and restore backup
aws s3 cp s3://yemen-market-backups/postgres-backups/backup.sql.gz .
gunzip backup.sql.gz
kubectl exec -i statefulset/postgres -n yemen-market-v2 -- \
  psql -U yemen_market < backup.sql
```

## Maintenance

### Regular Tasks

**Daily**:
- Check monitoring dashboards
- Review error logs
- Verify backup completion

**Weekly**:
- Review performance metrics
- Check for security updates
- Test backup restoration

**Monthly**:
- Update dependencies
- Review and optimize queries
- Clean up old logs and data

### Updating the Application

```bash
# Update to new version
kubectl set image deployment/yemen-market-api \
  api=your-registry/yemen-market-v2:v2.1.0 \
  -n yemen-market-v2

# Monitor rollout
kubectl rollout status deployment/yemen-market-api -n yemen-market-v2
```

## Support

For issues or questions:
- Check logs and monitoring first
- Review troubleshooting section
- Contact: <EMAIL>

Remember to keep this guide updated as the deployment evolves!