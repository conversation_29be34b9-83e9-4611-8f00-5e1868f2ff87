"""Pytest configuration and fixtures."""

import asyncio
from datetime import datetime
from typing import Async<PERSON>enerator, Generator
from uuid import uuid4

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import (
    Commodity,
    Coordinates,
    MarketId,
    MarketType,
    Price,
)
from src.infrastructure.persistence.unit_of_work import PostgresUnitOfWork
from src.shared.container import Container


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_container() -> Container:
    """Create test container with in-memory services."""
    container = Container()
    
    # Configure for testing
    container.config.database.url.from_value("postgresql://test/test_db")
    container.config.cache.type.from_value("memory")
    container.config.events.type.from_value("inmemory")
    
    return container


@pytest.fixture
def sample_market() -> Market:
    """Create a sample market for testing."""
    return Market(
        market_id=MarketId("TEST_MARKET"),
        name="Test Market",
        coordinates=Coordinates(latitude=15.0, longitude=44.0),
        market_type=MarketType.RETAIL,
        governorate="Test Governorate",
        district="Test District",
        active_since=datetime(2020, 1, 1)
    )


@pytest.fixture
def sample_commodity() -> Commodity:
    """Create a sample commodity for testing."""
    return Commodity(
        code="TEST_COMMODITY",
        name="Test Commodity",
        category="test",
        standard_unit="kg"
    )


@pytest.fixture
def sample_price_observation(sample_market, sample_commodity) -> PriceObservation:
    """Create a sample price observation for testing."""
    return PriceObservation(
        market_id=sample_market.market_id,
        commodity=sample_commodity,
        price=Price(amount=100.0, currency="YER", unit="kg"),
        observed_date=datetime(2023, 6, 1),
        source="TEST",
        quality="standard",
        observations_count=5
    )


@pytest_asyncio.fixture
async def mock_uow(mocker):
    """Create a mock unit of work."""
    uow = mocker.AsyncMock(spec=PostgresUnitOfWork)
    uow.__aenter__.return_value = uow
    uow.__aexit__.return_value = None
    
    # Mock repositories
    uow.markets = mocker.AsyncMock()
    uow.prices = mocker.AsyncMock()
    
    return uow