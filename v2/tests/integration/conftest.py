"""Integration test configuration and fixtures."""

import asyncio
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Async<PERSON>enerator, Dict, Generator
from uuid import uuid4

import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine

from src.infrastructure.persistence.database import init_database
from src.interfaces.api.rest.app import create_app
from src.shared.container import Container


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def test_database_url() -> str:
    """Create test database URL."""
    # Use separate test database
    base_url = os.getenv("DATABASE_URL", "postgresql://localhost/yemen_market")
    test_db_name = f"test_yemen_market_{uuid4().hex[:8]}"
    
    # Create test database
    engine = create_async_engine(base_url.replace("/yemen_market", "/postgres"))
    async with engine.connect() as conn:
        await conn.execute(f"CREATE DATABASE {test_db_name}")
    await engine.dispose()
    
    test_url = base_url.replace("/yemen_market", f"/{test_db_name}")
    
    yield test_url
    
    # Drop test database
    engine = create_async_engine(base_url.replace("/yemen_market", "/postgres"))
    async with engine.connect() as conn:
        # Terminate connections
        await conn.execute(
            f"SELECT pg_terminate_backend(pid) FROM pg_stat_activity "
            f"WHERE datname = '{test_db_name}' AND pid <> pg_backend_pid()"
        )
        await conn.execute(f"DROP DATABASE {test_db_name}")
    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def test_container(test_database_url: str) -> Container:
    """Create test container with test database."""
    container = Container()
    
    # Configure for testing
    container.config.database.url.from_value(test_database_url)
    container.config.cache.type.from_value("memory")
    container.config.events.type.from_value("inmemory")
    container.config.api.environment.from_value("test")
    
    # Initialize database
    await init_database(test_database_url)
    
    return container


@pytest_asyncio.fixture(scope="function")
async def test_client(test_container: Container) -> AsyncGenerator[AsyncClient, None]:
    """Create test HTTP client."""
    app = create_app(test_container)
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def auth_headers() -> Dict[str, str]:
    """Create authentication headers for testing."""
    # In real implementation, would generate test JWT token
    return {
        "Authorization": "Bearer test-token-12345",
        "X-API-Key": "test-api-key"
    }


@pytest.fixture
def sample_market_id() -> str:
    """Provide sample market ID."""
    return "SANAA_CENTRAL"


@pytest.fixture
def sample_commodity_code() -> str:
    """Provide sample commodity code."""
    return "WHEAT_FLOUR"


@pytest_asyncio.fixture
async def setup_test_markets(test_container: Container) -> None:
    """Set up test markets in database."""
    from src.core.domain.market.entities import Market
    from src.core.domain.market.value_objects import (
        MarketId, Coordinates, MarketType
    )
    
    markets = [
        Market(
            market_id=MarketId("SANAA_CENTRAL"),
            name="Sana'a Central Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        ),
        Market(
            market_id=MarketId("ADEN_PORT"),
            name="Aden Port Market",
            coordinates=Coordinates(12.7855, 45.0187),
            market_type=MarketType.PORT,
            governorate="Aden",
            district="Port Area",
            active_since=datetime(2020, 1, 1)
        ),
        Market(
            market_id=MarketId("TAIZ_MAIN"),
            name="Taiz Main Market",
            coordinates=Coordinates(13.5795, 44.0178),
            market_type=MarketType.RETAIL,
            governorate="Taiz",
            district="City Center",
            active_since=datetime(2020, 1, 1)
        )
    ]
    
    async with test_container.unit_of_work() as uow:
        for market in markets:
            await uow.markets.save(market)
        await uow.commit()


@pytest_asyncio.fixture
async def setup_test_commodities(test_container: Container) -> None:
    """Set up test commodities in database."""
    from src.core.domain.market.value_objects import Commodity
    
    # Commodities are typically loaded from configuration
    # This would be handled by the commodity repository


@pytest.fixture
def sample_wfp_file(tmp_path: Path) -> Path:
    """Create sample WFP data file."""
    csv_content = """Date,Market,Commodity,Price,Currency,Unit
2023-06-01,Sana'a Central Market,Wheat Flour,1200,YER,kg
2023-06-01,Sana'a Central Market,Rice (Imported),1800,YER,kg
2023-06-01,Aden Port Market,Wheat Flour,1150,YER,kg
2023-06-01,Aden Port Market,Rice (Imported),1750,YER,kg
2023-06-02,Sana'a Central Market,Wheat Flour,1210,YER,kg
2023-06-02,Sana'a Central Market,Rice (Imported),1810,YER,kg
"""
    
    file_path = tmp_path / "wfp_sample.csv"
    file_path.write_text(csv_content)
    return file_path


@pytest_asyncio.fixture
async def setup_price_history(test_container: Container, setup_test_markets) -> None:
    """Set up price history for testing."""
    from src.core.domain.market.entities import PriceObservation
    from src.core.domain.market.value_objects import (
        MarketId, Commodity, Price
    )
    
    commodity = Commodity(
        code="WHEAT_FLOUR",
        name="Wheat Flour",
        category="cereal",
        standard_unit="kg"
    )
    
    # Create 30 days of price history
    base_date = datetime.utcnow().date() - timedelta(days=30)
    observations = []
    
    for i in range(30):
        date = base_date + timedelta(days=i)
        # Add some variation
        base_price = 1000 + (i * 2) + (10 if i % 7 == 0 else 0)
        
        observations.append(
            PriceObservation(
                market_id=MarketId("SANAA_CENTRAL"),
                commodity=commodity,
                price=Price(
                    amount=base_price,
                    currency="YER",
                    unit="kg"
                ),
                observed_date=date,
                source="TEST",
                quality="standard",
                observations_count=5
            )
        )
    
    async with test_container.unit_of_work() as uow:
        await uow.prices.save_batch(observations)
        await uow.commit()


@pytest_asyncio.fixture
async def setup_monthly_data(test_container: Container, setup_test_markets) -> None:
    """Set up monthly data for report testing."""
    from src.core.domain.market.entities import PriceObservation
    from src.core.domain.market.value_objects import (
        MarketId, Commodity, Price
    )
    
    commodities = [
        Commodity(code="WHEAT_FLOUR", name="Wheat Flour", category="cereal", standard_unit="kg"),
        Commodity(code="RICE_IMPORTED", name="Rice (Imported)", category="cereal", standard_unit="kg"),
        Commodity(code="FUEL_DIESEL", name="Fuel (Diesel)", category="fuel", standard_unit="liter")
    ]
    
    markets = ["SANAA_CENTRAL", "ADEN_PORT", "TAIZ_MAIN"]
    
    # Create data for June 2023
    observations = []
    for day in range(1, 31):
        for market_id in markets:
            for commodity in commodities:
                # Different base prices per commodity
                base_prices = {
                    "WHEAT_FLOUR": 1200,
                    "RICE_IMPORTED": 1800,
                    "FUEL_DIESEL": 1500
                }
                
                price = base_prices[commodity.code] + (day * 5)
                
                observations.append(
                    PriceObservation(
                        market_id=MarketId(market_id),
                        commodity=commodity,
                        price=Price(
                            amount=price,
                            currency="YER",
                            unit=commodity.standard_unit
                        ),
                        observed_date=datetime(2023, 6, day).date(),
                        source="TEST",
                        quality="standard",
                        observations_count=10
                    )
                )
    
    async with test_container.unit_of_work() as uow:
        # Save in batches
        batch_size = 100
        for i in range(0, len(observations), batch_size):
            batch = observations[i:i + batch_size]
            await uow.prices.save_batch(batch)
        await uow.commit()


@pytest_asyncio.fixture
async def setup_training_data(test_container: Container, setup_test_markets) -> None:
    """Set up training data for ML workflows."""
    # This would create several years of historical data
    # Including prices, conflict events, and other features
    # For brevity, using the monthly data setup
    await setup_monthly_data(test_container, setup_test_markets)


@pytest.fixture
def mock_internal_error(monkeypatch):
    """Mock to trigger internal server errors for testing."""
    async def raise_error(*args, **kwargs):
        raise RuntimeError("Simulated internal error")
    
    # Would patch specific methods to raise errors
    # when certain test values are used
    return raise_error