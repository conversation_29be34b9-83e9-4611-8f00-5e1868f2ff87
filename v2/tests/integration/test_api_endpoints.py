"""Integration tests for API endpoints."""

import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, List

import pytest
from httpx import AsyncClient

from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import (
    Commodity,
    Coordinates,
    MarketId,
    MarketType,
    Price,
)


class TestMarketEndpoints:
    """Test market API endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_market(self, test_client: AsyncClient, auth_headers: Dict):
        """Test creating a new market."""
        market_data = {
            "market_id": "NEW_MARKET",
            "name": "New Test Market",
            "coordinates": {"latitude": 15.5, "longitude": 44.5},
            "market_type": "retail",
            "governorate": "Test Gov",
            "district": "Test District"
        }
        
        response = await test_client.post(
            "/api/v1/markets",
            json=market_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["market_id"] == "NEW_MARKET"
        assert data["name"] == "New Test Market"
    
    @pytest.mark.asyncio
    async def test_get_market(self, test_client: AsyncClient, sample_market_id: str):
        """Test retrieving a market."""
        response = await test_client.get(f"/api/v1/markets/{sample_market_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["market_id"] == sample_market_id
        assert "coordinates" in data
    
    @pytest.mark.asyncio
    async def test_list_markets(self, test_client: AsyncClient):
        """Test listing markets with filters."""
        response = await test_client.get(
            "/api/v1/markets",
            params={
                "market_type": "retail",
                "governorate": "Test Gov",
                "active": True,
                "limit": 10
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "has_next" in data
        
        # All returned markets should match filters
        for market in data["items"]:
            assert market["market_type"] == "retail"
            assert market["governorate"] == "Test Gov"
    
    @pytest.mark.asyncio
    async def test_market_not_found(self, test_client: AsyncClient):
        """Test retrieving non-existent market."""
        response = await test_client.get("/api/v1/markets/NONEXISTENT")
        
        assert response.status_code == 404
        assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_invalid_market_data(self, test_client: AsyncClient, auth_headers: Dict):
        """Test creating market with invalid data."""
        invalid_data = {
            "market_id": "",  # Empty ID
            "name": "Test",
            "coordinates": {"latitude": 200, "longitude": 44},  # Invalid latitude
            "market_type": "invalid_type"
        }
        
        response = await test_client.post(
            "/api/v1/markets",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
        errors = response.json()["detail"]
        assert any(e["loc"] == ["body", "market_id"] for e in errors)
        assert any(e["loc"] == ["body", "coordinates", "latitude"] for e in errors)


class TestPriceEndpoints:
    """Test price API endpoints."""
    
    @pytest.mark.asyncio
    async def test_submit_price(self, test_client: AsyncClient, auth_headers: Dict,
                               sample_market_id: str, sample_commodity_code: str):
        """Test submitting a price observation."""
        price_data = {
            "market_id": sample_market_id,
            "commodity_code": sample_commodity_code,
            "price": {
                "amount": "150.50",
                "currency": "YER",
                "unit": "kg"
            },
            "observed_date": "2023-06-15",
            "quality": "standard",
            "source": "WFP"
        }
        
        response = await test_client.post(
            "/api/v1/prices",
            json=price_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["market_id"] == sample_market_id
        assert data["commodity_code"] == sample_commodity_code
        assert float(data["price"]["amount"]) == 150.50
    
    @pytest.mark.asyncio
    async def test_get_prices(self, test_client: AsyncClient,
                             sample_market_id: str, sample_commodity_code: str):
        """Test retrieving prices with filters."""
        response = await test_client.get(
            "/api/v1/prices",
            params={
                "market_id": sample_market_id,
                "commodity_code": sample_commodity_code,
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "limit": 50
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        
        # All prices should match filters
        for price in data["items"]:
            assert price["market_id"] == sample_market_id
            assert price["commodity_code"] == sample_commodity_code
    
    @pytest.mark.asyncio
    async def test_aggregate_prices(self, test_client: AsyncClient,
                                   sample_commodity_code: str):
        """Test price aggregation endpoint."""
        response = await test_client.get(
            f"/api/v1/prices/aggregate",
            params={
                "commodity_code": sample_commodity_code,
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "aggregation": "monthly",
                "group_by": ["governorate"]
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "aggregations" in data
        
        # Check aggregation structure
        for agg in data["aggregations"]:
            assert "governorate" in agg
            assert "period" in agg
            assert "avg_price" in agg
            assert "min_price" in agg
            assert "max_price" in agg
            assert "observation_count" in agg
    
    @pytest.mark.asyncio
    async def test_batch_price_submission(self, test_client: AsyncClient,
                                         auth_headers: Dict,
                                         sample_market_id: str,
                                         sample_commodity_code: str):
        """Test submitting multiple prices at once."""
        prices_data = {
            "prices": [
                {
                    "market_id": sample_market_id,
                    "commodity_code": sample_commodity_code,
                    "price": {"amount": "100", "currency": "YER", "unit": "kg"},
                    "observed_date": "2023-06-01"
                },
                {
                    "market_id": sample_market_id,
                    "commodity_code": sample_commodity_code,
                    "price": {"amount": "105", "currency": "YER", "unit": "kg"},
                    "observed_date": "2023-06-02"
                }
            ]
        }
        
        response = await test_client.post(
            "/api/v1/prices/batch",
            json=prices_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["created"] == 2
        assert "ids" in data


class TestAnalysisEndpoints:
    """Test analysis API endpoints."""
    
    @pytest.mark.asyncio
    async def test_price_transmission_analysis(self, test_client: AsyncClient,
                                              auth_headers: Dict):
        """Test price transmission analysis endpoint."""
        analysis_params = {
            "source_market": "SANAA_CENTRAL",
            "target_market": "ADEN_PORT",
            "commodity_code": "WHEAT_FLOUR",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31"
        }
        
        response = await test_client.post(
            "/api/v1/analysis/price-transmission",
            json=analysis_params,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "correlation" in data
        assert "beta_coefficient" in data
        assert "adjustment_speed" in data
        assert "half_life_days" in data
    
    @pytest.mark.asyncio
    async def test_market_integration_analysis(self, test_client: AsyncClient,
                                              auth_headers: Dict):
        """Test market integration analysis endpoint."""
        analysis_params = {
            "market_ids": ["SANAA_CENTRAL", "ADEN_PORT", "TAIZ_MAIN"],
            "commodity_codes": ["WHEAT_FLOUR", "RICE_IMPORTED"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "distance_threshold": 200.0,
            "correlation_threshold": 0.7
        }
        
        response = await test_client.post(
            "/api/v1/analysis/market-integration",
            json=analysis_params,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "integration_score" in data
        assert "integrated_pairs" in data
        assert "non_integrated_pairs" in data
        assert "recommendations" in data
    
    @pytest.mark.asyncio
    async def test_conflict_impact_analysis(self, test_client: AsyncClient,
                                           auth_headers: Dict):
        """Test conflict impact analysis endpoint."""
        analysis_params = {
            "market_ids": ["SANAA_CENTRAL"],
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "conflict_radius_km": 50.0
        }
        
        response = await test_client.post(
            "/api/v1/analysis/conflict-impact",
            json=analysis_params,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "conflict_events" in data
        assert "price_impacts" in data
        assert "market_disruptions" in data
    
    @pytest.mark.asyncio
    async def test_long_running_analysis(self, test_client: AsyncClient,
                                        auth_headers: Dict):
        """Test async job submission for long-running analysis."""
        analysis_params = {
            "analysis_type": "comprehensive_integration",
            "parameters": {
                "start_date": "2020-01-01",
                "end_date": "2023-12-31",
                "include_all_markets": True,
                "include_all_commodities": True
            }
        }
        
        # Submit job
        response = await test_client.post(
            "/api/v1/analysis/jobs",
            json=analysis_params,
            headers=auth_headers
        )
        
        assert response.status_code == 202
        data = response.json()
        assert "job_id" in data
        assert "status" in data
        assert data["status"] == "pending"
        
        job_id = data["job_id"]
        
        # Check job status
        response = await test_client.get(
            f"/api/v1/analysis/jobs/{job_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        status_data = response.json()
        assert status_data["job_id"] == job_id
        assert status_data["status"] in ["pending", "running", "completed", "failed"]


class TestHealthEndpoints:
    """Test health and monitoring endpoints."""
    
    @pytest.mark.asyncio
    async def test_health_check(self, test_client: AsyncClient):
        """Test basic health check."""
        response = await test_client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_readiness_check(self, test_client: AsyncClient):
        """Test readiness check with dependencies."""
        response = await test_client.get("/ready")
        
        assert response.status_code == 200
        data = response.json()
        assert "database" in data
        assert "cache" in data
        assert "event_bus" in data
        
        # All should be ready
        assert all(v["status"] == "ready" for v in data.values())
    
    @pytest.mark.asyncio
    async def test_metrics_endpoint(self, test_client: AsyncClient, auth_headers: Dict):
        """Test metrics endpoint."""
        response = await test_client.get("/metrics", headers=auth_headers)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/plain"
        
        # Should contain Prometheus metrics
        metrics_text = response.text
        assert "http_requests_total" in metrics_text
        assert "http_request_duration_seconds" in metrics_text


class TestErrorHandling:
    """Test API error handling."""
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, test_client: AsyncClient):
        """Test rate limiting."""
        # Make many requests quickly
        responses = []
        for _ in range(150):  # Exceed rate limit
            response = await test_client.get("/api/v1/markets")
            responses.append(response)
        
        # Should get rate limited
        assert any(r.status_code == 429 for r in responses)
        
        # Check rate limit headers
        limited_response = next(r for r in responses if r.status_code == 429)
        assert "X-RateLimit-Limit" in limited_response.headers
        assert "X-RateLimit-Remaining" in limited_response.headers
        assert "X-RateLimit-Reset" in limited_response.headers
    
    @pytest.mark.asyncio
    async def test_validation_errors(self, test_client: AsyncClient, auth_headers: Dict):
        """Test validation error responses."""
        # Invalid date format
        response = await test_client.get(
            "/api/v1/prices",
            params={"start_date": "invalid-date"}
        )
        
        assert response.status_code == 422
        errors = response.json()["detail"]
        assert any("start_date" in str(e["loc"]) for e in errors)
    
    @pytest.mark.asyncio
    async def test_authentication_required(self, test_client: AsyncClient):
        """Test endpoints requiring authentication."""
        # Try to create market without auth
        response = await test_client.post(
            "/api/v1/markets",
            json={"name": "Test"}
        )
        
        assert response.status_code == 401
        assert "detail" in response.json()
    
    @pytest.mark.asyncio
    async def test_server_error_handling(self, test_client: AsyncClient,
                                        mock_internal_error):
        """Test internal server error handling."""
        # Trigger an internal error
        response = await test_client.get("/api/v1/markets/TRIGGER_ERROR")
        
        assert response.status_code == 500
        data = response.json()
        assert "detail" in data
        assert "request_id" in data  # For error tracking