"""Unit tests for domain services."""

from datetime import datetime
from decimal import Decimal

import numpy as np
import pytest

from src.core.domain.market.services import (
    MarketIntegrationService,
    PriceTransmissionService,
    TransmissionMetrics,
)
from src.core.domain.market.value_objects import MarketId, MarketPair
from src.core.domain.shared.exceptions import BusinessRuleViolation


class TestPriceTransmissionService:
    """Test price transmission service."""
    
    @pytest.fixture
    def service(self):
        """Create price transmission service."""
        return PriceTransmissionService()
    
    @pytest.fixture
    def price_series(self, sample_commodity):
        """Create sample price series."""
        from src.core.domain.market.entities import PriceObservation
        from src.core.domain.market.value_objects import Price
        
        dates = pd.date_range("2023-01-01", periods=30, freq="D")
        
        # Create correlated price series
        np.random.seed(42)
        base_prices = 100 + np.random.randn(30) * 10
        
        source_prices = []
        target_prices = []
        
        for i, date in enumerate(dates):
            # Source market prices
            source_prices.append(
                PriceObservation(
                    market_id=MarketId("SOURCE"),
                    commodity=sample_commodity,
                    price=Price(
                        amount=Decimal(str(base_prices[i])),
                        currency="YER",
                        unit="kg"
                    ),
                    observed_date=date,
                    source="TEST"
                )
            )
            
            # Target market prices (correlated with noise)
            target_price = base_prices[i] * 1.05 + np.random.randn() * 5
            target_prices.append(
                PriceObservation(
                    market_id=MarketId("TARGET"),
                    commodity=sample_commodity,
                    price=Price(
                        amount=Decimal(str(target_price)),
                        currency="YER",
                        unit="kg"
                    ),
                    observed_date=date,
                    source="TEST"
                )
            )
        
        return source_prices, target_prices
    
    def test_transmission_calculation(self, service, price_series):
        """Test price transmission calculation."""
        source_prices, target_prices = price_series
        
        market_pair = MarketPair(
            source=MarketId("SOURCE"),
            target=MarketId("TARGET"),
            distance_km=100.0
        )
        
        metrics = service.calculate_transmission(
            source_prices=source_prices,
            target_prices=target_prices,
            market_pair=market_pair
        )
        
        assert isinstance(metrics, TransmissionMetrics)
        assert 0.5 < metrics.correlation < 1.0  # Should be positively correlated
        assert metrics.beta_coefficient > 0  # Positive transmission
        assert 0 < metrics.adjustment_speed < 1  # Valid adjustment speed
        assert metrics.half_life_days > 0  # Valid half-life
    
    def test_insufficient_data(self, service, sample_commodity):
        """Test handling of insufficient data."""
        from src.core.domain.market.entities import PriceObservation
        from src.core.domain.market.value_objects import Price
        
        # Only 5 observations (need at least 10)
        prices = []
        for i in range(5):
            prices.append(
                PriceObservation(
                    market_id=MarketId("TEST"),
                    commodity=sample_commodity,
                    price=Price(amount=100, currency="YER", unit="kg"),
                    observed_date=datetime(2023, 1, i + 1),
                    source="TEST"
                )
            )
        
        market_pair = MarketPair(
            source=MarketId("SOURCE"),
            target=MarketId("TARGET")
        )
        
        with pytest.raises(BusinessRuleViolation, match="Insufficient data"):
            service.calculate_transmission(prices, prices, market_pair)
    
    def test_different_commodities(self, service, sample_commodity):
        """Test validation of same commodity requirement."""
        from src.core.domain.market.entities import PriceObservation
        from src.core.domain.market.value_objects import Price, Commodity
        
        # Different commodities
        price1 = PriceObservation(
            market_id=MarketId("M1"),
            commodity=sample_commodity,
            price=Price(amount=100, currency="YER", unit="kg"),
            observed_date=datetime(2023, 1, 1),
            source="TEST"
        )
        
        price2 = PriceObservation(
            market_id=MarketId("M2"),
            commodity=Commodity(
                code="OTHER",
                name="Other",
                category="test",
                standard_unit="kg"
            ),
            price=Price(amount=100, currency="YER", unit="kg"),
            observed_date=datetime(2023, 1, 1),
            source="TEST"
        )
        
        market_pair = MarketPair(
            source=MarketId("M1"),
            target=MarketId("M2")
        )
        
        with pytest.raises(BusinessRuleViolation, match="same commodity"):
            service.calculate_transmission([price1], [price2], market_pair)
    
    def test_structural_break_detection(self, service):
        """Test identification of structural breaks."""
        # Create transmission metrics with a break
        metrics_before = TransmissionMetrics(
            market_pair=MarketPair(MarketId("A"), MarketId("B")),
            commodity=None,
            period_start=datetime(2023, 1, 1),
            period_end=datetime(2023, 3, 31),
            correlation=0.8,
            beta_coefficient=0.9,
            adjustment_speed=0.3,
            half_life_days=2.3
        )
        
        metrics_after = TransmissionMetrics(
            market_pair=MarketPair(MarketId("A"), MarketId("B")),
            commodity=None,
            period_start=datetime(2023, 4, 1),
            period_end=datetime(2023, 6, 30),
            correlation=0.2,  # Significant drop
            beta_coefficient=0.3,  # Significant drop
            adjustment_speed=0.1,
            half_life_days=6.9
        )
        
        breaks = service.identify_transmission_breaks(
            [metrics_before, metrics_after],
            threshold=0.5
        )
        
        assert len(breaks) == 1
        assert breaks[0] == datetime(2023, 4, 1)


import pandas as pd


class TestMarketIntegrationService:
    """Test market integration service."""
    
    @pytest.fixture
    def service(self):
        """Create market integration service."""
        transmission_service = PriceTransmissionService()
        return MarketIntegrationService(transmission_service)
    
    def test_integration_analysis(self, service, sample_market):
        """Test market integration analysis."""
        from src.core.domain.market.entities import Market
        
        # Create multiple markets
        markets = [
            sample_market,
            Market(
                market_id=MarketId("MARKET2"),
                name="Market 2",
                coordinates=Coordinates(15.5, 44.5),
                market_type=MarketType.WHOLESALE,
                governorate="Test",
                district="Test2",
                active_since=datetime(2020, 1, 1)
            ),
            Market(
                market_id=MarketId("MARKET3"),
                name="Market 3",
                coordinates=Coordinates(16.0, 45.0),
                market_type=MarketType.RETAIL,
                governorate="Test",
                district="Test3",
                active_since=datetime(2020, 1, 1)
            )
        ]
        
        # Create price observations
        price_observations = {}
        for market in markets:
            # Would create actual price observations here
            price_observations[market.market_id] = []
        
        # Run analysis
        metrics = service.analyze_integration(
            markets=markets,
            price_observations=price_observations,
            distance_threshold=200.0,
            correlation_threshold=0.7
        )
        
        assert metrics.integration_score >= 0
        assert metrics.integration_score <= 1
        assert isinstance(metrics.integrated_pairs, list)
        assert isinstance(metrics.non_integrated_pairs, list)