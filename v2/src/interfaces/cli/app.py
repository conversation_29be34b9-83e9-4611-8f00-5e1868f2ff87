"""CLI application using Typer."""

from datetime import datetime
from pathlib import Path
from typing import List, Optional

import typer
from rich.console import Console
from rich.table import Table

from ...shared.container import Container


app = typer.Typer(
    name="yemen-market",
    help="Yemen Market Integration CLI - Analyze market prices and integration"
)
console = Console()


def get_container() -> Container:
    """Get configured container."""
    container = Container()
    # Configure from environment or defaults
    container.config.database.url.from_value("postgresql://localhost/yemen_market")
    container.config.cache.type.from_value("memory")
    container.config.events.type.from_value("inmemory")
    return container


@app.command()
def analyze(
    markets: List[str] = typer.Argument(..., help="Market IDs to analyze"),
    commodities: List[str] = typer.Argument(..., help="Commodity codes to analyze"),
    start_date: str = typer.Option("2023-01-01", help="Start date (YYYY-MM-DD)"),
    end_date: str = typer.Option("2023-12-31", help="End date (YYYY-MM-DD)"),
    output: Optional[Path] = typer.Option(None, help="Output file path")
):
    """Run market integration analysis."""
    console.print(f"[bold blue]Starting analysis...[/bold blue]")
    console.print(f"Markets: {', '.join(markets)}")
    console.print(f"Commodities: {', '.join(commodities)}")
    console.print(f"Period: {start_date} to {end_date}")
    
    # Convert dates
    start = datetime.fromisoformat(start_date)
    end = datetime.fromisoformat(end_date)
    
    # Get container and handler
    container = get_container()
    handler = container.analyze_market_integration_handler()
    
    # Create command
    from ...application.commands.analyze_market_integration import (
        AnalyzeMarketIntegrationCommand
    )
    
    command = AnalyzeMarketIntegrationCommand(
        start_date=start,
        end_date=end,
        market_ids=markets,
        commodity_ids=commodities,
        analysis_config={},
        user_id="cli_user"
    )
    
    try:
        # Run analysis
        with console.status("Running analysis..."):
            import asyncio
            result = asyncio.run(handler.handle(command))
        
        console.print(f"[bold green]Analysis started![/bold green]")
        console.print(f"Analysis ID: {result.analysis_id}")
        console.print(f"Status: {result.status}")
        console.print(f"Message: {result.message}")
        
        if output:
            # Save result info to file
            output.write_text(f"Analysis ID: {result.analysis_id}\\n")
            console.print(f"Result saved to: {output}")
            
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        raise typer.Exit(1)


@app.command()
def list_markets(
    governorate: Optional[str] = typer.Option(None, help="Filter by governorate"),
    market_type: Optional[str] = typer.Option(None, help="Filter by type")
):
    """List available markets."""
    container = get_container()
    
    with console.status("Fetching markets..."):
        import asyncio
        
        async def fetch_markets():
            async with container.unit_of_work() as uow:
                if governorate:
                    return await uow.markets.find_by_governorate(governorate)
                else:
                    # Get all active markets
                    return await uow.markets.find_active_at(datetime.utcnow())
        
        markets = asyncio.run(fetch_markets())
    
    # Create table
    table = Table(title="Yemen Markets")
    table.add_column("Market ID", style="cyan")
    table.add_column("Name", style="magenta")
    table.add_column("Governorate", style="green")
    table.add_column("District")
    table.add_column("Type", style="yellow")
    
    # Filter by type if specified
    if market_type:
        markets = [m for m in markets if m.market_type.value == market_type]
    
    # Add rows
    for market in markets:
        table.add_row(
            market.market_id.value,
            market.name,
            market.governorate,
            market.district,
            market.market_type.value
        )
    
    console.print(table)
    console.print(f"\\nTotal markets: {len(markets)}")


@app.command()
def price_stats(
    market: str = typer.Argument(..., help="Market ID"),
    commodity: str = typer.Argument(..., help="Commodity code"),
    start_date: str = typer.Option("2023-01-01", help="Start date"),
    end_date: str = typer.Option("2023-12-31", help="End date")
):
    """Get price statistics for a market-commodity pair."""
    container = get_container()
    
    # Convert dates
    start = datetime.fromisoformat(start_date)
    end = datetime.fromisoformat(end_date)
    
    with console.status("Calculating statistics..."):
        import asyncio
        from ...core.domain.market.value_objects import MarketId, Commodity
        
        async def get_stats():
            async with container.unit_of_work() as uow:
                commodity_obj = Commodity(
                    code=commodity,
                    name=commodity,
                    category="food",
                    standard_unit="kg"
                )
                
                prices = await uow.prices.find_by_market_and_commodity(
                    MarketId(market),
                    commodity_obj,
                    start,
                    end
                )
                
                return prices
        
        prices = asyncio.run(get_stats())
    
    if not prices:
        console.print("[bold red]No price data found[/bold red]")
        return
    
    # Calculate statistics
    import statistics
    price_values = [float(p.price.amount) for p in prices]
    
    # Create stats table
    table = Table(title=f"Price Statistics: {market} - {commodity}")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Period", f"{start_date} to {end_date}")
    table.add_row("Observations", str(len(prices)))
    table.add_row("Mean Price", f"{statistics.mean(price_values):.2f}")
    table.add_row("Median Price", f"{statistics.median(price_values):.2f}")
    table.add_row("Std Deviation", f"{statistics.stdev(price_values):.2f}" if len(price_values) > 1 else "N/A")
    table.add_row("Min Price", f"{min(price_values):.2f}")
    table.add_row("Max Price", f"{max(price_values):.2f}")
    table.add_row("Currency", prices[0].price.currency)
    table.add_row("Unit", prices[0].price.unit)
    
    console.print(table)


@app.command()
def download_data(
    source: str = typer.Argument(..., help="Data source (hdx, wfp, acled)"),
    output_dir: Path = typer.Option(Path("./data"), help="Output directory")
):
    """Download data from external sources."""
    console.print(f"[bold blue]Downloading from {source}...[/bold blue]")
    
    container = get_container()
    output_dir.mkdir(parents=True, exist_ok=True)
    
    import asyncio
    
    async def download():
        if source == "hdx":
            client = container.hdx_client()
            datasets = await client.get_yemen_datasets()
            console.print(f"Found {len(datasets)} Yemen datasets on HDX")
            
            # Show first few
            for i, dataset in enumerate(datasets[:5]):
                console.print(f"{i+1}. {dataset['title']}")
                
        elif source == "wfp":
            client = container.wfp_client()
            data = await client.get_market_prices()
            console.print(f"Downloaded {len(data)} price records from WFP")
            
        elif source == "acled":
            client = container.acled_client()
            events = await client.get_conflict_events()
            console.print(f"Downloaded {len(events)} conflict events from ACLED")
            
        else:
            console.print(f"[bold red]Unknown source: {source}[/bold red]")
    
    asyncio.run(download())


if __name__ == "__main__":
    app()