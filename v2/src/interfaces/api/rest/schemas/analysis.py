"""Analysis API schemas."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class AnalysisRequest(BaseModel):
    """Request schema for starting an analysis."""
    
    start_date: datetime = Field(..., description="Analysis start date")
    end_date: datetime = Field(..., description="Analysis end date")
    market_ids: List[str] = Field(..., min_items=1, description="List of market IDs to analyze")
    commodity_ids: List[str] = Field(..., min_items=1, description="List of commodity codes to analyze")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Analysis configuration")
    
    @validator("end_date")
    def end_after_start(cls, v, values):
        """Validate end date is after start date."""
        if "start_date" in values and v <= values["start_date"]:
            raise ValueError("end_date must be after start_date")
        return v
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "start_date": "2023-01-01T00:00:00",
                "end_date": "2023-12-31T23:59:59",
                "market_ids": ["SANAA_CENTRAL", "ADEN_MAIN", "TAIZ_WHOLESALE"],
                "commodity_ids": ["WHEAT_FLOUR", "RICE_IMPORTED", "SUGAR"],
                "config": {
                    "run_pooled_panel": True,
                    "run_vecm": True,
                    "distance_threshold": 500,
                    "correlation_threshold": 0.7
                }
            }
        }


class AnalysisResponse(BaseModel):
    """Response schema for analysis creation."""
    
    analysis_id: str = Field(..., description="Unique analysis identifier")
    status: str = Field(..., description="Analysis status")
    message: str = Field(..., description="Status message")
    estimated_completion_time: Optional[datetime] = Field(None, description="Estimated completion time")
    links: Dict[str, str] = Field(..., description="Related resource links")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
                "status": "started",
                "message": "Analysis started for 3 markets and 3 commodities",
                "estimated_completion_time": "2023-01-01T00:05:00",
                "links": {
                    "self": "/api/v2/analysis/550e8400-e29b-41d4-a716-446655440000",
                    "results": "/api/v2/analysis/550e8400-e29b-41d4-a716-446655440000/results",
                    "cancel": "/api/v2/analysis/550e8400-e29b-41d4-a716-446655440000/cancel"
                }
            }
        }


class AnalysisStatusResponse(BaseModel):
    """Response schema for analysis status."""
    
    analysis_id: str = Field(..., description="Unique analysis identifier")
    status: str = Field(..., enum=["pending", "running", "completed", "failed", "cancelled"])
    progress: int = Field(..., ge=0, le=100, description="Progress percentage")
    current_phase: Optional[str] = Field(None, description="Current analysis phase")
    started_at: datetime = Field(..., description="Analysis start time")
    completed_at: Optional[datetime] = Field(None, description="Analysis completion time")
    updated_at: datetime = Field(..., description="Last update time")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
                "status": "running",
                "progress": 75,
                "current_phase": "econometric_modeling",
                "started_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:03:45"
            }
        }