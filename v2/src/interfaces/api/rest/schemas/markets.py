"""Market API schemas."""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class MarketResponse(BaseModel):
    """Response schema for market data."""
    
    market_id: str = Field(..., description="Unique market identifier")
    name: str = Field(..., description="Market name")
    governorate: str = Field(..., description="Governorate name")
    district: str = Field(..., description="District name")
    market_type: str = Field(..., description="Market type (wholesale, retail, etc.)")
    latitude: float = Field(..., description="Latitude coordinate")
    longitude: float = Field(..., description="Longitude coordinate")
    active_since: datetime = Field(..., description="Date market became active")
    active_until: Optional[datetime] = Field(None, description="Date market became inactive")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "market_id": "SANAA_CENTRAL",
                "name": "Sana'a Central Market",
                "governorate": "Sana'a",
                "district": "Old City",
                "market_type": "wholesale",
                "latitude": 15.3694,
                "longitude": 44.1910,
                "active_since": "2019-01-01T00:00:00",
                "active_until": None
            }
        }


class MarketListResponse(BaseModel):
    """Response schema for market list."""
    
    markets: List[MarketResponse] = Field(..., description="List of markets")
    total: int = Field(..., description="Total number of markets matching filters")
    skip: int = Field(..., description="Number of markets skipped")
    limit: int = Field(..., description="Maximum markets returned")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "markets": [
                    {
                        "market_id": "SANAA_CENTRAL",
                        "name": "Sana'a Central Market",
                        "governorate": "Sana'a",
                        "district": "Old City",
                        "market_type": "wholesale",
                        "latitude": 15.3694,
                        "longitude": 44.1910,
                        "active_since": "2019-01-01T00:00:00",
                        "active_until": None
                    }
                ],
                "total": 42,
                "skip": 0,
                "limit": 20
            }
        }