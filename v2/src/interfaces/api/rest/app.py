"""FastAPI application factory."""

from contextlib import asynccontextmanager
from typing import Any, Dict

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse

from ....shared.container import Container
from .middleware import (
    ErrorHandlerMiddleware,
    LoggingMiddleware,
    RequestIdMiddleware,
)
from .routes import analysis, health, markets, prices


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    container = app.state.container
    
    # Start event bus if async
    if hasattr(container.event_bus(), 'start'):
        await container.event_bus().start()
    
    yield
    
    # Shutdown
    # Stop event bus if async
    if hasattr(container.event_bus(), 'stop'):
        await container.event_bus().stop()
    
    # Close cache connections
    if hasattr(container.cache(), 'close'):
        await container.cache().close()
    
    # Close database connections
    if hasattr(container.unit_of_work(), 'close'):
        await container.unit_of_work().close()


def create_app(container: Container) -> FastAPI:
    """Create FastAPI application with dependency injection."""
    
    # Create app with metadata
    app = FastAPI(
        title="Yemen Market Integration API",
        description="Modern API for Yemen market price analysis and integration research",
        version="2.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        lifespan=lifespan
    )
    
    # Store container in app state
    app.state.container = container
    
    # Add middleware (order matters - applied in reverse)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure based on environment
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(ErrorHandlerMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(RequestIdMiddleware)
    
    # Include routers
    app.include_router(health.router, tags=["health"])
    app.include_router(
        markets.router,
        prefix="/api/v2/markets",
        tags=["markets"]
    )
    app.include_router(
        prices.router,
        prefix="/api/v2/prices",
        tags=["prices"]
    )
    app.include_router(
        analysis.router,
        prefix="/api/v2/analysis",
        tags=["analysis"]
    )
    
    # Exception handlers
    @app.exception_handler(404)
    async def not_found_handler(request, exc):
        return JSONResponse(
            status_code=404,
            content={"detail": "Resource not found"}
        )
    
    @app.exception_handler(500)
    async def internal_error_handler(request, exc):
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )
    
    return app