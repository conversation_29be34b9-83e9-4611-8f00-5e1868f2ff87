"""Error handling middleware."""

import sys
import traceback
from typing import Callable

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from .....core.domain.shared.exceptions import (
    DomainException,
    EntityNotFoundException,
    ValidationException,
)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware for handling exceptions and converting to proper responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and handle exceptions."""
        try:
            response = await call_next(request)
            return response
            
        except EntityNotFoundException as e:
            return JSONResponse(
                status_code=404,
                content={
                    "error": "not_found",
                    "message": str(e),
                    "entity_type": e.entity_type,
                    "entity_id": e.entity_id
                }
            )
            
        except ValidationException as e:
            return JSONResponse(
                status_code=422,
                content={
                    "error": "validation_error",
                    "message": str(e),
                    "code": e.code
                }
            )
            
        except DomainException as e:
            return JSONResponse(
                status_code=400,
                content={
                    "error": "domain_error",
                    "message": str(e),
                    "code": e.code
                }
            )
            
        except Exception as e:
            # Log the full traceback
            tb = "".join(traceback.format_exception(*sys.exc_info()))
            print(f"Unhandled exception: {tb}")
            
            # Return generic error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "internal_server_error",
                    "message": "An unexpected error occurred"
                }
            )