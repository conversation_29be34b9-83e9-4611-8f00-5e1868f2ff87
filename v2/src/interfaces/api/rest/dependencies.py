"""FastAPI dependencies for dependency injection."""

from typing import Optional

from fastapi import Depends, HTTPException, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ....shared.container import Container


# Global container instance (set by app factory)
_container: Optional[Container] = None


def set_container(container: Container) -> None:
    """Set the global container instance."""
    global _container
    _container = container


def get_container() -> Container:
    """Get the dependency injection container."""
    if _container is None:
        raise RuntimeError("Container not initialized")
    return _container


# Security
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    api_key: Optional[str] = Header(None, alias="X-API-Key")
) -> str:
    """Get current user from auth token or API key."""
    # For now, accept any valid bearer token or API key
    if credentials and credentials.credentials:
        # In production, validate JWT token
        return "authenticated_user"
    elif api_key:
        # In production, validate API key
        return f"api_key_user_{api_key[:8]}"
    else:
        # For development, allow anonymous access
        return "anonymous"


def require_auth(user: str = Depends(get_current_user)) -> str:
    """Require authentication."""
    if user == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return user


# Command and Query handlers
def get_command_handler(command_name: str):
    """Get command handler from container."""
    def _get_handler(container: Container = Depends(get_container)):
        handler_name = f"{command_name}_handler"
        if not hasattr(container, handler_name):
            raise ValueError(f"Unknown command handler: {command_name}")
        return getattr(container, handler_name)()
    return _get_handler


def get_query_handler(query_name: str):
    """Get query handler from container."""
    def _get_handler(container: Container = Depends(get_container)):
        handler_name = f"{query_name}_handler"
        if not hasattr(container, handler_name):
            raise ValueError(f"Unknown query handler: {query_name}")
        return getattr(container, handler_name)()
    return _get_handler