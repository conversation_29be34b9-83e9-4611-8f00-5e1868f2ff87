"""Market endpoints."""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query

from .....core.domain.market.value_objects import MarketId
from ..dependencies import get_container, get_current_user
from ..schemas.markets import MarketResponse, MarketListResponse


router = APIRouter()


@router.get("/", response_model=MarketListResponse)
async def list_markets(
    governorate: Optional[str] = Query(None, description="Filter by governorate"),
    district: Optional[str] = Query(None, description="Filter by district"),
    market_type: Optional[str] = Query(None, description="Filter by market type"),
    active_at: Optional[datetime] = Query(None, description="Filter markets active at date"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    container = Depends(get_container)
) -> MarketListResponse:
    """List markets with optional filters."""
    
    async with container.unit_of_work() as uow:
        # Apply filters
        if governorate:
            markets = await uow.markets.find_by_governorate(governorate)
        elif active_at:
            markets = await uow.markets.find_active_at(active_at)
        else:
            # For now, get all markets by getting a sample governorate
            # In production, would have a find_all method
            markets = []
        
        # Apply additional filters
        if district:
            markets = [m for m in markets if m.district == district]
        if market_type:
            markets = [m for m in markets if m.market_type.value == market_type]
        
        # Pagination
        total = len(markets)
        markets = markets[skip:skip + limit]
        
        # Convert to response format
        market_responses = [
            MarketResponse(
                market_id=m.market_id.value,
                name=m.name,
                governorate=m.governorate,
                district=m.district,
                market_type=m.market_type.value,
                latitude=m.coordinates.latitude,
                longitude=m.coordinates.longitude,
                active_since=m.active_since,
                active_until=m.active_until
            )
            for m in markets
        ]
        
        return MarketListResponse(
            markets=market_responses,
            total=total,
            skip=skip,
            limit=limit
        )


@router.get("/{market_id}", response_model=MarketResponse)
async def get_market(
    market_id: str,
    container = Depends(get_container)
) -> MarketResponse:
    """Get a specific market by ID."""
    
    async with container.unit_of_work() as uow:
        market = await uow.markets.find_by_id(MarketId(market_id))
        
        if not market:
            raise HTTPException(
                status_code=404,
                detail=f"Market {market_id} not found"
            )
        
        return MarketResponse(
            market_id=market.market_id.value,
            name=market.name,
            governorate=market.governorate,
            district=market.district,
            market_type=market.market_type.value,
            latitude=market.coordinates.latitude,
            longitude=market.coordinates.longitude,
            active_since=market.active_since,
            active_until=market.active_until
        )


@router.get("/{market_id}/accessibility")
async def get_market_accessibility(
    market_id: str,
    k_nearest: int = Query(5, ge=1, le=20, description="Number of nearest markets"),
    container = Depends(get_container)
):
    """Get accessibility metrics for a market."""
    
    async with container.unit_of_work() as uow:
        market = await uow.markets.find_by_id(MarketId(market_id))
        
        if not market:
            raise HTTPException(
                status_code=404,
                detail=f"Market {market_id} not found"
            )
        
        # Get all markets for accessibility calculation
        all_markets = await uow.markets.find_active_at(datetime.utcnow())
        
        # Calculate accessibility using spatial analysis service
        from .....core.domain.geography.services import SpatialAnalysisService
        spatial_service = SpatialAnalysisService()
        
        # Get governorates for control zone analysis
        # In production, would fetch from database
        governorates = []
        
        accessibility = spatial_service.calculate_market_accessibility(
            market=market,
            all_markets=all_markets,
            governorates=governorates,
            k_nearest=k_nearest
        )
        
        return {
            "market_id": market_id,
            "accessibility_metrics": {
                "isolation_index": accessibility.isolation_index,
                "connectivity_score": accessibility.connectivity_score,
                "average_distance_to_others": accessibility.average_distance_to_others,
                "nearest_markets": [
                    {
                        "market_id": mid.value,
                        "distance_km": distance
                    }
                    for mid, distance in accessibility.nearest_markets
                ],
                "control_zone_accessibility": accessibility.control_zone_accessibility
            }
        }