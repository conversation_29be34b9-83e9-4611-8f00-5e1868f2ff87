"""Health check endpoints."""

from datetime import datetime
from typing import Dict

from fastapi import APIRouter, Depends

from ..dependencies import get_container


router = APIRouter()


@router.get("/health")
async def health_check() -> Dict[str, str]:
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "yemen-market-integration",
        "version": "2.0.0"
    }


@router.get("/health/ready")
async def readiness_check(container = Depends(get_container)) -> Dict[str, any]:
    """Readiness check - verifies all dependencies are available."""
    checks = {
        "database": False,
        "cache": False,
        "event_bus": False
    }
    
    # Check database
    try:
        async with container.unit_of_work() as uow:
            # Simple query to verify connection
            await uow.markets.find_by_governorate("test")
        checks["database"] = True
    except Exception:
        pass
    
    # Check cache
    try:
        await container.cache().set("health_check", "ok", ttl=1)
        value = await container.cache().get("health_check")
        checks["cache"] = value == "ok"
    except Exception:
        pass
    
    # Check event bus
    try:
        if hasattr(container.event_bus(), '_running'):
            checks["event_bus"] = container.event_bus()._running
        else:
            checks["event_bus"] = True
    except Exception:
        pass
    
    # Overall status
    all_healthy = all(checks.values())
    
    return {
        "status": "ready" if all_healthy else "not_ready",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": checks
    }