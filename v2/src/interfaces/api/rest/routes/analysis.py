"""Analysis endpoints."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query
from pydantic import BaseModel

from .....application.commands.analyze_market_integration import (
    AnalyzeMarketIntegrationCommand,
)
from ..dependencies import get_command_handler, get_container, get_current_user
from ..schemas.analysis import (
    AnalysisRequest,
    AnalysisResponse,
    AnalysisStatusResponse,
)


router = APIRouter()


@router.post("/market-integration", response_model=AnalysisResponse)
async def start_market_integration_analysis(
    request: AnalysisRequest,
    background_tasks: BackgroundTasks,
    handler = Depends(lambda: get_command_handler("analyze_market_integration")),
    current_user: str = Depends(get_current_user)
) -> AnalysisResponse:
    """Start a new market integration analysis."""
    
    # Create command
    command = AnalyzeMarketIntegrationCommand(
        start_date=request.start_date,
        end_date=request.end_date,
        market_ids=request.market_ids,
        commodity_ids=request.commodity_ids,
        analysis_config=request.config or {},
        user_id=current_user
    )
    
    try:
        # Execute command
        result = await handler.handle(command)
        
        return AnalysisResponse(
            analysis_id=str(result.analysis_id),
            status=result.status,
            message=result.message,
            estimated_completion_time=result.estimated_completion_time,
            links={
                "self": f"/api/v2/analysis/{result.analysis_id}",
                "results": f"/api/v2/analysis/{result.analysis_id}/results",
                "cancel": f"/api/v2/analysis/{result.analysis_id}/cancel"
            }
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{analysis_id}", response_model=AnalysisStatusResponse)
async def get_analysis_status(
    analysis_id: UUID,
    container = Depends(get_container),
    current_user: str = Depends(get_current_user)
) -> AnalysisStatusResponse:
    """Get the status of an analysis."""
    
    # Check cache for analysis status
    cache_key = f"analysis:status:{analysis_id}"
    cached_status = await container.cache().get(cache_key)
    
    if cached_status:
        return AnalysisStatusResponse(**cached_status)
    
    # Query database
    async with container.unit_of_work() as uow:
        # This would query an analysis tracking table
        # For now, return a mock response
        return AnalysisStatusResponse(
            analysis_id=str(analysis_id),
            status="running",
            progress=50,
            current_phase="transmission_analysis",
            started_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )


@router.get("/{analysis_id}/results")
async def get_analysis_results(
    analysis_id: UUID,
    format: str = Query("json", enum=["json", "csv", "excel"]),
    container = Depends(get_container),
    current_user: str = Depends(get_current_user)
):
    """Get analysis results in specified format."""
    
    # Check cache for results
    cache_key = f"analysis:{analysis_id}"
    results = await container.cache().get(cache_key)
    
    if not results:
        raise HTTPException(
            status_code=404,
            detail=f"Results not found for analysis {analysis_id}"
        )
    
    # Format results based on requested format
    if format == "json":
        return results
    elif format == "csv":
        # Convert to CSV format
        # This would use pandas or similar
        raise HTTPException(
            status_code=501,
            detail="CSV format not yet implemented"
        )
    elif format == "excel":
        # Convert to Excel format
        raise HTTPException(
            status_code=501,
            detail="Excel format not yet implemented"
        )


@router.post("/{analysis_id}/cancel")
async def cancel_analysis(
    analysis_id: UUID,
    container = Depends(get_container),
    current_user: str = Depends(get_current_user)
) -> dict:
    """Cancel a running analysis."""
    
    # This would interact with the analysis orchestrator to cancel
    # For now, return success
    return {
        "status": "cancelled",
        "analysis_id": str(analysis_id),
        "message": "Analysis cancelled successfully"
    }


@router.get("/", response_model=List[AnalysisStatusResponse])
async def list_analyses(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = Query(None, enum=["pending", "running", "completed", "failed", "cancelled"]),
    container = Depends(get_container),
    current_user: str = Depends(get_current_user)
) -> List[AnalysisStatusResponse]:
    """List analyses for the current user."""
    
    # This would query the database
    # For now, return empty list
    return []