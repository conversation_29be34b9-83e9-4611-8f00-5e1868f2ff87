"""Principal Component Analysis model for dimensionality reduction.

This model uses PCA to reduce the dimensionality of price data
and identify the main patterns of variation.
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

from core.models.interfaces import Model, ModelSpecification
from infrastructure.logging import Logger

logger = Logger(__name__)


class PCAModel(Model):
    """PCA model for analyzing price variation patterns.
    
    This model uses Principal Component Analysis to:
    - Reduce dimensionality of price data
    - Identify main sources of price variation
    - Create composite price indices
    - Visualize market relationships
    """
    
    def __init__(self, specification: ModelSpecification):
        """Initialize PCA model.
        
        Args:
            specification: Model specification with PCA parameters
        """
        self.specification = specification
        self.n_components = specification.features.get('n_components', None)
        self.variance_threshold = specification.features.get('variance_threshold', 0.95)
        self.standardize = specification.features.get('standardize', True)
        self.center = specification.features.get('center', True)
        
    @property
    def name(self) -> str:
        """Model name for identification."""
        n_comp_str = self.n_components if self.n_components else 'auto'
        return f"PCAModel_{n_comp_str}components"
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate that data meets PCA requirements.
        
        Args:
            data: Input data to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check for numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 2:
            errors.append(f"Insufficient numeric variables for PCA: {len(numeric_cols)} < 2")
        
        # Check for missing values
        if data[numeric_cols].isnull().any().any():
            missing_pct = data[numeric_cols].isnull().sum().sum() / (len(data) * len(numeric_cols)) * 100
            errors.append(f"Missing values detected ({missing_pct:.1f}% of data). "
                         "Consider imputation before PCA.")
        
        # Check sample size
        n_obs = len(data)
        n_vars = len(numeric_cols)
        if n_obs < n_vars:
            errors.append(f"More variables ({n_vars}) than observations ({n_obs}). "
                         "PCA may be unstable.")
        
        # Check for zero variance columns
        zero_var_cols = [col for col in numeric_cols if data[col].var() == 0]
        if zero_var_cols:
            errors.append(f"Zero variance columns found: {zero_var_cols}")
        
        return errors
    
    def fit_pca(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Fit PCA model to the data.
        
        Args:
            data: DataFrame with variables for PCA
            
        Returns:
            Dictionary with PCA results
        """
        logger.info(f"Fitting PCA model with n_components={self.n_components}")
        
        # Prepare data
        numeric_data = data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        # Standardize if requested
        if self.standardize or self.center:
            scaler = StandardScaler(with_std=self.standardize, with_mean=self.center)
            scaled_data = scaler.fit_transform(clean_data)
            data_for_pca = pd.DataFrame(
                scaled_data,
                index=clean_data.index,
                columns=clean_data.columns
            )
        else:
            data_for_pca = clean_data
            scaler = None
        
        # Determine number of components
        if self.n_components is None:
            # Use variance threshold
            pca_temp = PCA()
            pca_temp.fit(data_for_pca)
            cumsum_var = np.cumsum(pca_temp.explained_variance_ratio_)
            n_components = np.argmax(cumsum_var >= self.variance_threshold) + 1
            self.n_components = min(n_components, len(data_for_pca.columns))
            logger.info(f"Selected {self.n_components} components to explain "
                       f"{self.variance_threshold*100}% of variance")
        
        # Fit PCA
        pca = PCA(n_components=self.n_components, random_state=42)
        scores = pca.fit_transform(data_for_pca)
        
        # Create results dictionary
        results = {
            'n_components': self.n_components,
            'loadings': pd.DataFrame(
                pca.components_.T,
                index=data_for_pca.columns,
                columns=[f'PC{i+1}' for i in range(self.n_components)]
            ),
            'scores': pd.DataFrame(
                scores,
                index=data_for_pca.index,
                columns=[f'PC{i+1}' for i in range(self.n_components)]
            ),
            'explained_variance': pd.Series(
                pca.explained_variance_,
                index=[f'PC{i+1}' for i in range(self.n_components)]
            ),
            'explained_variance_ratio': pd.Series(
                pca.explained_variance_ratio_,
                index=[f'PC{i+1}' for i in range(self.n_components)]
            ),
            'cumulative_variance_ratio': pd.Series(
                np.cumsum(pca.explained_variance_ratio_),
                index=[f'PC{i+1}' for i in range(self.n_components)]
            ),
            'standardized': self.standardize,
            'centered': self.center,
            'scaler': scaler,
            'pca_object': pca
        }
        
        # Interpret components
        results['component_interpretation'] = self._interpret_components(results['loadings'])
        
        # Calculate quality metrics
        results['quality_metrics'] = self._calculate_quality_metrics(
            data_for_pca, results
        )
        
        # Identify outliers
        results['outliers'] = self._identify_outliers(results['scores'])
        
        return results
    
    def _interpret_components(self, loadings: pd.DataFrame) -> Dict[str, str]:
        """Interpret principal components based on loadings.
        
        Args:
            loadings: Component loadings matrix
            
        Returns:
            Dictionary with component interpretations
        """
        interpretations = {}
        
        for pc in loadings.columns:
            # Find variables with high absolute loadings
            abs_loadings = abs(loadings[pc])
            high_loadings = loadings[pc][abs_loadings > 0.3].sort_values(
                ascending=False, key=abs
            )
            
            if len(high_loadings) > 0:
                # Create interpretation
                positive_vars = high_loadings[high_loadings > 0].head(3).index.tolist()
                negative_vars = high_loadings[high_loadings < 0].head(3).index.tolist()
                
                interpretation = ""
                if positive_vars:
                    interpretation += f"High: {', '.join(positive_vars)}"
                if negative_vars:
                    if interpretation:
                        interpretation += " | "
                    interpretation += f"Low: {', '.join(negative_vars)}"
                
                # Add general interpretation
                if pc == 'PC1':
                    interpretation += " (Overall price level)"
                elif pc == 'PC2':
                    interpretation += " (Price contrast)"
                
                interpretations[pc] = interpretation
            else:
                interpretations[pc] = "No clear interpretation"
        
        return interpretations
    
    def _calculate_quality_metrics(self, original_data: pd.DataFrame,
                                 results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate PCA quality metrics.
        
        Args:
            original_data: Original data before PCA
            results: PCA results
            
        Returns:
            Dictionary with quality metrics
        """
        metrics = {}
        
        # Reconstruction error
        pca = results['pca_object']
        reconstructed = pca.inverse_transform(results['scores'])
        reconstruction_error = np.mean((original_data - reconstructed) ** 2)
        metrics['reconstruction_error'] = float(reconstruction_error)
        
        # Contribution of variables to components
        contributions = {}
        for pc in results['loadings'].columns:
            contrib = (results['loadings'][pc] ** 2 * 
                      results['explained_variance_ratio'][pc])
            contributions[pc] = contrib.sort_values(ascending=False)
        metrics['variable_contributions'] = contributions
        
        # Kaiser criterion
        if 'explained_variance' in results:
            n_kaiser = sum(results['explained_variance'] > 1)
            metrics['kaiser_criterion'] = n_kaiser
        
        # Scree test suggestion
        if len(results['explained_variance']) > 1:
            var_diffs = np.diff(results['explained_variance'])
            elbow = np.argmax(var_diffs > -0.1 * var_diffs[0]) + 1
            metrics['scree_suggestion'] = elbow
        
        return metrics
    
    def _identify_outliers(self, scores: pd.DataFrame,
                          n_std: float = 3) -> Dict[str, Any]:
        """Identify outliers in PC space.
        
        Args:
            scores: PC scores
            n_std: Number of standard deviations for outlier threshold
            
        Returns:
            Dictionary with outlier information
        """
        outliers = {}
        
        # Check each component
        for pc in scores.columns:
            pc_mean = scores[pc].mean()
            pc_std = scores[pc].std()
            
            outlier_mask = abs(scores[pc] - pc_mean) > n_std * pc_std
            outlier_indices = scores.index[outlier_mask].tolist()
            
            if outlier_indices:
                outliers[pc] = {
                    'indices': outlier_indices,
                    'values': scores.loc[outlier_indices, pc].to_dict()
                }
        
        # Multivariate outliers using Mahalanobis distance
        if len(scores.columns) > 1:
            from scipy.spatial.distance import mahalanobis
            
            mean = scores.mean().values
            cov_matrix = scores.cov().values
            
            try:
                inv_cov = np.linalg.inv(cov_matrix)
                distances = []
                
                for idx, row in scores.iterrows():
                    dist = mahalanobis(row.values, mean, inv_cov)
                    distances.append(dist)
                
                distances = pd.Series(distances, index=scores.index)
                
                # Chi-square threshold
                from scipy import stats
                threshold = stats.chi2.ppf(0.975, len(scores.columns))
                multivariate_outliers = distances[distances > threshold]
                
                if len(multivariate_outliers) > 0:
                    outliers['multivariate'] = {
                        'indices': multivariate_outliers.index.tolist(),
                        'distances': multivariate_outliers.to_dict()
                    }
            except np.linalg.LinAlgError:
                logger.warning("Could not calculate Mahalanobis distances (singular covariance)")
        
        return outliers
    
    def create_composite_index(self, results: Dict[str, Any],
                             weights: Optional[Dict[str, float]] = None) -> pd.Series:
        """Create composite index from principal components.
        
        Args:
            results: PCA results dictionary
            weights: Optional weights for components (defaults to variance explained)
            
        Returns:
            Composite index series
        """
        scores = results['scores']
        
        if weights is None:
            # Use variance explained as weights
            weights = results['explained_variance_ratio'].to_dict()
        
        # Normalize weights
        total_weight = sum(weights.values())
        norm_weights = {k: v/total_weight for k, v in weights.items()}
        
        # Calculate weighted sum
        composite = pd.Series(0, index=scores.index)
        for pc, weight in norm_weights.items():
            if pc in scores.columns:
                composite += weight * scores[pc]
        
        # Standardize to 0-100 scale
        composite_min = composite.min()
        composite_max = composite.max()
        if composite_max > composite_min:
            composite = 100 * (composite - composite_min) / (composite_max - composite_min)
        
        return composite
    
    def plot_results(self, results: Dict[str, Any],
                    save_path: Optional[str] = None) -> None:
        """Plot PCA results.
        
        Args:
            results: PCA results dictionary
            save_path: Optional path to save plots
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 1. Scree plot
        ax = axes[0, 0]
        var_exp = results['explained_variance_ratio']
        ax.bar(range(1, len(var_exp) + 1), var_exp)
        ax.plot(range(1, len(var_exp) + 1), 
                results['cumulative_variance_ratio'], 
                'r-o', label='Cumulative')
        ax.set_xlabel('Principal Component')
        ax.set_ylabel('Variance Explained')
        ax.set_title('Scree Plot')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 2. Biplot (first two components)
        if len(results['loadings'].columns) >= 2:
            ax = axes[0, 1]
            loadings = results['loadings'][['PC1', 'PC2']]
            
            # Plot variables as vectors
            for var in loadings.index:
                ax.arrow(0, 0, loadings.loc[var, 'PC1'], loadings.loc[var, 'PC2'],
                        head_width=0.05, head_length=0.05, fc='red', ec='red')
                ax.text(loadings.loc[var, 'PC1'] * 1.1, 
                       loadings.loc[var, 'PC2'] * 1.1, 
                       var, fontsize=8)
            
            ax.set_xlabel('PC1')
            ax.set_ylabel('PC2')
            ax.set_title('Variable Loadings Biplot')
            ax.grid(True, alpha=0.3)
            ax.axhline(y=0, color='k', linewidth=0.5)
            ax.axvline(x=0, color='k', linewidth=0.5)
        
        # 3. Contribution plot
        ax = axes[1, 0]
        contrib = pd.DataFrame({
            pc: abs(results['loadings'][pc]) * results['explained_variance_ratio'][pc]
            for pc in results['loadings'].columns
        })
        contrib.plot(kind='bar', stacked=True, ax=ax)
        ax.set_xlabel('Variables')
        ax.set_ylabel('Contribution')
        ax.set_title('Variable Contributions to PCs')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # 4. Score plot (first two components)
        if len(results['scores'].columns) >= 2:
            ax = axes[1, 1]
            ax.scatter(results['scores']['PC1'], results['scores']['PC2'], alpha=0.6)
            ax.set_xlabel('PC1')
            ax.set_ylabel('PC2')
            ax.set_title('Observation Scores')
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"PCA plots saved to {save_path}")
        
        plt.close()