"""Factor model for identifying common drivers of price movements.

This model extracts latent factors that drive price co-movements
across markets and commodities.
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.decomposition import FactorAnalysis
from sklearn.preprocessing import StandardScaler

from core.models.interfaces import Model, ModelSpecification
from infrastructure.logging import Logger

logger = Logger(__name__)


class FactorModel(Model):
    """Factor model for analyzing common price drivers.
    
    This model uses factor analysis to identify latent factors that
    explain co-movements in prices across markets, helping to understand
    the underlying structure of market integration.
    """
    
    def __init__(self, specification: ModelSpecification):
        """Initialize factor model.
        
        Args:
            specification: Model specification with factor analysis parameters
        """
        self.specification = specification
        self.n_factors = specification.features.get('n_factors', 3)
        self.rotation = specification.features.get('rotation', 'varimax')
        self.standardize = specification.features.get('standardize', True)
        self.min_variance_explained = specification.features.get('min_variance_explained', 0.7)
        
    @property
    def name(self) -> str:
        """Model name for identification."""
        return f"FactorModel_{self.n_factors}factors"
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate that data meets factor analysis requirements.
        
        Args:
            data: Input data to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check for numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 3:
            errors.append(f"Insufficient numeric variables for factor analysis: {len(numeric_cols)} < 3")
        
        # Check for missing values
        if data[numeric_cols].isnull().any().any():
            missing_pct = data[numeric_cols].isnull().sum().sum() / (len(data) * len(numeric_cols)) * 100
            errors.append(f"Missing values detected ({missing_pct:.1f}% of data)")
        
        # Check sample size
        n_obs = len(data)
        n_vars = len(numeric_cols)
        if n_obs < 5 * n_vars:
            errors.append(f"Sample size ({n_obs}) may be insufficient for {n_vars} variables "
                         f"(recommended: {5 * n_vars})")
        
        # Check for constant columns
        constant_cols = [col for col in numeric_cols if data[col].nunique() == 1]
        if constant_cols:
            errors.append(f"Constant columns found: {constant_cols}")
        
        return errors
    
    def extract_factors(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Extract latent factors from the data.
        
        Args:
            data: DataFrame with variables for factor analysis
            
        Returns:
            Dictionary with factor analysis results
        """
        logger.info(f"Extracting {self.n_factors} factors using {self.rotation} rotation")
        
        # Prepare data
        numeric_data = data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        # Standardize if requested
        if self.standardize:
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(clean_data)
            data_for_fa = pd.DataFrame(
                scaled_data, 
                index=clean_data.index,
                columns=clean_data.columns
            )
        else:
            data_for_fa = clean_data
        
        # Determine optimal number of factors if not specified
        if self.n_factors == 'auto':
            self.n_factors = self._determine_n_factors(data_for_fa)
            logger.info(f"Automatically selected {self.n_factors} factors")
        
        # Fit factor model
        fa = FactorAnalysis(
            n_components=self.n_factors,
            rotation=self.rotation if self.rotation != 'none' else None,
            random_state=42
        )
        
        factor_scores = fa.fit_transform(data_for_fa)
        
        # Create results dictionary
        results = {
            'n_factors': self.n_factors,
            'factor_loadings': pd.DataFrame(
                fa.components_.T,
                index=data_for_fa.columns,
                columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
            ),
            'factor_scores': pd.DataFrame(
                factor_scores,
                index=data_for_fa.index,
                columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
            ),
            'communalities': pd.Series(
                1 - fa.noise_variance_,
                index=data_for_fa.columns
            ),
            'variance_explained': self._calculate_variance_explained(fa, data_for_fa),
            'rotation': self.rotation,
            'standardized': self.standardize
        }
        
        # Interpret factors
        results['factor_interpretation'] = self._interpret_factors(results['factor_loadings'])
        
        # Test factor adequacy
        results['adequacy_tests'] = self._test_factor_adequacy(data_for_fa, results)
        
        return results
    
    def _determine_n_factors(self, data: pd.DataFrame) -> int:
        """Determine optimal number of factors using eigenvalue criterion.
        
        Args:
            data: Prepared data for factor analysis
            
        Returns:
            Optimal number of factors
        """
        # Calculate correlation matrix eigenvalues
        corr_matrix = data.corr()
        eigenvalues, _ = np.linalg.eig(corr_matrix)
        eigenvalues = np.sort(eigenvalues)[::-1]
        
        # Kaiser criterion: eigenvalues > 1
        n_factors_kaiser = np.sum(eigenvalues > 1)
        
        # Scree test: find elbow
        diffs = np.diff(eigenvalues)
        n_factors_scree = np.argmax(diffs < np.mean(diffs)) + 1
        
        # Use minimum of the two
        n_factors = min(n_factors_kaiser, n_factors_scree, len(data.columns) - 1)
        
        logger.info(f"Kaiser criterion suggests {n_factors_kaiser} factors, "
                   f"scree test suggests {n_factors_scree} factors")
        
        return max(1, n_factors)
    
    def _calculate_variance_explained(self, fa: FactorAnalysis, 
                                    data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate variance explained by factors.
        
        Args:
            fa: Fitted FactorAnalysis object
            data: Original data
            
        Returns:
            Dictionary with variance explained metrics
        """
        # Total variance (sum of variances of standardized variables)
        total_variance = len(data.columns)  # For standardized data
        
        # Variance explained by each factor
        factor_variances = np.sum(fa.components_**2, axis=1)
        
        # Proportion of variance explained
        prop_variance = factor_variances / total_variance
        cumulative_variance = np.cumsum(prop_variance)
        
        return {
            'by_factor': pd.Series(
                prop_variance,
                index=[f'Factor_{i+1}' for i in range(len(prop_variance))]
            ),
            'cumulative': pd.Series(
                cumulative_variance,
                index=[f'Factor_{i+1}' for i in range(len(cumulative_variance))]
            ),
            'total': float(cumulative_variance[-1])
        }
    
    def _interpret_factors(self, loadings: pd.DataFrame) -> Dict[str, str]:
        """Interpret factors based on loadings.
        
        Args:
            loadings: Factor loadings matrix
            
        Returns:
            Dictionary with factor interpretations
        """
        interpretations = {}
        
        for factor in loadings.columns:
            # Find variables with high loadings
            high_loadings = loadings[factor][abs(loadings[factor]) > 0.4].sort_values(
                ascending=False, key=abs
            )
            
            if len(high_loadings) > 0:
                # Create interpretation based on top loadings
                top_vars = high_loadings.head(3).index.tolist()
                interpretation = f"Driven by {', '.join(top_vars)}"
                
                # Add direction information
                if all(high_loadings.head(3) > 0):
                    interpretation += " (positive relationship)"
                elif all(high_loadings.head(3) < 0):
                    interpretation += " (negative relationship)"
                else:
                    interpretation += " (mixed relationship)"
                
                interpretations[factor] = interpretation
            else:
                interpretations[factor] = "No strong loadings"
        
        return interpretations
    
    def _test_factor_adequacy(self, data: pd.DataFrame, 
                             results: Dict[str, Any]) -> Dict[str, Any]:
        """Test adequacy of factor model.
        
        Args:
            data: Original data
            results: Factor analysis results
            
        Returns:
            Dictionary with test results
        """
        tests = {}
        
        # Kaiser-Meyer-Olkin (KMO) test
        tests['kmo'] = self._calculate_kmo(data)
        
        # Bartlett's test of sphericity
        tests['bartlett'] = self._bartlett_sphericity_test(data)
        
        # Check variance explained
        total_var_explained = results['variance_explained']['total']
        tests['variance_adequate'] = total_var_explained >= self.min_variance_explained
        
        # Overall adequacy
        tests['overall_adequate'] = (
            tests['kmo']['overall'] > 0.6 and
            tests['bartlett']['reject_null'] and
            tests['variance_adequate']
        )
        
        return tests
    
    def _calculate_kmo(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate Kaiser-Meyer-Olkin measure of sampling adequacy.
        
        Args:
            data: Data matrix
            
        Returns:
            Dictionary with KMO results
        """
        # Correlation matrix
        corr = data.corr().values
        
        # Partial correlation matrix
        try:
            inv_corr = np.linalg.inv(corr)
            partial_corr = -inv_corr / np.sqrt(np.outer(np.diag(inv_corr), np.diag(inv_corr)))
            np.fill_diagonal(partial_corr, 0)
        except np.linalg.LinAlgError:
            logger.warning("Singular correlation matrix, KMO cannot be calculated")
            return {'overall': np.nan, 'by_variable': {}}
        
        # KMO calculation
        corr_sq = corr**2
        partial_corr_sq = partial_corr**2
        
        # Overall KMO
        overall_kmo = np.sum(corr_sq) / (np.sum(corr_sq) + np.sum(partial_corr_sq))
        
        # Per-variable KMO
        var_kmo = {}
        for i, var in enumerate(data.columns):
            var_kmo[var] = np.sum(corr_sq[i, :]) / (
                np.sum(corr_sq[i, :]) + np.sum(partial_corr_sq[i, :])
            )
        
        return {
            'overall': overall_kmo,
            'by_variable': var_kmo,
            'adequate': overall_kmo > 0.6,
            'interpretation': self._interpret_kmo(overall_kmo)
        }
    
    def _interpret_kmo(self, kmo: float) -> str:
        """Interpret KMO value."""
        if kmo >= 0.9:
            return "Marvelous"
        elif kmo >= 0.8:
            return "Meritorious"
        elif kmo >= 0.7:
            return "Middling"
        elif kmo >= 0.6:
            return "Mediocre"
        elif kmo >= 0.5:
            return "Miserable"
        else:
            return "Unacceptable"
    
    def _bartlett_sphericity_test(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Bartlett's test of sphericity.
        
        Tests whether correlation matrix is identity matrix.
        
        Args:
            data: Data matrix
            
        Returns:
            Dictionary with test results
        """
        n_obs, n_vars = data.shape
        corr_matrix = data.corr()
        
        # Calculate test statistic
        det_corr = np.linalg.det(corr_matrix)
        if det_corr <= 0:
            logger.warning("Non-positive definite correlation matrix")
            return {
                'statistic': np.nan,
                'p_value': np.nan,
                'reject_null': False
            }
        
        chi_square = -(n_obs - 1 - (2 * n_vars + 5) / 6) * np.log(det_corr)
        
        # Degrees of freedom
        df = n_vars * (n_vars - 1) / 2
        
        # P-value
        from scipy import stats
        p_value = 1 - stats.chi2.cdf(chi_square, df)
        
        return {
            'statistic': chi_square,
            'df': df,
            'p_value': p_value,
            'reject_null': p_value < 0.05,
            'interpretation': "Variables are intercorrelated" if p_value < 0.05 
                            else "Variables may be independent"
        }