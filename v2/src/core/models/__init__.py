"""Core econometric models as first-class citizens."""

from .interfaces import Model, Estimator, EstimationResult, DiagnosticResult, ModelSpecification
from .panel import PooledPanelModel, FixedEffectsModel, TwoWayFixedEffectsModel
from .time_series import VECMModel, ThresholdVECMModel

__all__ = [
    # Interfaces
    "Model",
    "Estimator",
    "EstimationResult",
    "DiagnosticResult",
    "ModelSpecification",
    # Panel models
    "PooledPanelModel",
    "FixedEffectsModel",
    "TwoWayFixedEffectsModel",
    # Time series models
    "VECMModel",
    "ThresholdVECMModel",
]