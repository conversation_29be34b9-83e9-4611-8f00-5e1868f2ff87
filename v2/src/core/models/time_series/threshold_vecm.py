"""Threshold Vector Error Correction Model implementation."""

from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

from .vecm import VECMModel
from ..interfaces import ModelSpecification


class ThresholdVECMModel(VECMModel):
    """
    Threshold Vector Error Correction Model.
    
    Allows for regime-switching behavior in the adjustment process based on
    a threshold variable (e.g., transaction costs, conflict intensity).
    
    This is an advanced Tier 2 model for detecting non-linear market integration.
    """
    
    @property
    def name(self) -> str:
        """Model name."""
        return "Threshold Vector Error Correction Model (TVECM)"
    
    def __init__(self, specification: ModelSpecification):
        """Initialize threshold VECM model."""
        super().__init__(specification)
        
        # Threshold-specific parameters
        self.threshold_variable = specification.parameters.get("threshold_variable")
        self.n_regimes = specification.parameters.get("n_regimes", 2)
        self.threshold_values = specification.parameters.get("threshold_values", None)
        
        # Threshold search parameters
        self.trim_percent = specification.parameters.get("trim_percent", 0.15)
        self.grid_points = specification.parameters.get("grid_points", 100)
        self.bootstrap_reps = specification.parameters.get("bootstrap_reps", 1000)
        
        # Regime-specific parameters
        self.regime_specific_alpha = specification.parameters.get(
            "regime_specific_alpha", True
        )
        self.regime_specific_gamma = specification.parameters.get(
            "regime_specific_gamma", True
        )
        
        # Delay parameter
        self.threshold_delay = specification.parameters.get("threshold_delay", 1)
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate data for threshold VECM."""
        # First run standard VECM validation
        errors = super().validate_data(data)
        
        # Check threshold variable
        if self.threshold_variable:
            if self.threshold_variable not in data.columns:
                errors.append(f"Threshold variable '{self.threshold_variable}' not found")
            else:
                # Check threshold variable properties
                thresh_data = data[self.threshold_variable]
                
                # Need sufficient variation
                if thresh_data.nunique() < 10:
                    errors.append(
                        f"Insufficient variation in threshold variable "
                        f"(only {thresh_data.nunique()} unique values)"
                    )
                
                # Check for missing values
                if thresh_data.isna().any():
                    errors.append("Missing values in threshold variable")
        else:
            # If no threshold variable specified, use error correction term
            if not self.coint_rank or self.coint_rank == 0:
                errors.append(
                    "Threshold variable not specified and no cointegration "
                    "for error correction term"
                )
        
        # Check minimum observations per regime
        min_obs_per_regime = len(self.endogenous_vars) * (self.k_ar_diff or 2) + 5
        total_min_obs = min_obs_per_regime * self.n_regimes
        
        if len(data) < total_min_obs:
            errors.append(
                f"Insufficient observations for {self.n_regimes} regimes: "
                f"have {len(data)}, need at least {total_min_obs}"
            )
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for threshold VECM estimation."""
        # First apply standard VECM preparation
        prepared = super().prepare_data(data)
        
        # Add lagged threshold variable if using delay
        if self.threshold_variable and self.threshold_delay > 0:
            if self.threshold_variable in prepared.columns:
                prepared[f"{self.threshold_variable}_lag{self.threshold_delay}"] = (
                    prepared[self.threshold_variable].shift(self.threshold_delay)
                )
        
        # Create potential threshold variable if not specified
        if not self.threshold_variable:
            # Use error correction term from linear VECM as threshold
            # This is a placeholder - actual implementation would estimate ECT
            prepared["ect_threshold"] = 0.0  # Would be calculated from cointegration
        
        return prepared
    
    def get_diagnostics(self) -> List[str]:
        """Get diagnostic tests for threshold VECM."""
        # Standard VECM diagnostics
        diagnostics = super().get_diagnostics()
        
        # Add threshold-specific tests
        diagnostics.extend([
            "threshold_linearity",     # Test for threshold effects
            "regime_homogeneity",      # Test parameter equality across regimes
            "threshold_constancy",     # Test threshold stability
            "regime_classification",   # Check regime assignment
            "regime_duration"          # Analyze regime persistence
        ])
        
        return diagnostics
    
    def find_threshold_values(
        self,
        data: pd.DataFrame,
        threshold_var: pd.Series
    ) -> Tuple[List[float], float]:
        """
        Find optimal threshold values using grid search.
        
        Returns:
            Tuple of (threshold_values, sum_squared_residuals)
        """
        # Get threshold variable values
        thresh_data = threshold_var.dropna().sort_values()
        n_obs = len(thresh_data)
        
        # Determine search range (trim extreme values)
        trim_n = int(n_obs * self.trim_percent)
        search_range = thresh_data.iloc[trim_n:-trim_n]
        
        if self.n_regimes == 2:
            # Single threshold
            threshold_grid = np.linspace(
                search_range.min(),
                search_range.max(),
                self.grid_points
            )
            
            # Would implement grid search here
            # For now, return median
            return [search_range.median()], 0.0
        
        elif self.n_regimes == 3:
            # Two thresholds
            # Would implement 2D grid search
            q1 = search_range.quantile(0.33)
            q2 = search_range.quantile(0.67)
            return [q1, q2], 0.0
        
        else:
            raise ValueError(f"Unsupported number of regimes: {self.n_regimes}")
    
    def test_threshold_effects(
        self,
        data: pd.DataFrame,
        test_type: str = "sup-wald"
    ) -> Dict[str, float]:
        """
        Test for presence of threshold effects.
        
        H0: Linear model (no threshold effects)
        H1: Threshold model
        
        Args:
            data: Prepared data
            test_type: Type of test ('sup-wald', 'ave-wald', 'exp-wald')
            
        Returns:
            Dictionary with test statistic and p-value
        """
        # Would implement Hansen (1999) or similar test
        # For now, return placeholder
        return {
            "test_statistic": 15.2,
            "p_value": 0.001,
            "critical_value_10%": 10.5,
            "critical_value_5%": 12.3,
            "critical_value_1%": 16.8
        }
    
    def assign_regimes(
        self,
        data: pd.DataFrame,
        threshold_values: List[float]
    ) -> pd.Series:
        """
        Assign observations to regimes based on threshold values.
        
        Returns:
            Series with regime assignments (0, 1, ..., n_regimes-1)
        """
        # Get threshold variable (with delay if specified)
        if self.threshold_delay > 0:
            thresh_col = f"{self.threshold_variable}_lag{self.threshold_delay}"
        else:
            thresh_col = self.threshold_variable or "ect_threshold"
        
        threshold_data = data[thresh_col]
        
        # Initialize with regime 0
        regimes = pd.Series(0, index=data.index)
        
        # Assign based on thresholds
        if self.n_regimes == 2:
            regimes[threshold_data > threshold_values[0]] = 1
        
        elif self.n_regimes == 3:
            regimes[threshold_data > threshold_values[0]] = 1
            regimes[threshold_data > threshold_values[1]] = 2
        
        return regimes
    
    def calculate_regime_probabilities(
        self,
        data: pd.DataFrame,
        threshold_values: List[float],
        smooth_transition: bool = False,
        gamma: float = 10.0
    ) -> pd.DataFrame:
        """
        Calculate regime probabilities.
        
        For sharp threshold model, probabilities are 0 or 1.
        For smooth transition, uses logistic function.
        
        Args:
            data: Data with threshold variable
            threshold_values: Threshold values
            smooth_transition: Whether to use smooth transition
            gamma: Smoothness parameter for logistic transition
            
        Returns:
            DataFrame with probability of each regime
        """
        # Get threshold variable
        thresh_col = (
            f"{self.threshold_variable}_lag{self.threshold_delay}"
            if self.threshold_delay > 0
            else self.threshold_variable or "ect_threshold"
        )
        threshold_data = data[thresh_col]
        
        if not smooth_transition:
            # Sharp transition
            regimes = self.assign_regimes(data, threshold_values)
            
            # Convert to probabilities (one-hot encoding)
            prob_df = pd.DataFrame(index=data.index)
            for regime in range(self.n_regimes):
                prob_df[f"regime_{regime}_prob"] = (regimes == regime).astype(float)
            
        else:
            # Smooth transition (logistic)
            prob_df = pd.DataFrame(index=data.index)
            
            if self.n_regimes == 2:
                # Single threshold
                z = gamma * (threshold_data - threshold_values[0])
                prob_regime_1 = 1 / (1 + np.exp(-z))
                
                prob_df["regime_0_prob"] = 1 - prob_regime_1
                prob_df["regime_1_prob"] = prob_regime_1
            
            else:
                # Multiple thresholds - would implement
                raise NotImplementedError(
                    "Smooth transition not implemented for multiple thresholds"
                )
        
        return prob_df