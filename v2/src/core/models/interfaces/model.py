"""Base interface for econometric models."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import pandas as pd


@dataclass
class ModelSpecification:
    """Specification for an econometric model."""
    
    model_type: str
    dependent_variable: str
    independent_variables: List[str]
    parameters: Dict[str, Any]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        """Initialize metadata if not provided."""
        if self.metadata is None:
            self.metadata = {}


class Model(ABC):
    """Abstract base class for all econometric models."""
    
    def __init__(self, specification: ModelSpecification):
        """Initialize model with specification."""
        self.specification = specification
        self._is_fitted = False
        self._results = None
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Model name for identification."""
        pass
    
    @property
    @abstractmethod
    def required_data_structure(self) -> str:
        """Required data structure (e.g., 'panel', 'time_series', 'cross_section')."""
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """
        Validate that data meets model requirements.
        
        Returns list of validation errors (empty if valid).
        """
        pass
    
    @abstractmethod
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare data for estimation (e.g., transformations, lags).
        
        Returns prepared DataFrame.
        """
        pass
    
    @abstractmethod
    def get_diagnostics(self) -> List[str]:
        """
        Get list of diagnostic tests applicable to this model.
        
        Returns list of diagnostic test names.
        """
        pass
    
    @property
    def is_fitted(self) -> bool:
        """Check if model has been fitted."""
        return self._is_fitted
    
    @property
    def results(self) -> Optional[Any]:
        """Get estimation results if fitted."""
        return self._results
    
    def __repr__(self) -> str:
        """String representation."""
        return f"{self.name}({self.specification.model_type})"