"""Pooled panel data model implementation."""

from typing import Dict, List

import pandas as pd

from ..interfaces import Model, ModelSpecification


class PooledPanelModel(Model):
    """
    Pooled panel data model (ignores panel structure).
    
    This is Tier 1 of the three-tier approach.
    """
    
    @property
    def name(self) -> str:
        """Model name."""
        return "Pooled Panel Model"
    
    @property
    def required_data_structure(self) -> str:
        """Required data structure."""
        return "panel"
    
    def __init__(self, specification: ModelSpecification):
        """Initialize pooled panel model."""
        super().__init__(specification)
        
        # Extract panel-specific parameters
        self.entity_var = specification.parameters.get("entity_var", "market")
        self.time_var = specification.parameters.get("time_var", "date")
        self.cluster_se = specification.parameters.get("cluster_se", True)
        self.weights_var = specification.parameters.get("weights_var", None)
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate panel data structure."""
        errors = []
        
        # Check required columns
        required_cols = [
            self.entity_var,
            self.time_var,
            self.specification.dependent_variable
        ] + self.specification.independent_variables
        
        missing_cols = set(required_cols) - set(data.columns)
        if missing_cols:
            errors.append(f"Missing columns: {missing_cols}")
        
        # Check for duplicates
        if not errors:
            duplicates = data.duplicated(subset=[self.entity_var, self.time_var])
            if duplicates.any():
                errors.append(
                    f"Found {duplicates.sum()} duplicate entity-time observations"
                )
        
        # Check minimum observations
        if len(data) < 30:
            errors.append("Insufficient observations (need at least 30)")
        
        # Check for variation in dependent variable
        if self.specification.dependent_variable in data.columns:
            if data[self.specification.dependent_variable].nunique() < 2:
                errors.append("No variation in dependent variable")
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for pooled estimation."""
        # Create a copy
        prepared = data.copy()
        
        # Sort by entity and time
        prepared = prepared.sort_values([self.entity_var, self.time_var])
        
        # Add log transformations if specified
        for var in self.specification.parameters.get("log_vars", []):
            if var in prepared.columns:
                prepared[f"log_{var}"] = prepared[var].apply(lambda x: pd.np.log(x) if x > 0 else pd.np.nan)
        
        # Create lagged variables if specified
        lag_vars = self.specification.parameters.get("lag_vars", {})
        for var, lags in lag_vars.items():
            if var in prepared.columns:
                for lag in lags:
                    prepared[f"{var}_lag{lag}"] = prepared.groupby(self.entity_var)[var].shift(lag)
        
        # Handle missing values
        if self.specification.parameters.get("dropna", True):
            prepared = prepared.dropna(subset=[
                self.specification.dependent_variable
            ] + self.specification.independent_variables)
        
        return prepared
    
    def get_diagnostics(self) -> List[str]:
        """Get applicable diagnostic tests."""
        return [
            "heteroskedasticity",
            "serial_correlation",
            "cross_sectional_dependence",
            "functional_form",
            "normality"
        ]