"""Pooled panel model implementation."""

from dataclasses import dataclass
from typing import List, Optional

import pandas as pd

from ..interfaces import Model


@dataclass
class PooledPanelModel(Model):
    """Pooled OLS panel model treating all observations as independent."""
    
    dependent_var: str
    independent_vars: List[str]
    entity_var: str = "market_id"
    time_var: str = "date"
    cluster_var: Optional[str] = None
    weights_var: Optional[str] = None
    
    @property
    def name(self) -> str:
        """Get model name."""
        return f"Pooled Panel Model: {self.dependent_var} ~ {' + '.join(self.independent_vars)}"
    
    @property
    def model_type(self) -> str:
        """Get model type."""
        return "panel"
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data meets model requirements."""
        required_cols = [self.dependent_var] + self.independent_vars
        required_cols.extend([self.entity_var, self.time_var])
        
        if self.cluster_var:
            required_cols.append(self.cluster_var)
        if self.weights_var:
            required_cols.append(self.weights_var)
        
        # Check all required columns exist
        missing = set(required_cols) - set(data.columns)
        if missing:
            raise ValueError(f"Missing required columns: {missing}")
        
        # Check for sufficient observations
        if len(data) < len(self.independent_vars) + 1:
            raise ValueError(
                f"Insufficient observations ({len(data)}) for "
                f"{len(self.independent_vars)} independent variables"
            )
        
        # Check for missing values
        if data[required_cols].isnull().any().any():
            raise ValueError("Data contains missing values in required columns")
        
        # Check for variation in dependent variable
        if data[self.dependent_var].std() == 0:
            raise ValueError("No variation in dependent variable")
        
        return True
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for estimation."""
        # Validate first
        self.validate_data(data)
        
        # Create a copy to avoid modifying original
        prepared = data.copy()
        
        # Sort by entity and time
        prepared = prepared.sort_values([self.entity_var, self.time_var])
        
        # Create numeric entity codes if needed
        if prepared[self.entity_var].dtype == 'object':
            entity_codes = pd.Categorical(prepared[self.entity_var]).codes
            prepared[f'{self.entity_var}_code'] = entity_codes
        
        # Convert time to numeric if needed
        if pd.api.types.is_datetime64_any_dtype(prepared[self.time_var]):
            # Convert to days since first observation
            min_time = prepared[self.time_var].min()
            prepared[f'{self.time_var}_numeric'] = (
                prepared[self.time_var] - min_time
            ).dt.days
        
        return prepared