"""Conflict entities - core business objects for conflict events."""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

from ..market.value_objects import Coordinates
from ..shared.entities import Entity
from ..shared.exceptions import ValidationException
from .value_objects import ConflictType, ConflictIntensity, ImpactRadius


@dataclass
class ConflictEvent(Entity):
    """Represents a conflict event that may impact markets."""
    
    event_date: datetime
    location: Coordinates
    conflict_type: ConflictType
    intensity: ConflictIntensity
    fatalities: int
    actors: List[str]
    description: str
    source: str
    impact_radius: Optional[ImpactRadius] = None
    
    def __post_init__(self) -> None:
        """Initialize conflict event."""
        super().__post_init__()
        self._validate()
        
        # Set default impact radius if not provided
        if self.impact_radius is None:
            self.impact_radius = ImpactRadius.default_for_type(self.conflict_type)
    
    def _validate(self) -> None:
        """Validate conflict event."""
        if self.event_date > datetime.utcnow():
            raise ValidationException("Event date cannot be in the future")
        if self.fatalities < 0:
            raise ValidationException("Fatalities cannot be negative")
        if not self.actors:
            raise ValidationException("At least one actor must be specified")
        if not self.description:
            raise ValidationException("Description is required")
        if not self.source:
            raise ValidationException("Source is required")
        
        # Validate intensity matches fatalities
        expected_intensity = ConflictIntensity.from_fatalities(self.fatalities)
        if self.intensity != expected_intensity:
            raise ValidationException(
                f"Intensity {self.intensity.value} doesn't match fatalities count {self.fatalities}"
            )
    
    def distance_to_point(self, point: Coordinates) -> float:
        """Calculate distance from conflict to a point."""
        return self.location.distance_to(point)
    
    def get_impact_level(self, point: Coordinates) -> str:
        """Determine impact level at a given point."""
        distance = self.distance_to_point(point)
        
        if distance <= self.impact_radius.immediate_km:
            return "immediate"
        elif distance <= self.impact_radius.moderate_km:
            return "moderate"
        elif distance <= self.impact_radius.marginal_km:
            return "marginal"
        else:
            return "none"
    
    def impacts_area(self, center: Coordinates, radius_km: float) -> bool:
        """Check if conflict impacts an area defined by center and radius."""
        distance = self.distance_to_point(center)
        return distance <= (self.impact_radius.marginal_km + radius_km)