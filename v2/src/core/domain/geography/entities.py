"""Geographic entities for spatial analysis."""

from dataclasses import dataclass
from typing import List, Optional

from ..market.value_objects import Coordinates
from ..shared.entities import Entity
from ..shared.exceptions import ValidationException


@dataclass
class GeographicZone(Entity):
    """Base geographic zone entity."""
    
    name: str
    code: str
    center: Coordinates
    area_km2: float
    population: Optional[int] = None
    
    def __post_init__(self) -> None:
        """Validate geographic zone."""
        super().__post_init__()
        if not self.name:
            raise ValidationException("Zone name is required")
        if not self.code:
            raise ValidationException("Zone code is required")
        if self.area_km2 <= 0:
            raise ValidationException("Area must be positive")
        if self.population is not None and self.population < 0:
            raise ValidationException("Population cannot be negative")
    
    @property
    def population_density(self) -> Optional[float]:
        """Calculate population density per km²."""
        if self.population is None:
            return None
        return self.population / self.area_km2


@dataclass
class District(GeographicZone):
    """District entity - subdivision of governorate."""
    
    governorate_code: str
    urban_population: Optional[int] = None
    rural_population: Optional[int] = None
    
    def __post_init__(self) -> None:
        """Validate district."""
        super().__post_init__()
        if not self.governorate_code:
            raise ValidationException("Governorate code is required")
        
        # Validate population breakdown
        if self.urban_population is not None and self.urban_population < 0:
            raise ValidationException("Urban population cannot be negative")
        if self.rural_population is not None and self.rural_population < 0:
            raise ValidationException("Rural population cannot be negative")
        
        # Check consistency
        if (self.population is not None and 
            self.urban_population is not None and 
            self.rural_population is not None):
            total = self.urban_population + self.rural_population
            if abs(total - self.population) > 1:  # Allow for rounding
                raise ValidationException(
                    f"Population breakdown ({total}) doesn't match total population ({self.population})"
                )
    
    @property
    def urbanization_rate(self) -> Optional[float]:
        """Calculate urbanization rate."""
        if self.population is None or self.population == 0:
            return None
        if self.urban_population is None:
            return None
        return self.urban_population / self.population


@dataclass
class Governorate(GeographicZone):
    """Governorate entity - top-level administrative division."""
    
    capital_city: str
    districts: List[District]
    control_authority: str  # "IRG", "DFA", "Contested"
    
    def __post_init__(self) -> None:
        """Validate governorate."""
        super().__post_init__()
        if not self.capital_city:
            raise ValidationException("Capital city is required")
        if not self.districts:
            raise ValidationException("Governorate must have at least one district")
        if self.control_authority not in ["IRG", "DFA", "Contested"]:
            raise ValidationException(f"Invalid control authority: {self.control_authority}")
    
    def add_district(self, district: District) -> None:
        """Add a district to the governorate."""
        if district.governorate_code != self.code:
            raise ValidationException(
                f"District {district.code} belongs to different governorate"
            )
        if any(d.code == district.code for d in self.districts):
            raise ValidationException(f"District {district.code} already exists")
        
        self.districts.append(district)
        
        # Update governorate population if possible
        if district.population is not None:
            if self.population is None:
                self.population = district.population
            else:
                self.population += district.population
    
    def get_district(self, district_code: str) -> Optional[District]:
        """Get district by code."""
        return next((d for d in self.districts if d.code == district_code), None)
    
    @property
    def district_count(self) -> int:
        """Get number of districts."""
        return len(self.districts)
    
    def is_contested(self) -> bool:
        """Check if governorate is contested."""
        return self.control_authority == "Contested"