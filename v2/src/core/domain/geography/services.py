"""Domain services for spatial and geographic analysis."""

from dataclasses import dataclass
from typing import Dict, List, Optional, Set, Tuple

import numpy as np
from scipy.spatial import distance_matrix

from ..market.entities import Market
from ..market.value_objects import Coordinates, MarketId
from ..conflict.entities import ConflictEvent
from .entities import District, Governorate


@dataclass
class SpatialCluster:
    """Represents a spatial cluster of markets."""
    
    center: Coordinates
    markets: List[Market]
    radius_km: float
    density: float  # Markets per 100 km²


@dataclass
class MarketAccessibility:
    """Accessibility metrics for a market."""
    
    market_id: MarketId
    nearest_markets: List[Tuple[MarketId, float]]  # (market_id, distance_km)
    isolation_index: float  # 0-1, higher means more isolated
    connectivity_score: float  # 0-1, higher means better connected
    average_distance_to_others: float
    control_zone_accessibility: Dict[str, float]  # Access to different control zones


class SpatialAnalysisService:
    """Domain service for spatial analysis of markets and conflicts."""
    
    def identify_market_clusters(
        self,
        markets: List[Market],
        max_cluster_radius_km: float = 100.0,
        min_cluster_size: int = 3
    ) -> List[SpatialCluster]:
        """Identify spatial clusters of markets."""
        if len(markets) < min_cluster_size:
            return []
        
        # Create distance matrix
        coords = np.array([[m.coordinates.latitude, m.coordinates.longitude] for m in markets])
        distances = self._calculate_distance_matrix(coords)
        
        # Use DBSCAN-like approach for clustering
        clusters = []
        unassigned = set(range(len(markets)))
        
        while unassigned:
            # Start with an unassigned market
            seed_idx = unassigned.pop()
            cluster_indices = {seed_idx}
            
            # Find all markets within radius
            for idx in list(unassigned):
                # Check if market is within radius of any cluster member
                for cluster_idx in cluster_indices:
                    if distances[cluster_idx, idx] <= max_cluster_radius_km:
                        cluster_indices.add(idx)
                        unassigned.discard(idx)
                        break
            
            # Create cluster if size threshold met
            if len(cluster_indices) >= min_cluster_size:
                cluster_markets = [markets[i] for i in cluster_indices]
                cluster = self._create_spatial_cluster(cluster_markets)
                clusters.append(cluster)
            
        return clusters
    
    def calculate_market_accessibility(
        self,
        market: Market,
        all_markets: List[Market],
        governorates: List[Governorate],
        k_nearest: int = 5
    ) -> MarketAccessibility:
        """Calculate accessibility metrics for a market."""
        if not all_markets:
            raise ValueError("No markets provided for accessibility calculation")
        
        # Calculate distances to all other markets
        other_markets = [m for m in all_markets if m.market_id != market.market_id]
        if not other_markets:
            return MarketAccessibility(
                market_id=market.market_id,
                nearest_markets=[],
                isolation_index=1.0,
                connectivity_score=0.0,
                average_distance_to_others=float('inf'),
                control_zone_accessibility={}
            )
        
        # Calculate distances
        distances = []
        for other in other_markets:
            dist = market.coordinates.distance_to(other.coordinates)
            distances.append((other.market_id, dist))
        
        # Sort by distance
        distances.sort(key=lambda x: x[1])
        
        # Get k nearest markets
        nearest_markets = distances[:k_nearest]
        
        # Calculate metrics
        all_distances = [d[1] for d in distances]
        avg_distance = np.mean(all_distances)
        
        # Isolation index: normalized average distance to k nearest
        k_distances = [d[1] for d in nearest_markets]
        avg_k_distance = np.mean(k_distances) if k_distances else float('inf')
        isolation_index = min(avg_k_distance / 100.0, 1.0)  # Normalize to 0-1
        
        # Connectivity score: inverse of isolation
        connectivity_score = 1.0 - isolation_index
        
        # Control zone accessibility
        control_zones = self._calculate_control_zone_access(
            market, other_markets, governorates
        )
        
        return MarketAccessibility(
            market_id=market.market_id,
            nearest_markets=nearest_markets,
            isolation_index=isolation_index,
            connectivity_score=connectivity_score,
            average_distance_to_others=avg_distance,
            control_zone_accessibility=control_zones
        )
    
    def analyze_conflict_market_overlap(
        self,
        markets: List[Market],
        conflicts: List[ConflictEvent],
        impact_threshold_km: float = 50.0
    ) -> Dict[str, Dict]:
        """Analyze overlap between conflict zones and market locations."""
        overlap_analysis = {
            "affected_markets": [],
            "safe_markets": [],
            "conflict_density_map": {},
            "market_exposure_scores": {}
        }
        
        for market in markets:
            exposure_score = 0.0
            affecting_conflicts = []
            
            for conflict in conflicts:
                distance = conflict.distance_to_point(market.coordinates)
                if distance <= impact_threshold_km:
                    # Calculate exposure based on distance and intensity
                    distance_factor = 1.0 - (distance / impact_threshold_km)
                    intensity_factor = (
                        list(ConflictEvent.ConflictIntensity).index(conflict.intensity) + 1
                    ) / len(ConflictEvent.ConflictIntensity)
                    
                    exposure_score += distance_factor * intensity_factor
                    affecting_conflicts.append(conflict)
            
            market_info = {
                "market_id": str(market.market_id.value),
                "exposure_score": exposure_score,
                "affecting_conflicts": len(affecting_conflicts)
            }
            
            if affecting_conflicts:
                overlap_analysis["affected_markets"].append(market_info)
            else:
                overlap_analysis["safe_markets"].append(market_info)
            
            overlap_analysis["market_exposure_scores"][str(market.market_id.value)] = exposure_score
        
        # Calculate conflict density per governorate
        for market in markets:
            gov = market.governorate
            if gov not in overlap_analysis["conflict_density_map"]:
                overlap_analysis["conflict_density_map"][gov] = {
                    "total_conflicts": 0,
                    "affected_markets": 0,
                    "total_markets": 0
                }
            
            overlap_analysis["conflict_density_map"][gov]["total_markets"] += 1
            if any(m["market_id"] == str(market.market_id.value) 
                   for m in overlap_analysis["affected_markets"]):
                overlap_analysis["conflict_density_map"][gov]["affected_markets"] += 1
        
        return overlap_analysis
    
    def calculate_spatial_price_correlation(
        self,
        market_prices: Dict[MarketId, List[float]],
        market_locations: Dict[MarketId, Coordinates],
        distance_bands: List[Tuple[float, float]] = [(0, 50), (50, 100), (100, 200), (200, float('inf'))]
    ) -> Dict[str, float]:
        """Calculate price correlation by distance bands."""
        correlations_by_band = {f"{low}-{high}km": [] for low, high in distance_bands}
        
        market_ids = list(market_prices.keys())
        n_markets = len(market_ids)
        
        for i in range(n_markets):
            for j in range(i + 1, n_markets):
                market1, market2 = market_ids[i], market_ids[j]
                
                # Calculate distance
                loc1 = market_locations[market1]
                loc2 = market_locations[market2]
                distance = loc1.distance_to(loc2)
                
                # Calculate price correlation
                prices1 = np.array(market_prices[market1])
                prices2 = np.array(market_prices[market2])
                
                if len(prices1) == len(prices2) and len(prices1) > 1:
                    correlation = np.corrcoef(prices1, prices2)[0, 1]
                    
                    # Assign to distance band
                    for low, high in distance_bands:
                        if low <= distance < high:
                            band_key = f"{low}-{high}km"
                            correlations_by_band[band_key].append(correlation)
                            break
        
        # Calculate average correlation per band
        avg_correlations = {}
        for band, corrs in correlations_by_band.items():
            if corrs:
                avg_correlations[band] = float(np.mean(corrs))
            else:
                avg_correlations[band] = None
        
        return avg_correlations
    
    def _calculate_distance_matrix(self, coords: np.ndarray) -> np.ndarray:
        """Calculate pairwise distance matrix between coordinates."""
        # Convert to radians
        coords_rad = np.radians(coords)
        
        # Calculate pairwise distances
        n_points = len(coords)
        distances = np.zeros((n_points, n_points))
        
        for i in range(n_points):
            for j in range(i + 1, n_points):
                # Haversine formula
                dlat = coords_rad[j, 0] - coords_rad[i, 0]
                dlon = coords_rad[j, 1] - coords_rad[i, 1]
                
                a = (np.sin(dlat/2)**2 + 
                     np.cos(coords_rad[i, 0]) * np.cos(coords_rad[j, 0]) * 
                     np.sin(dlon/2)**2)
                c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))
                
                dist_km = 6371 * c  # Earth radius in km
                distances[i, j] = distances[j, i] = dist_km
        
        return distances
    
    def _create_spatial_cluster(self, markets: List[Market]) -> SpatialCluster:
        """Create a spatial cluster from markets."""
        # Calculate cluster center (centroid)
        avg_lat = np.mean([m.coordinates.latitude for m in markets])
        avg_lon = np.mean([m.coordinates.longitude for m in markets])
        center = Coordinates(latitude=avg_lat, longitude=avg_lon)
        
        # Calculate cluster radius
        max_distance = max(
            center.distance_to(m.coordinates)
            for m in markets
        )
        
        # Calculate density (markets per 100 km²)
        area = np.pi * max_distance**2
        density = len(markets) / (area / 100) if area > 0 else 0
        
        return SpatialCluster(
            center=center,
            markets=markets,
            radius_km=max_distance,
            density=density
        )
    
    def _calculate_control_zone_access(
        self,
        market: Market,
        other_markets: List[Market],
        governorates: List[Governorate]
    ) -> Dict[str, float]:
        """Calculate accessibility to different control zones."""
        # Find market's governorate
        market_gov = next(
            (g for g in governorates if g.name == market.governorate),
            None
        )
        
        if not market_gov:
            return {}
        
        # Group other markets by control zone
        zone_markets = {"IRG": [], "DFA": [], "Contested": []}
        
        for other in other_markets:
            other_gov = next(
                (g for g in governorates if g.name == other.governorate),
                None
            )
            if other_gov:
                zone_markets[other_gov.control_authority].append(other)
        
        # Calculate average distance to each zone
        zone_access = {}
        for zone, markets_in_zone in zone_markets.items():
            if markets_in_zone:
                distances = [
                    market.coordinates.distance_to(m.coordinates)
                    for m in markets_in_zone
                ]
                avg_distance = np.mean(distances)
                # Convert to accessibility score (inverse distance, normalized)
                zone_access[zone] = 1.0 / (1.0 + avg_distance / 100.0)
            else:
                zone_access[zone] = 0.0
        
        return zone_access