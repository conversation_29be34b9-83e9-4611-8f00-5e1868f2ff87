"""Domain events for event-driven architecture."""

from abc import ABC
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict
from uuid import UUID, uuid4


@dataclass
class DomainEvent(ABC):
    """Base domain event."""
    
    event_id: UUID = field(default_factory=uuid4)
    occurred_at: datetime = field(default_factory=datetime.utcnow)
    aggregate_id: UUID = field(default=None)
    
    @property
    def event_name(self) -> str:
        """Get the event name."""
        return self.__class__.__name__
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            'event_id': str(self.event_id),
            'event_name': self.event_name,
            'occurred_at': self.occurred_at.isoformat(),
            'aggregate_id': str(self.aggregate_id) if self.aggregate_id else None,
            'data': {
                k: v for k, v in self.__dict__.items()
                if k not in ['event_id', 'occurred_at', 'aggregate_id']
            }
        }