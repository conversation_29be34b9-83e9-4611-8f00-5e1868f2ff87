"""Domain exceptions for business rule violations."""


class DomainException(Exception):
    """Base domain exception."""
    
    def __init__(self, message: str, code: str = None) -> None:
        """Initialize domain exception with message and optional code."""
        super().__init__(message)
        self.code = code or self.__class__.__name__


class ValidationException(DomainException):
    """Raised when domain validation fails."""
    pass


class BusinessRuleViolation(DomainException):
    """Raised when a business rule is violated."""
    pass


class EntityNotFoundException(DomainException):
    """Raised when an entity is not found."""
    
    def __init__(self, entity_type: str, entity_id: str) -> None:
        """Initialize with entity type and ID."""
        super().__init__(
            f"{entity_type} with id '{entity_id}' not found",
            code="ENTITY_NOT_FOUND"
        )
        self.entity_type = entity_type
        self.entity_id = entity_id


class ConcurrencyException(DomainException):
    """Raised when there's a concurrency conflict."""
    
    def __init__(self, entity_type: str, entity_id: str, expected_version: int, actual_version: int) -> None:
        """Initialize with version conflict details."""
        super().__init__(
            f"Concurrency conflict for {entity_type} '{entity_id}': expected version {expected_version}, but was {actual_version}",
            code="CONCURRENCY_CONFLICT"
        )
        self.entity_type = entity_type
        self.entity_id = entity_id
        self.expected_version = expected_version
        self.actual_version = actual_version