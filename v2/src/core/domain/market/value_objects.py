"""Value objects for the Market bounded context."""

from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import Optional

from ..shared.entities import ValueObject
from ..shared.exceptions import ValidationException


@dataclass(frozen=True)
class MarketId(ValueObject):
    """Market identifier value object."""
    
    value: str
    
    def __post_init__(self) -> None:
        """Validate market ID."""
        if not self.value or not self.value.strip():
            raise ValidationException("Market ID cannot be empty")
        if len(self.value) > 50:
            raise ValidationException("Market ID cannot exceed 50 characters")
        super().__post_init__()


@dataclass(frozen=True)
class Coordinates(ValueObject):
    """Geographic coordinates value object."""
    
    latitude: float
    longitude: float
    
    def __post_init__(self) -> None:
        """Validate coordinates."""
        if not -90 <= self.latitude <= 90:
            raise ValidationException(f"Invalid latitude: {self.latitude}")
        if not -180 <= self.longitude <= 180:
            raise ValidationException(f"Invalid longitude: {self.longitude}")
        super().__post_init__()
    
    def distance_to(self, other: 'Coordinates') -> float:
        """Calculate distance to another coordinate in kilometers using Haversine formula."""
        from math import radians, sin, cos, sqrt, atan2
        
        R = 6371  # Earth's radius in kilometers
        
        lat1, lon1 = radians(self.latitude), radians(self.longitude)
        lat2, lon2 = radians(other.latitude), radians(other.longitude)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return R * c


class MarketType(Enum):
    """Market type enumeration."""
    
    WHOLESALE = "wholesale"
    RETAIL = "retail"
    BORDER = "border"
    PORT = "port"
    RURAL = "rural"
    URBAN = "urban"


@dataclass(frozen=True)
class Price(ValueObject):
    """Price value object with currency information."""
    
    amount: Decimal
    currency: str
    unit: str  # e.g., "kg", "50kg bag", "liter"
    
    def __post_init__(self) -> None:
        """Validate price."""
        if self.amount < 0:
            raise ValidationException("Price amount cannot be negative")
        if not self.currency:
            raise ValidationException("Currency is required")
        if not self.unit:
            raise ValidationException("Unit is required")
        super().__post_init__()
    
    def to_usd(self, exchange_rate: Decimal) -> 'Price':
        """Convert price to USD."""
        if self.currency == "USD":
            return self
        return Price(
            amount=self.amount / exchange_rate,
            currency="USD",
            unit=self.unit
        )
    
    def to_standard_unit(self, conversion_factor: Decimal, standard_unit: str) -> 'Price':
        """Convert price to standard unit (e.g., from 50kg bag to kg)."""
        return Price(
            amount=self.amount / conversion_factor,
            currency=self.currency,
            unit=standard_unit
        )


@dataclass(frozen=True)
class Commodity(ValueObject):
    """Commodity value object."""
    
    code: str
    name: str
    category: str
    standard_unit: str
    
    def __post_init__(self) -> None:
        """Validate commodity."""
        if not self.code:
            raise ValidationException("Commodity code is required")
        if not self.name:
            raise ValidationException("Commodity name is required")
        if not self.category:
            raise ValidationException("Commodity category is required")
        if not self.standard_unit:
            raise ValidationException("Standard unit is required")
        super().__post_init__()


@dataclass(frozen=True)
class MarketPair(ValueObject):
    """Represents a pair of markets for transmission analysis."""
    
    source: MarketId
    target: MarketId
    distance_km: Optional[float] = None
    
    def __post_init__(self) -> None:
        """Validate market pair."""
        if self.source == self.target:
            raise ValidationException("Source and target markets must be different")
        if self.distance_km is not None and self.distance_km < 0:
            raise ValidationException("Distance cannot be negative")
        super().__post_init__()