"""Market entities - core business objects."""

from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from ..shared.entities import AggregateRoot, Entity
from ..shared.events import DomainEvent
from ..shared.exceptions import BusinessRuleViolation, ValidationException
from .value_objects import MarketId, Coordinates, MarketType, Price, Commodity


@dataclass
class MarketCreatedEvent(DomainEvent):
    """Event raised when a market is created."""
    
    market_id: MarketId
    name: str
    market_type: MarketType


@dataclass
class MarketDeactivatedEvent(DomainEvent):
    """Event raised when a market is deactivated."""
    
    market_id: MarketId
    reason: str
    deactivated_at: datetime


@dataclass
class Market(AggregateRoot):
    """Market aggregate root - represents a physical market location."""
    
    market_id: MarketId
    name: str
    coordinates: Coordinates
    market_type: MarketType
    governorate: str
    district: str
    active_since: datetime
    active_until: Optional[datetime] = None
    
    def __post_init__(self) -> None:
        """Initialize market and raise creation event."""
        super().__post_init__()
        self._validate()
        self.add_event(MarketCreatedEvent(
            aggregate_id=self.id,
            market_id=self.market_id,
            name=self.name,
            market_type=self.market_type
        ))
    
    def _validate(self) -> None:
        """Validate market state."""
        if not self.name:
            raise ValidationException("Market name is required")
        if not self.governorate:
            raise ValidationException("Governorate is required")
        if not self.district:
            raise ValidationException("District is required")
        if self.active_since > datetime.utcnow():
            raise ValidationException("Active since date cannot be in the future")
        if self.active_until and self.active_until <= self.active_since:
            raise ValidationException("Active until must be after active since")
    
    def deactivate(self, reason: str) -> None:
        """Deactivate market with reason."""
        if self.active_until is not None:
            raise BusinessRuleViolation("Market is already deactivated")
        
        self.active_until = datetime.utcnow()
        self.add_event(MarketDeactivatedEvent(
            aggregate_id=self.id,
            market_id=self.market_id,
            reason=reason,
            deactivated_at=self.active_until
        ))
    
    def is_active_at(self, date: datetime) -> bool:
        """Check if market was active at given date."""
        if date < self.active_since:
            return False
        if self.active_until and date > self.active_until:
            return False
        return True
    
    def can_trade_commodity(self, commodity: Commodity) -> bool:
        """Check if market can trade given commodity based on market type."""
        # Business rule: Port markets can only trade imported commodities
        if self.market_type == MarketType.PORT:
            return commodity.category == "imported"
        
        # Business rule: Rural markets have limited commodity types
        if self.market_type == MarketType.RURAL:
            return commodity.category in ["local", "agricultural"]
        
        # Other markets can trade all commodities
        return True


@dataclass
class PriceObservation(Entity):
    """Price observation entity - represents a single price point."""
    
    market_id: MarketId
    commodity: Commodity
    price: Price
    observed_date: datetime
    source: str
    quality: str = "standard"
    observations_count: int = 1
    
    def __post_init__(self) -> None:
        """Validate price observation."""
        super().__post_init__()
        if self.observed_date > datetime.utcnow():
            raise ValidationException("Observation date cannot be in the future")
        if self.observations_count < 1:
            raise ValidationException("Observations count must be at least 1")
        if self.quality not in ["high", "standard", "low"]:
            raise ValidationException(f"Invalid quality: {self.quality}")
    
    def is_outlier(self, mean_price: Decimal, std_dev: Decimal, threshold: float = 3.0) -> bool:
        """Check if price is an outlier based on z-score."""
        if std_dev == 0:
            return False
        
        z_score = abs(float(self.price.amount - mean_price) / float(std_dev))
        return z_score > threshold
    
    def merge_with(self, other: 'PriceObservation') -> 'PriceObservation':
        """Merge with another observation of the same commodity and date."""
        if self.commodity != other.commodity:
            raise BusinessRuleViolation("Cannot merge observations of different commodities")
        if self.observed_date != other.observed_date:
            raise BusinessRuleViolation("Cannot merge observations from different dates")
        if self.market_id != other.market_id:
            raise BusinessRuleViolation("Cannot merge observations from different markets")
        
        # Calculate weighted average price
        total_count = self.observations_count + other.observations_count
        weighted_price = (
            (self.price.amount * self.observations_count +
             other.price.amount * other.observations_count) /
            total_count
        )
        
        return PriceObservation(
            market_id=self.market_id,
            commodity=self.commodity,
            price=Price(
                amount=weighted_price,
                currency=self.price.currency,
                unit=self.price.unit
            ),
            observed_date=self.observed_date,
            source=f"{self.source},{other.source}",
            quality=min(self.quality, other.quality),  # Conservative quality estimate
            observations_count=total_count
        )