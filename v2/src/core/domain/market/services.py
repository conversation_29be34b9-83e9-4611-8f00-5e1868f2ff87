"""Domain services for the Market bounded context."""

from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

import numpy as np

from ..shared.exceptions import BusinessRuleViolation
from .entities import Market, PriceObservation
from .value_objects import MarketId, MarketPair, Commodity


@dataclass
class TransmissionMetrics:
    """Price transmission metrics between markets."""
    
    market_pair: MarketPair
    commodity: Commodity
    period_start: datetime
    period_end: datetime
    correlation: float
    beta_coefficient: float
    adjustment_speed: float
    half_life_days: float
    threshold: Optional[float] = None
    regime: Optional[str] = None


@dataclass
class IntegrationMetrics:
    """Market integration metrics."""
    
    integrated_pairs: List[MarketPair]
    non_integrated_pairs: List[MarketPair]
    average_transmission_speed: float
    spatial_decay_parameter: float
    integration_score: float  # 0-1 score


class PriceTransmissionService:
    """Domain service for price transmission analysis."""
    
    def calculate_transmission(
        self,
        source_prices: List[PriceObservation],
        target_prices: List[PriceObservation],
        market_pair: MarketPair
    ) -> TransmissionMetrics:
        """Calculate price transmission metrics between markets."""
        if not source_prices or not target_prices:
            raise BusinessRuleViolation("Cannot calculate transmission without price data")
        
        # Ensure same commodity
        commodity = source_prices[0].commodity
        if any(p.commodity != commodity for p in source_prices + target_prices):
            raise BusinessRuleViolation("All prices must be for the same commodity")
        
        # Align time series
        source_series = self._create_price_series(source_prices)
        target_series = self._create_price_series(target_prices)
        aligned_source, aligned_target = self._align_series(source_series, target_series)
        
        if len(aligned_source) < 10:
            raise BusinessRuleViolation("Insufficient data for transmission analysis (need at least 10 observations)")
        
        # Calculate metrics
        correlation = np.corrcoef(aligned_source, aligned_target)[0, 1]
        beta_coefficient = self._calculate_beta(aligned_source, aligned_target)
        adjustment_speed = self._calculate_adjustment_speed(aligned_source, aligned_target)
        half_life_days = self._calculate_half_life(adjustment_speed)
        
        return TransmissionMetrics(
            market_pair=market_pair,
            commodity=commodity,
            period_start=min(p.observed_date for p in source_prices),
            period_end=max(p.observed_date for p in source_prices),
            correlation=correlation,
            beta_coefficient=beta_coefficient,
            adjustment_speed=adjustment_speed,
            half_life_days=half_life_days
        )
    
    def identify_transmission_breaks(
        self,
        transmission_series: List[TransmissionMetrics],
        threshold: float = 0.5
    ) -> List[datetime]:
        """Identify structural breaks in transmission."""
        if len(transmission_series) < 2:
            return []
        
        breaks = []
        for i in range(1, len(transmission_series)):
            prev = transmission_series[i-1]
            curr = transmission_series[i]
            
            # Check for significant change in transmission
            correlation_change = abs(curr.correlation - prev.correlation)
            beta_change = abs(curr.beta_coefficient - prev.beta_coefficient)
            
            if correlation_change > threshold or beta_change > threshold:
                breaks.append(curr.period_start)
        
        return breaks
    
    def _create_price_series(self, observations: List[PriceObservation]) -> Dict[datetime, Decimal]:
        """Create time series from observations."""
        series = {}
        for obs in observations:
            date = obs.observed_date.date()
            if date in series:
                # Average if multiple observations on same date
                series[date] = (series[date] + obs.price.amount) / 2
            else:
                series[date] = obs.price.amount
        return series
    
    def _align_series(
        self,
        series1: Dict[datetime, Decimal],
        series2: Dict[datetime, Decimal]
    ) -> Tuple[List[float], List[float]]:
        """Align two time series to common dates."""
        common_dates = sorted(set(series1.keys()) & set(series2.keys()))
        aligned1 = [float(series1[date]) for date in common_dates]
        aligned2 = [float(series2[date]) for date in common_dates]
        return aligned1, aligned2
    
    def _calculate_beta(self, source: List[float], target: List[float]) -> float:
        """Calculate beta coefficient (price transmission elasticity)."""
        # Simple OLS regression: target = alpha + beta * source
        source_array = np.array(source)
        target_array = np.array(target)
        
        # Add constant term
        X = np.column_stack([np.ones(len(source)), source_array])
        
        # OLS estimation
        beta_hat = np.linalg.lstsq(X, target_array, rcond=None)[0]
        return float(beta_hat[1])  # Return slope coefficient
    
    def _calculate_adjustment_speed(self, source: List[float], target: List[float]) -> float:
        """Calculate speed of adjustment using error correction model."""
        # Simplified ECM: Δtarget = α(target[-1] - β*source[-1]) + ε
        source_array = np.array(source)
        target_array = np.array(target)
        
        # Calculate first differences
        target_diff = np.diff(target_array)
        
        # Calculate error correction term
        beta = self._calculate_beta(source[:-1], target[:-1])
        error_term = target_array[:-1] - beta * source_array[:-1]
        
        # Estimate adjustment speed
        alpha = np.corrcoef(target_diff, error_term)[0, 1]
        return abs(float(alpha))  # Return absolute value
    
    def _calculate_half_life(self, adjustment_speed: float) -> float:
        """Calculate half-life of price shock."""
        if adjustment_speed <= 0 or adjustment_speed >= 1:
            return float('inf')
        return -np.log(2) / np.log(1 - adjustment_speed)


class MarketIntegrationService:
    """Domain service for market integration analysis."""
    
    def __init__(self, transmission_service: PriceTransmissionService):
        """Initialize with transmission service."""
        self.transmission_service = transmission_service
    
    def analyze_integration(
        self,
        markets: List[Market],
        price_observations: Dict[MarketId, List[PriceObservation]],
        distance_threshold: float = 500.0,  # km
        correlation_threshold: float = 0.7
    ) -> IntegrationMetrics:
        """Analyze market integration for a set of markets."""
        integrated_pairs = []
        non_integrated_pairs = []
        transmission_speeds = []
        
        # Analyze all market pairs
        for i, market1 in enumerate(markets):
            for market2 in markets[i+1:]:
                # Calculate distance
                distance = market1.coordinates.distance_to(market2.coordinates)
                
                # Skip if too far apart
                if distance > distance_threshold:
                    continue
                
                market_pair = MarketPair(
                    source=market1.market_id,
                    target=market2.market_id,
                    distance_km=distance
                )
                
                # Get price observations
                prices1 = price_observations.get(market1.market_id, [])
                prices2 = price_observations.get(market2.market_id, [])
                
                if not prices1 or not prices2:
                    non_integrated_pairs.append(market_pair)
                    continue
                
                try:
                    # Calculate transmission metrics
                    metrics = self.transmission_service.calculate_transmission(
                        prices1, prices2, market_pair
                    )
                    
                    # Classify as integrated or not
                    if metrics.correlation >= correlation_threshold:
                        integrated_pairs.append(market_pair)
                        transmission_speeds.append(metrics.adjustment_speed)
                    else:
                        non_integrated_pairs.append(market_pair)
                        
                except BusinessRuleViolation:
                    # Insufficient data
                    non_integrated_pairs.append(market_pair)
        
        # Calculate summary metrics
        avg_transmission_speed = (
            np.mean(transmission_speeds) if transmission_speeds else 0.0
        )
        
        # Estimate spatial decay parameter
        spatial_decay = self._estimate_spatial_decay(
            integrated_pairs, transmission_speeds
        )
        
        # Calculate integration score
        total_pairs = len(integrated_pairs) + len(non_integrated_pairs)
        integration_score = len(integrated_pairs) / total_pairs if total_pairs > 0 else 0.0
        
        return IntegrationMetrics(
            integrated_pairs=integrated_pairs,
            non_integrated_pairs=non_integrated_pairs,
            average_transmission_speed=float(avg_transmission_speed),
            spatial_decay_parameter=spatial_decay,
            integration_score=integration_score
        )
    
    def _estimate_spatial_decay(
        self,
        market_pairs: List[MarketPair],
        transmission_speeds: List[float]
    ) -> float:
        """Estimate how transmission speed decays with distance."""
        if not market_pairs or not transmission_speeds:
            return 0.0
        
        distances = [pair.distance_km for pair in market_pairs if pair.distance_km]
        
        if not distances:
            return 0.0
        
        # Simple exponential decay model: speed = exp(-decay * distance)
        # Taking log: log(speed) = -decay * distance
        log_speeds = np.log(transmission_speeds)
        
        # Estimate decay parameter
        decay = -np.corrcoef(distances, log_speeds)[0, 1]
        return max(0.0, float(decay))  # Ensure non-negative