"""<PERSON>sen cointegration test implementation.

This module provides a dedicated implementation of the <PERSON><PERSON> test
for cointegration in multivariate time series.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple
from statsmodels.tsa.vector_ar.vecm import coint_johansen, select_coint_rank

from infrastructure.logging import Logger

logger = Logger(__name__)


class JohansenTest:
    """<PERSON>sen cointegration test for multivariate time series.
    
    This test determines the number of cointegrating relationships
    among a set of time series variables.
    """
    
    def __init__(self, det_order: int = 0, k_ar_diff: int = 1):
        """Initialize <PERSON><PERSON> test.
        
        Args:
            det_order: Deterministic trend order
                      -1: No deterministic terms
                       0: Constant term restricted to cointegration relation
                       1: Constant term in model
                       2: Linear trend in cointegration relation
            k_ar_diff: Number of lags minus one in the VAR representation
        """
        self.det_order = det_order
        self.k_ar_diff = k_ar_diff
        
    def test(self, data: pd.DataFrame, 
             significance_level: float = 0.05) -> Dict[str, Any]:
        """Run Johansen cointegration test.
        
        Args:
            data: DataFrame with time series variables in columns
            significance_level: Significance level for rank selection
            
        Returns:
            Dictionary with test results including:
            - selected_rank: Number of cointegrating relationships
            - trace_statistics: Trace test statistics
            - max_eigenvalue_statistics: Maximum eigenvalue test statistics
            - critical_values: Critical values for both tests
            - eigenvalues: Eigenvalues from the test
            - eigenvectors: Cointegrating vectors
        """
        logger.info(f"Running Johansen test with det_order={self.det_order}, "
                   f"k_ar_diff={self.k_ar_diff}")
        
        # Ensure data is numeric and drop any NaN values
        numeric_data = data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        if len(clean_data) < 20:
            logger.error("Insufficient observations for Johansen test")
            return {'error': 'Insufficient data', 'selected_rank': 0}
        
        try:
            # Run rank selection test
            rank_test = select_coint_rank(
                clean_data,
                det_order=self.det_order,
                k_ar_diff=self.k_ar_diff,
                method='trace',
                signif=significance_level
            )
            
            selected_rank = rank_test.rank
            
            # Get full test results
            joh_result = coint_johansen(
                clean_data.values,
                det_order=self.det_order,
                k_ar_diff=self.k_ar_diff
            )
            
            # Extract results
            n_vars = data.shape[1]
            results = {
                'selected_rank': selected_rank,
                'n_variables': n_vars,
                'max_rank': n_vars - 1,
                'n_obs': len(clean_data),
                'det_order': self.det_order,
                'k_ar_diff': self.k_ar_diff,
                'significance_level': significance_level,
                
                # Trace test results
                'trace_statistics': joh_result.lr1.tolist(),
                'trace_critical_values': {
                    '10%': joh_result.cvt[:, 0].tolist(),
                    '5%': joh_result.cvt[:, 1].tolist(),
                    '1%': joh_result.cvt[:, 2].tolist()
                },
                
                # Maximum eigenvalue test results
                'max_eigenvalue_statistics': joh_result.lr2.tolist(),
                'max_eigenvalue_critical_values': {
                    '10%': joh_result.cvm[:, 0].tolist(),
                    '5%': joh_result.cvm[:, 1].tolist(),
                    '1%': joh_result.cvm[:, 2].tolist()
                },
                
                # Eigenvalues and eigenvectors
                'eigenvalues': joh_result.eig.tolist(),
                'eigenvectors': joh_result.evec.tolist(),
                
                # Test conclusions
                'trace_test_results': self._evaluate_trace_test(
                    joh_result.lr1, joh_result.cvt[:, 1]
                ),
                'max_eig_test_results': self._evaluate_max_eig_test(
                    joh_result.lr2, joh_result.cvm[:, 1]
                ),
                
                'conclusion': f"Found {selected_rank} cointegrating relationship(s) at {significance_level*100}% level"
            }
            
            # Add cointegrating vectors if any found
            if selected_rank > 0:
                results['cointegrating_vectors'] = self._extract_cointegrating_vectors(
                    joh_result, selected_rank
                )
                results['adjustment_coefficients'] = self._extract_adjustment_coefficients(
                    joh_result, selected_rank
                )
            
            logger.info(f"Johansen test complete: {results['conclusion']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Johansen test failed: {e}")
            return {
                'error': str(e),
                'selected_rank': 0,
                'conclusion': 'Test failed'
            }
    
    def test_with_lag_selection(self, data: pd.DataFrame,
                               max_lags: int = 12,
                               ic: str = 'aic') -> Dict[str, Any]:
        """Run Johansen test with automatic lag selection.
        
        Args:
            data: DataFrame with time series variables
            max_lags: Maximum number of lags to consider
            ic: Information criterion ('aic', 'bic', 'hqic', 'fpe')
            
        Returns:
            Dictionary with test results including optimal lag order
        """
        logger.info(f"Running Johansen test with lag selection (max_lags={max_lags}, ic={ic})")
        
        # Select optimal lag order
        from statsmodels.tsa.api import VAR
        
        model = VAR(data)
        lag_order_results = model.select_order(maxlags=max_lags)
        
        # Get optimal lag based on chosen criterion
        optimal_lag = getattr(lag_order_results, ic)
        logger.info(f"Selected lag order: {optimal_lag} (based on {ic.upper()})")
        
        # Update k_ar_diff
        self.k_ar_diff = optimal_lag - 1
        
        # Run test with optimal lag
        results = self.test(data)
        
        # Add lag selection information
        results['lag_selection'] = {
            'optimal_lag': optimal_lag,
            'criterion': ic,
            'aic': lag_order_results.aic,
            'bic': lag_order_results.bic,
            'hqic': lag_order_results.hqic,
            'fpe': lag_order_results.fpe
        }
        
        return results
    
    def _evaluate_trace_test(self, trace_stats: np.ndarray,
                            critical_values: np.ndarray) -> Dict[int, Dict[str, Any]]:
        """Evaluate trace test results for each rank.
        
        Args:
            trace_stats: Trace test statistics
            critical_values: 5% critical values
            
        Returns:
            Dictionary with test results for each rank hypothesis
        """
        results = {}
        n_stats = len(trace_stats)
        
        for r in range(n_stats):
            results[r] = {
                'null_hypothesis': f'r <= {r}',
                'statistic': float(trace_stats[r]),
                'critical_value_5pct': float(critical_values[r]),
                'reject_null': trace_stats[r] > critical_values[r]
            }
        
        return results
    
    def _evaluate_max_eig_test(self, max_eig_stats: np.ndarray,
                              critical_values: np.ndarray) -> Dict[int, Dict[str, Any]]:
        """Evaluate maximum eigenvalue test results for each rank.
        
        Args:
            max_eig_stats: Maximum eigenvalue test statistics
            critical_values: 5% critical values
            
        Returns:
            Dictionary with test results for each rank hypothesis
        """
        results = {}
        n_stats = len(max_eig_stats)
        
        for r in range(n_stats):
            results[r] = {
                'null_hypothesis': f'r = {r}',
                'statistic': float(max_eig_stats[r]),
                'critical_value_5pct': float(critical_values[r]),
                'reject_null': max_eig_stats[r] > critical_values[r]
            }
        
        return results
    
    def _extract_cointegrating_vectors(self, joh_result: Any,
                                     rank: int) -> Dict[str, np.ndarray]:
        """Extract normalized cointegrating vectors.
        
        Args:
            joh_result: Johansen test result object
            rank: Number of cointegrating relationships
            
        Returns:
            Dictionary with cointegrating vectors
        """
        # Extract first 'rank' columns of eigenvectors
        beta = joh_result.evec[:, :rank]
        
        # Normalize (first variable coefficient = 1)
        beta_normalized = beta / beta[0, :]
        
        return {
            'raw': beta.tolist(),
            'normalized': beta_normalized.tolist()
        }
    
    def _extract_adjustment_coefficients(self, joh_result: Any,
                                       rank: int) -> np.ndarray:
        """Extract adjustment coefficients (alpha).
        
        Args:
            joh_result: Johansen test result object
            rank: Number of cointegrating relationships
            
        Returns:
            Array of adjustment coefficients
        """
        # Note: statsmodels doesn't directly provide alpha
        # This would need to be calculated from the VECM representation
        # For now, return placeholder
        n_vars = joh_result.evec.shape[0]
        return np.zeros((n_vars, rank)).tolist()