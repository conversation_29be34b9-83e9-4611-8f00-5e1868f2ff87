"""Engle-Granger cointegration test implementation.

This module provides the two-step Engle-Granger cointegration test
for pairs of time series.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, List
from statsmodels.tsa.stattools import adfuller, coint
import statsmodels.api as sm

from infrastructure.logging import Logger

logger = Logger(__name__)


class EngleGrangerTest:
    """Engle-Granger two-step cointegration test.
    
    This test is designed for testing cointegration between pairs
    of time series variables.
    """
    
    def __init__(self, trend: str = 'c', autolag: str = 'AIC'):
        """Initialize Engle-Granger test.
        
        Args:
            trend: Trend to include in cointegrating regression
                  'nc': no constant
                  'c': constant (default)
                  'ct': constant and trend
                  'ctt': constant, trend, and trend squared
            autolag: Method for automatic lag selection in ADF test
        """
        self.trend = trend
        self.autolag = autolag
        
    def test_pair(self, y1: pd.Series, y2: pd.Series) -> Dict[str, Any]:
        """Test cointegration between two series.
        
        Args:
            y1: First time series
            y2: Second time series
            
        Returns:
            Dictionary with test results
        """
        logger.info("Running Engle-Granger test for series pair")
        
        # Align series
        aligned = pd.DataFrame({'y1': y1, 'y2': y2}).dropna()
        
        if len(aligned) < 20:
            logger.warning("Insufficient observations for cointegration test")
            return {
                'error': 'Insufficient data',
                'n_obs': len(aligned),
                'is_cointegrated': False
            }
        
        try:
            # Use statsmodels coint function
            coint_t, p_value, crit_values = coint(
                aligned['y1'], 
                aligned['y2'],
                trend=self.trend,
                autolag=self.autolag,
                return_results=False
            )
            
            # Also run the regression manually to get more details
            regression_results = self._run_cointegrating_regression(
                aligned['y1'], aligned['y2']
            )
            
            # Test residuals for unit root
            residual_test = self._test_residuals(regression_results['residuals'])
            
            is_cointegrated = p_value < 0.05
            
            results = {
                'test_statistic': float(coint_t),
                'p_value': float(p_value),
                'critical_values': {
                    '1%': float(crit_values[0]),
                    '5%': float(crit_values[1]),
                    '10%': float(crit_values[2])
                },
                'is_cointegrated': is_cointegrated,
                'n_obs': len(aligned),
                'trend': self.trend,
                'autolag': self.autolag,
                
                # Cointegrating regression details
                'cointegrating_regression': regression_results,
                
                # Residual unit root test
                'residual_test': residual_test,
                
                'conclusion': 'Series are cointegrated' if is_cointegrated else 'No cointegration found'
            }
            
            logger.info(f"Engle-Granger test complete: {results['conclusion']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Engle-Granger test failed: {e}")
            return {
                'error': str(e),
                'is_cointegrated': False,
                'conclusion': 'Test failed'
            }
    
    def test_all_pairs(self, data: pd.DataFrame,
                      significance_level: float = 0.05) -> Dict[str, Any]:
        """Test cointegration for all pairs of variables.
        
        Args:
            data: DataFrame with time series variables in columns
            significance_level: Significance level for tests
            
        Returns:
            Dictionary with test results for all pairs
        """
        logger.info(f"Running Engle-Granger tests for all pairs in {len(data.columns)} variables")
        
        variables = list(data.columns)
        n_vars = len(variables)
        
        if n_vars < 2:
            return {'error': 'Need at least 2 variables for pairwise tests'}
        
        results = {
            'pairwise_tests': {},
            'significant_pairs': [],
            'cointegration_matrix': np.zeros((n_vars, n_vars)),
            'n_pairs_tested': 0,
            'n_cointegrated': 0
        }
        
        # Test all unique pairs
        for i in range(n_vars):
            for j in range(i + 1, n_vars):
                var1, var2 = variables[i], variables[j]
                pair_name = f"{var1}-{var2}"
                
                # Run test for this pair
                pair_result = self.test_pair(data[var1], data[var2])
                results['pairwise_tests'][pair_name] = pair_result
                
                # Update statistics
                results['n_pairs_tested'] += 1
                
                if pair_result.get('is_cointegrated', False):
                    results['significant_pairs'].append(pair_name)
                    results['n_cointegrated'] += 1
                    results['cointegration_matrix'][i, j] = 1
                    results['cointegration_matrix'][j, i] = 1
        
        # Calculate summary statistics
        results['proportion_cointegrated'] = (
            results['n_cointegrated'] / results['n_pairs_tested']
            if results['n_pairs_tested'] > 0 else 0
        )
        
        results['summary'] = {
            'total_pairs': results['n_pairs_tested'],
            'cointegrated_pairs': results['n_cointegrated'],
            'proportion': results['proportion_cointegrated'],
            'significant_pairs': results['significant_pairs']
        }
        
        logger.info(f"Pairwise tests complete: {results['n_cointegrated']}/{results['n_pairs_tested']} "
                   f"pairs cointegrated")
        
        return results
    
    def _run_cointegrating_regression(self, y: pd.Series, 
                                    x: pd.Series) -> Dict[str, Any]:
        """Run the cointegrating regression (step 1 of Engle-Granger).
        
        Args:
            y: Dependent variable
            x: Independent variable
            
        Returns:
            Dictionary with regression results
        """
        # Add appropriate deterministic terms based on trend
        if self.trend == 'nc':
            # No constant
            X = x.values.reshape(-1, 1)
        elif self.trend == 'c':
            # Add constant
            X = sm.add_constant(x)
        elif self.trend == 'ct':
            # Add constant and trend
            X = sm.add_constant(x)
            X['trend'] = np.arange(len(x))
        elif self.trend == 'ctt':
            # Add constant, trend, and trend squared
            X = sm.add_constant(x)
            X['trend'] = np.arange(len(x))
            X['trend2'] = X['trend'] ** 2
        else:
            raise ValueError(f"Unknown trend specification: {self.trend}")
        
        # Run OLS regression
        model = sm.OLS(y, X)
        results = model.fit()
        
        return {
            'coefficients': results.params.to_dict(),
            'std_errors': results.bse.to_dict(),
            't_stats': results.tvalues.to_dict(),
            'p_values': results.pvalues.to_dict(),
            'r_squared': results.rsquared,
            'adj_r_squared': results.rsquared_adj,
            'residuals': results.resid,
            'fitted_values': results.fittedvalues
        }
    
    def _test_residuals(self, residuals: pd.Series) -> Dict[str, Any]:
        """Test residuals for unit root (step 2 of Engle-Granger).
        
        Args:
            residuals: Residuals from cointegrating regression
            
        Returns:
            Dictionary with ADF test results
        """
        # ADF test on residuals
        # Note: Use 'nc' because residuals from regression have no mean
        adf_result = adfuller(
            residuals, 
            regression='nc',
            autolag=self.autolag
        )
        
        return {
            'adf_statistic': adf_result[0],
            'p_value': adf_result[1],
            'used_lag': adf_result[2],
            'n_obs': adf_result[3],
            'critical_values': adf_result[4],
            'ic_best': adf_result[5] if len(adf_result) > 5 else None
        }
    
    def estimate_error_correction_model(self, y: pd.Series, x: pd.Series,
                                      cointegrating_residuals: pd.Series,
                                      n_lags: int = 1) -> Dict[str, Any]:
        """Estimate the error correction model given cointegrating residuals.
        
        Args:
            y: Dependent variable
            x: Independent variable
            cointegrating_residuals: Residuals from cointegrating regression
            n_lags: Number of lags of differences to include
            
        Returns:
            Dictionary with ECM estimation results
        """
        # Create differences
        dy = y.diff()
        dx = x.diff()
        
        # Lag the error correction term
        ect = cointegrating_residuals.shift(1)
        
        # Create regression data
        data = pd.DataFrame({
            'dy': dy,
            'dx': dx,
            'ect': ect
        })
        
        # Add lags if requested
        for lag in range(1, n_lags + 1):
            data[f'dy_lag{lag}'] = dy.shift(lag)
            data[f'dx_lag{lag}'] = dx.shift(lag)
        
        # Drop missing values
        data = data.dropna()
        
        # Set up regression
        y_ecm = data['dy']
        X_ecm = data.drop('dy', axis=1)
        X_ecm = sm.add_constant(X_ecm)
        
        # Estimate ECM
        ecm_model = sm.OLS(y_ecm, X_ecm)
        ecm_results = ecm_model.fit()
        
        # Extract key results
        results = {
            'adjustment_speed': ecm_results.params.get('ect', np.nan),
            'adjustment_t_stat': ecm_results.tvalues.get('ect', np.nan),
            'adjustment_p_value': ecm_results.pvalues.get('ect', np.nan),
            'short_run_coefficients': {
                k: v for k, v in ecm_results.params.items() 
                if k not in ['const', 'ect']
            },
            'r_squared': ecm_results.rsquared,
            'adj_r_squared': ecm_results.rsquared_adj,
            'aic': ecm_results.aic,
            'bic': ecm_results.bic,
            'full_results': ecm_results
        }
        
        return results