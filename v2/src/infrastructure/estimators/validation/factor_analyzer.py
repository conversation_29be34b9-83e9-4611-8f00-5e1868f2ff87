"""Factor analysis estimator implementation.

This module provides the infrastructure for extracting latent factors
from market price data using factor analysis techniques.
"""

from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.decomposition import FactorAnalysis
from sklearn.preprocessing import StandardScaler

from ....core.models.validation import FactorModel
from ....core.models.interfaces import EstimationResult, Estimator
from ...logging import Logger

logger = Logger(__name__)


class FactorAnalyzer(Estimator):
    """Estimator for factor analysis of market prices.
    
    This estimator implements factor extraction techniques to identify
    latent factors driving price co-movements across markets.
    """
    
    def __init__(self):
        """Initialize factor analyzer."""
        self.model = None
        self.scaler = StandardScaler()
        
    def estimate(self, model: FactorModel, 
                data: pd.DataFrame) -> EstimationResult:
        """Extract factors from market price data.
        
        Args:
            model: Factor model specification
            data: Price data (wide format with markets as columns)
            
        Returns:
            EstimationResult with factor loadings and scores
        """
        logger.info(f"Starting factor analysis with {model.n_factors} factors")
        
        self.model = model
        
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")
        
        # Prepare data
        prepared_data = model.prepare_data(data)
        
        # Standardize if requested
        if model.standardize:
            standardized_data = self.scaler.fit_transform(prepared_data)
            data_for_analysis = pd.DataFrame(
                standardized_data,
                index=prepared_data.index,
                columns=prepared_data.columns
            )
        else:
            data_for_analysis = prepared_data
        
        # Perform factor analysis
        factor_results = self._perform_factor_analysis(
            data_for_analysis, model
        )
        
        # Test factor significance
        significance_tests = self._test_factor_significance(
            factor_results, data_for_analysis
        )
        
        # Calculate factor scores
        factor_scores = self._calculate_factor_scores(
            data_for_analysis, factor_results
        )
        
        # Interpret factors
        factor_interpretation = self._interpret_factors(
            factor_results, prepared_data.columns
        )
        
        # Create comprehensive results
        results = {
            'loadings': factor_results['loadings'],
            'communalities': factor_results['communalities'],
            'uniqueness': factor_results['uniqueness'],
            'variance_explained': factor_results['variance_explained'],
            'factor_scores': factor_scores,
            'significance_tests': significance_tests,
            'interpretation': factor_interpretation,
            'rotation_matrix': factor_results.get('rotation_matrix')
        }
        
        return EstimationResult(
            model_name=model.name,
            params=pd.Series(factor_results['loadings'].flatten()),
            standard_errors=pd.Series([np.nan] * len(factor_results['loadings'].flatten())),
            fitted_values=factor_scores,
            residuals=self._calculate_residuals(data_for_analysis, factor_results),
            diagnostics=significance_tests,
            metadata={
                'n_factors': model.n_factors,
                'rotation': model.rotation,
                'standardized': model.standardize,
                'results': results
            }
        )
    
    def _perform_factor_analysis(self, data: pd.DataFrame,
                                model: FactorModel) -> Dict[str, Any]:
        """Perform the actual factor analysis.
        
        Args:
            data: Prepared data
            model: Model specification
            
        Returns:
            Dictionary with factor analysis results
        """
        # Initialize factor analysis
        n_factors = model.n_factors
        if n_factors is None:
            # Determine optimal number of factors
            n_factors = self._determine_n_factors(data, model)
        
        # Fit factor analysis model
        fa = FactorAnalysis(
            n_components=n_factors,
            rotation=model.rotation if model.rotation != 'none' else None,
            max_iter=1000,
            random_state=42
        )
        
        try:
            fa.fit(data)
            
            # Extract results
            loadings = pd.DataFrame(
                fa.components_.T,
                index=data.columns,
                columns=[f'Factor_{i+1}' for i in range(n_factors)]
            )
            
            # Calculate communalities
            communalities = np.sum(loadings.values ** 2, axis=1)
            
            # Calculate uniqueness (specific variance)
            uniqueness = 1 - communalities
            
            # Variance explained
            explained_var = fa.explained_variance_ratio_
            cumulative_var = np.cumsum(explained_var)
            
            results = {
                'loadings': loadings,
                'communalities': pd.Series(communalities, index=data.columns),
                'uniqueness': pd.Series(uniqueness, index=data.columns),
                'variance_explained': {
                    'individual': pd.Series(explained_var, 
                                          index=[f'Factor_{i+1}' for i in range(n_factors)]),
                    'cumulative': pd.Series(cumulative_var,
                                          index=[f'Factor_{i+1}' for i in range(n_factors)])
                },
                'factor_model': fa
            }
            
            # Add rotation matrix if rotation was applied
            if model.rotation and model.rotation != 'none':
                if hasattr(fa, 'rotation_matrix_'):
                    results['rotation_matrix'] = fa.rotation_matrix_
            
            return results
            
        except Exception as e:
            logger.error(f"Factor analysis failed: {e}")
            raise
    
    def _determine_n_factors(self, data: pd.DataFrame,
                           model: FactorModel) -> int:
        """Determine optimal number of factors using various criteria.
        
        Args:
            data: Data matrix
            model: Model specification
            
        Returns:
            Optimal number of factors
        """
        n_vars = data.shape[1]
        max_factors = min(n_vars - 1, data.shape[0] // 10)
        
        # Try different numbers of factors
        criteria = {
            'eigenvalues': [],
            'variance_explained': [],
            'bic': []
        }
        
        for n in range(1, max_factors + 1):
            try:
                fa = FactorAnalysis(n_components=n, random_state=42)
                fa.fit(data)
                
                # Approximate eigenvalues from explained variance
                criteria['eigenvalues'].append(fa.explained_variance_ratio_[0])
                criteria['variance_explained'].append(sum(fa.explained_variance_ratio_))
                
                # Approximate BIC
                n_params = n * n_vars
                log_likelihood = fa.score(data) * data.shape[0]
                bic = -2 * log_likelihood + n_params * np.log(data.shape[0])
                criteria['bic'].append(bic)
                
            except Exception:
                break
        
        # Kaiser criterion: eigenvalues > 1
        # Using variance explained as proxy
        kaiser_n = sum(1 for ev in criteria['eigenvalues'] if ev > 1/n_vars)
        
        # Scree test: look for elbow
        if len(criteria['variance_explained']) > 2:
            diffs = np.diff(criteria['variance_explained'])
            scree_n = np.argmin(diffs) + 1 if len(diffs) > 0 else 1
        else:
            scree_n = 1
        
        # BIC: minimum BIC
        if criteria['bic']:
            bic_n = np.argmin(criteria['bic']) + 1
        else:
            bic_n = kaiser_n
        
        # Use specified threshold if provided
        if model.variance_threshold:
            cumvar = np.cumsum(criteria['variance_explained'])
            threshold_n = next((i + 1 for i, cv in enumerate(cumvar) 
                              if cv >= model.variance_threshold), kaiser_n)
        else:
            threshold_n = kaiser_n
        
        # Take median of different criteria
        optimal_n = int(np.median([kaiser_n, scree_n, bic_n, threshold_n]))
        
        logger.info(f"Factor selection - Kaiser: {kaiser_n}, Scree: {scree_n}, "
                   f"BIC: {bic_n}, Threshold: {threshold_n}, Selected: {optimal_n}")
        
        return max(1, min(optimal_n, max_factors))
    
    def _test_factor_significance(self, factor_results: Dict[str, Any],
                                 data: pd.DataFrame) -> Dict[str, Any]:
        """Test statistical significance of factors.
        
        Args:
            factor_results: Results from factor analysis
            data: Original data
            
        Returns:
            Dictionary with significance tests
        """
        tests = {}
        
        # 1. Bartlett's test of sphericity
        # Tests if correlation matrix is identity (no factors needed)
        corr_matrix = data.corr()
        n_obs = len(data)
        n_vars = data.shape[1]
        
        # Calculate determinant of correlation matrix
        det_corr = np.linalg.det(corr_matrix)
        
        if det_corr > 0:
            chi_square = -(n_obs - 1 - (2 * n_vars + 5) / 6) * np.log(det_corr)
            df = n_vars * (n_vars - 1) / 2
            p_value = 1 - stats.chi2.cdf(chi_square, df)
            
            tests['bartlett_sphericity'] = {
                'test_statistic': chi_square,
                'p_value': p_value,
                'reject_null': p_value < 0.05,
                'interpretation': 'Factor analysis is appropriate' if p_value < 0.05 
                                else 'Variables may be uncorrelated'
            }
        
        # 2. KMO (Kaiser-Meyer-Olkin) test
        # Measures sampling adequacy
        kmo_all, kmo_individual = self._calculate_kmo(corr_matrix)
        
        tests['kmo'] = {
            'overall': kmo_all,
            'individual': pd.Series(kmo_individual, index=data.columns),
            'interpretation': self._interpret_kmo(kmo_all)
        }
        
        # 3. Test individual factor loadings
        loadings = factor_results['loadings']
        n_factors = loadings.shape[1]
        
        # Approximate standard errors for loadings
        loading_se = 1 / np.sqrt(n_obs)
        
        loading_tests = {}
        for factor in loadings.columns:
            t_stats = loadings[factor] / loading_se
            p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), n_obs - 1))
            
            loading_tests[factor] = {
                't_statistics': t_stats,
                'p_values': p_values,
                'significant_loadings': sum(p_values < 0.05),
                'percent_significant': sum(p_values < 0.05) / len(p_values) * 100
            }
        
        tests['loading_significance'] = loading_tests
        
        # 4. Goodness of fit test
        # Compare reproduced correlation vs original
        reproduced_corr = loadings @ loadings.T
        residual_corr = corr_matrix - reproduced_corr
        
        # Count residuals > 0.05
        large_residuals = np.sum(np.abs(residual_corr.values) > 0.05) / 2  # Symmetric
        total_correlations = n_vars * (n_vars - 1) / 2
        
        tests['goodness_of_fit'] = {
            'large_residuals': large_residuals,
            'percent_large_residuals': large_residuals / total_correlations * 100,
            'rmse': np.sqrt(np.mean(residual_corr.values ** 2)),
            'interpretation': 'Good fit' if large_residuals / total_correlations < 0.05 
                            else 'Poor fit - more factors may be needed'
        }
        
        return tests
    
    def _calculate_kmo(self, corr_matrix: pd.DataFrame) -> tuple:
        """Calculate Kaiser-Meyer-Olkin measure of sampling adequacy.
        
        Args:
            corr_matrix: Correlation matrix
            
        Returns:
            Tuple of (overall KMO, individual KMOs)
        """
        # Inverse of correlation matrix
        try:
            inv_corr = np.linalg.inv(corr_matrix)
            
            # Partial correlations
            partial_corr = -inv_corr / np.sqrt(np.outer(np.diag(inv_corr), 
                                                       np.diag(inv_corr)))
            np.fill_diagonal(partial_corr, 0)
            
            # KMO calculation
            sum_corr = np.sum(corr_matrix.values ** 2) - np.trace(corr_matrix.values ** 2)
            sum_partial = np.sum(partial_corr ** 2)
            
            kmo_all = sum_corr / (sum_corr + sum_partial)
            
            # Individual KMO
            kmo_individual = []
            for i in range(len(corr_matrix)):
                sum_r = np.sum(corr_matrix.iloc[i, :] ** 2) - 1
                sum_p = np.sum(partial_corr[i, :] ** 2)
                kmo_i = sum_r / (sum_r + sum_p) if (sum_r + sum_p) > 0 else 0
                kmo_individual.append(kmo_i)
            
            return kmo_all, kmo_individual
            
        except np.linalg.LinAlgError:
            # Singular matrix
            return 0, [0] * len(corr_matrix)
    
    def _interpret_kmo(self, kmo: float) -> str:
        """Interpret KMO value.
        
        Args:
            kmo: KMO statistic
            
        Returns:
            Interpretation string
        """
        if kmo >= 0.9:
            return "Marvelous"
        elif kmo >= 0.8:
            return "Meritorious"
        elif kmo >= 0.7:
            return "Middling"
        elif kmo >= 0.6:
            return "Mediocre"
        elif kmo >= 0.5:
            return "Miserable"
        else:
            return "Unacceptable for factor analysis"
    
    def _calculate_factor_scores(self, data: pd.DataFrame,
                               factor_results: Dict[str, Any]) -> pd.DataFrame:
        """Calculate factor scores for each observation.
        
        Args:
            data: Original data
            factor_results: Factor analysis results
            
        Returns:
            DataFrame with factor scores
        """
        fa_model = factor_results['factor_model']
        
        # Transform data to get factor scores
        scores = fa_model.transform(data)
        
        # Create DataFrame
        factor_names = [f'Factor_{i+1}' for i in range(scores.shape[1])]
        factor_scores = pd.DataFrame(
            scores,
            index=data.index,
            columns=factor_names
        )
        
        return factor_scores
    
    def _calculate_residuals(self, data: pd.DataFrame,
                           factor_results: Dict[str, Any]) -> pd.DataFrame:
        """Calculate residuals from factor model.
        
        Args:
            data: Original data
            factor_results: Factor analysis results
            
        Returns:
            DataFrame with residuals
        """
        # Reconstruct data from factors
        loadings = factor_results['loadings'].values
        scores = factor_results['factor_model'].transform(data)
        reconstructed = scores @ loadings.T
        
        # Calculate residuals
        residuals = data.values - reconstructed
        
        return pd.DataFrame(
            residuals,
            index=data.index,
            columns=data.columns
        )
    
    def _interpret_factors(self, factor_results: Dict[str, Any],
                         variable_names: pd.Index) -> Dict[str, Any]:
        """Interpret factors based on loadings.
        
        Args:
            factor_results: Factor analysis results
            variable_names: Names of variables
            
        Returns:
            Dictionary with factor interpretations
        """
        loadings = factor_results['loadings']
        interpretations = {}
        
        for factor in loadings.columns:
            # Get variables with high loadings (> 0.4)
            high_loadings = loadings[loadings[factor].abs() > 0.4][factor]
            high_loadings = high_loadings.sort_values(ascending=False)
            
            # Identify positive and negative loadings
            positive_vars = high_loadings[high_loadings > 0]
            negative_vars = high_loadings[high_loadings < 0]
            
            interpretation = {
                'high_positive_loadings': positive_vars.to_dict(),
                'high_negative_loadings': negative_vars.to_dict(),
                'variance_explained': factor_results['variance_explained']['individual'][factor],
                'suggested_name': self._suggest_factor_name(positive_vars, negative_vars)
            }
            
            interpretations[factor] = interpretation
        
        return interpretations
    
    def _suggest_factor_name(self, positive_vars: pd.Series,
                           negative_vars: pd.Series) -> str:
        """Suggest a name for the factor based on loadings.
        
        Args:
            positive_vars: Variables with positive loadings
            negative_vars: Variables with negative loadings
            
        Returns:
            Suggested factor name
        """
        # This would be customized based on domain knowledge
        # For market analysis, some common patterns:
        
        if len(positive_vars) > len(negative_vars) * 2:
            # Mostly positive loadings
            if any('price' in str(var).lower() for var in positive_vars.index):
                return "General Price Level"
            elif any('conflict' in str(var).lower() for var in positive_vars.index):
                return "Conflict Intensity"
            else:
                return "Common Positive Factor"
        
        elif len(negative_vars) > len(positive_vars) * 2:
            # Mostly negative loadings
            return "Inverse Factor"
        
        else:
            # Mixed loadings
            return "Bipolar Factor"
    
    def diagnose(self, model: FactorModel, 
                result: EstimationResult) -> Dict[str, Any]:
        """Run diagnostic tests for factor analysis.
        
        Args:
            model: Factor model
            result: Estimation results
            
        Returns:
            Dictionary with diagnostic test results
        """
        # Most diagnostics are already in the significance tests
        # Add any additional diagnostics here
        
        diagnostics = result.diagnostics.copy()
        
        # Add factor stability test
        if hasattr(result, 'metadata') and 'results' in result.metadata:
            loadings = result.metadata['results']['loadings']
            
            # Check for Heywood cases (communalities > 1)
            communalities = result.metadata['results']['communalities']
            heywood_cases = communalities[communalities > 1]
            
            diagnostics['heywood_cases'] = {
                'found': len(heywood_cases) > 0,
                'variables': heywood_cases.index.tolist() if len(heywood_cases) > 0 else [],
                'interpretation': 'Model may be overfitted' if len(heywood_cases) > 0 
                                else 'No Heywood cases found'
            }
        
        return diagnostics
    
    def predict(self, model: FactorModel, 
               result: EstimationResult, 
               new_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate factor scores for new data.
        
        Args:
            model: Factor model
            result: Estimation results
            new_data: New observations
            
        Returns:
            Factor scores for new data
        """
        if 'results' not in result.metadata:
            raise ValueError("No factor model found in results")
        
        fa_model = result.metadata['results']['factor_model']
        
        # Prepare new data
        prepared_data = model.prepare_data(new_data)
        
        # Standardize if the original model was standardized
        if model.standardize:
            standardized_data = self.scaler.transform(prepared_data)
            data_for_scoring = pd.DataFrame(
                standardized_data,
                index=prepared_data.index,
                columns=prepared_data.columns
            )
        else:
            data_for_scoring = prepared_data
        
        # Calculate scores
        scores = fa_model.transform(data_for_scoring)
        
        factor_names = [f'Factor_{i+1}' for i in range(scores.shape[1])]
        return pd.DataFrame(
            scores,
            index=data_for_scoring.index,
            columns=factor_names
        )