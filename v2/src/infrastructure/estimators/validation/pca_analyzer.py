"""Principal Component Analysis estimator implementation.

This module provides the infrastructure for performing PCA on market
price data to reduce dimensionality and identify key patterns.
"""

from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

from ....core.models.validation import PCAModel
from ....core.models.interfaces import EstimationResult, Estimator
from ...logging import Logger

logger = Logger(__name__)


class PCAAnalyzer(Estimator):
    """Estimator for Principal Component Analysis of market prices.
    
    This estimator implements PCA techniques for dimensionality reduction
    and pattern identification in market price data.
    """
    
    def __init__(self):
        """Initialize PCA analyzer."""
        self.model = None
        self.scaler = StandardScaler()
        self.pca = None
        
    def estimate(self, model: PCAModel, 
                data: pd.DataFrame) -> EstimationResult:
        """Perform PCA on market price data.
        
        Args:
            model: PCA model specification
            data: Price data (wide format with markets/variables as columns)
            
        Returns:
            EstimationResult with PCA results
        """
        logger.info(f"Starting PCA with variance threshold {model.variance_threshold}")
        
        self.model = model
        
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")
        
        # Prepare data
        prepared_data = model.prepare_data(data)
        
        # Standardize if requested
        if model.standardize:
            standardized_data = self.scaler.fit_transform(prepared_data)
            data_for_pca = pd.DataFrame(
                standardized_data,
                index=prepared_data.index,
                columns=prepared_data.columns
            )
        else:
            data_for_pca = prepared_data
        
        # Perform PCA
        pca_results = self._perform_pca(data_for_pca, model)
        
        # Test component significance
        significance_tests = self._test_component_significance(
            pca_results, data_for_pca
        )
        
        # Calculate component scores
        component_scores = self._calculate_component_scores(
            data_for_pca, pca_results
        )
        
        # Analyze component stability
        stability_analysis = self._analyze_component_stability(
            data_for_pca, pca_results
        )
        
        # Interpret components
        component_interpretation = self._interpret_components(
            pca_results, prepared_data.columns
        )
        
        # Create comprehensive results
        results = {
            'loadings': pca_results['loadings'],
            'eigenvalues': pca_results['eigenvalues'],
            'variance_explained': pca_results['variance_explained'],
            'component_scores': component_scores,
            'significance_tests': significance_tests,
            'stability_analysis': stability_analysis,
            'interpretation': component_interpretation
        }
        
        return EstimationResult(
            model_name=model.name,
            params=pd.Series(pca_results['loadings'].values.flatten()),
            standard_errors=pd.Series([np.nan] * len(pca_results['loadings'].values.flatten())),
            fitted_values=component_scores,
            residuals=self._calculate_reconstruction_error(data_for_pca, pca_results),
            diagnostics=significance_tests,
            metadata={
                'n_components': pca_results['n_components'],
                'variance_threshold': model.variance_threshold,
                'standardized': model.standardize,
                'results': results
            }
        )
    
    def _perform_pca(self, data: pd.DataFrame,
                    model: PCAModel) -> Dict[str, Any]:
        """Perform the actual PCA.
        
        Args:
            data: Prepared data
            model: Model specification
            
        Returns:
            Dictionary with PCA results
        """
        # Determine number of components
        if model.n_components is not None:
            n_components = model.n_components
        elif model.variance_threshold is not None:
            # Use all components initially to determine how many we need
            n_components = min(data.shape)
        else:
            # Default to preserving 95% variance
            n_components = min(data.shape)
        
        # Fit PCA
        self.pca = PCA(n_components=n_components, random_state=42)
        self.pca.fit(data)
        
        # Determine final number of components based on variance threshold
        if model.variance_threshold is not None:
            cumvar = np.cumsum(self.pca.explained_variance_ratio_)
            n_components = np.argmax(cumvar >= model.variance_threshold) + 1
            
            # Refit with optimal number
            self.pca = PCA(n_components=n_components, random_state=42)
            self.pca.fit(data)
        
        # Extract results
        component_names = [f'PC{i+1}' for i in range(self.pca.n_components_)]
        
        loadings = pd.DataFrame(
            self.pca.components_.T,
            index=data.columns,
            columns=component_names
        )
        
        eigenvalues = pd.Series(
            self.pca.explained_variance_,
            index=component_names
        )
        
        variance_explained = {
            'individual': pd.Series(
                self.pca.explained_variance_ratio_,
                index=component_names
            ),
            'cumulative': pd.Series(
                np.cumsum(self.pca.explained_variance_ratio_),
                index=component_names
            )
        }
        
        results = {
            'loadings': loadings,
            'eigenvalues': eigenvalues,
            'variance_explained': variance_explained,
            'n_components': self.pca.n_components_,
            'pca_model': self.pca
        }
        
        logger.info(f"PCA complete: {self.pca.n_components_} components "
                   f"explain {variance_explained['cumulative'].iloc[-1]:.2%} of variance")
        
        return results
    
    def _test_component_significance(self, pca_results: Dict[str, Any],
                                   data: pd.DataFrame) -> Dict[str, Any]:
        """Test statistical significance of principal components.
        
        Args:
            pca_results: Results from PCA
            data: Original data
            
        Returns:
            Dictionary with significance tests
        """
        tests = {}
        
        # 1. Kaiser-Guttman criterion (eigenvalue > 1)
        eigenvalues = pca_results['eigenvalues']
        if self.model.standardize:
            # For standardized data, compare to 1
            kaiser_significant = eigenvalues > 1
        else:
            # For non-standardized, compare to mean eigenvalue
            kaiser_significant = eigenvalues > eigenvalues.mean()
        
        tests['kaiser_criterion'] = {
            'significant_components': kaiser_significant.sum(),
            'eigenvalues': eigenvalues.to_dict(),
            'threshold': 1 if self.model.standardize else eigenvalues.mean(),
            'interpretation': f"{kaiser_significant.sum()} components have eigenvalues above threshold"
        }
        
        # 2. Broken stick model
        n_vars = data.shape[1]
        broken_stick = self._broken_stick_model(n_vars)
        bs_significant = pca_results['variance_explained']['individual'] > broken_stick[:len(eigenvalues)]
        
        tests['broken_stick'] = {
            'significant_components': bs_significant.sum(),
            'expected_variance': pd.Series(
                broken_stick[:len(eigenvalues)],
                index=eigenvalues.index
            ).to_dict(),
            'interpretation': f"{bs_significant.sum()} components exceed random expectation"
        }
        
        # 3. Parallel analysis (permutation test)
        parallel_eigenvalues = self._parallel_analysis(data, n_permutations=100)
        parallel_significant = eigenvalues > parallel_eigenvalues
        
        tests['parallel_analysis'] = {
            'significant_components': parallel_significant.sum(),
            'random_eigenvalues': parallel_eigenvalues.to_dict(),
            'interpretation': f"{parallel_significant.sum()} components exceed random data eigenvalues"
        }
        
        # 4. Bartlett's test for equality of remaining eigenvalues
        bartlett_results = self._bartlett_test_eigenvalues(
            eigenvalues.values, len(data)
        )
        tests['bartlett_eigenvalues'] = bartlett_results
        
        # 5. Component loadings significance
        loadings = pca_results['loadings']
        n_obs = len(data)
        
        # Approximate standard errors for loadings
        loading_se = 1 / np.sqrt(n_obs)
        
        loading_tests = {}
        for comp in loadings.columns:
            t_stats = loadings[comp] / loading_se
            p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), n_obs - 1))
            
            loading_tests[comp] = {
                'n_significant': sum(p_values < 0.05),
                'percent_significant': sum(p_values < 0.05) / len(p_values) * 100,
                'max_loading': loadings[comp].abs().max(),
                'dominant_variables': loadings[comp].abs().nlargest(3).index.tolist()
            }
        
        tests['loading_significance'] = loading_tests
        
        # Overall assessment
        consensus_components = min(
            kaiser_significant.sum(),
            bs_significant.sum(),
            parallel_significant.sum()
        )
        
        tests['consensus'] = {
            'recommended_components': consensus_components,
            'variance_explained_by_consensus': 
                pca_results['variance_explained']['cumulative'].iloc[consensus_components-1]
                if consensus_components > 0 else 0
        }
        
        return tests
    
    def _broken_stick_model(self, n_vars: int) -> np.ndarray:
        """Calculate expected eigenvalue proportions under broken stick model.
        
        Args:
            n_vars: Number of variables
            
        Returns:
            Array of expected proportions
        """
        expected = np.zeros(n_vars)
        for i in range(n_vars):
            for j in range(i, n_vars):
                expected[i] += 1.0 / (j + 1)
            expected[i] /= n_vars
        return expected
    
    def _parallel_analysis(self, data: pd.DataFrame,
                          n_permutations: int = 100) -> pd.Series:
        """Perform parallel analysis to determine significant components.
        
        Args:
            data: Original data
            n_permutations: Number of random permutations
            
        Returns:
            Series of 95th percentile eigenvalues from random data
        """
        n_obs, n_vars = data.shape
        random_eigenvalues = []
        
        for _ in range(n_permutations):
            # Generate random data with same shape
            if self.model.standardize:
                random_data = np.random.randn(n_obs, n_vars)
            else:
                # Match mean and variance of original data
                random_data = np.random.randn(n_obs, n_vars)
                random_data = (random_data * data.std().values + data.mean().values)
            
            # Perform PCA on random data
            random_pca = PCA(n_components=min(n_obs, n_vars))
            random_pca.fit(random_data)
            random_eigenvalues.append(random_pca.explained_variance_)
        
        # Calculate 95th percentile for each component
        random_eigenvalues = np.array(random_eigenvalues)
        percentile_95 = np.percentile(random_eigenvalues, 95, axis=0)
        
        # Return as series with same index as actual components
        n_components = len(self.pca.explained_variance_)
        component_names = [f'PC{i+1}' for i in range(n_components)]
        
        return pd.Series(percentile_95[:n_components], index=component_names)
    
    def _bartlett_test_eigenvalues(self, eigenvalues: np.ndarray,
                                  n_obs: int) -> Dict[str, Any]:
        """Bartlett's test for equality of remaining eigenvalues.
        
        Args:
            eigenvalues: Array of eigenvalues
            n_obs: Number of observations
            
        Returns:
            Dictionary with test results
        """
        results = {}
        
        # Test for each possible number of components
        for k in range(len(eigenvalues) - 1):
            remaining = eigenvalues[k+1:]
            if len(remaining) < 2:
                continue
            
            # Calculate test statistic
            mean_remaining = np.mean(remaining)
            log_mean = np.log(mean_remaining)
            mean_log = np.mean(np.log(remaining + 1e-10))  # Add small value to avoid log(0)
            
            n_remaining = len(remaining)
            test_stat = -(n_obs - (2 * len(eigenvalues) + 11) / 6) * \
                       n_remaining * (log_mean - mean_log)
            
            # Degrees of freedom
            df = (n_remaining - 1) * (n_remaining + 2) / 2
            
            # P-value
            p_value = 1 - stats.chi2.cdf(test_stat, df)
            
            results[f'after_PC{k+1}'] = {
                'test_statistic': test_stat,
                'p_value': p_value,
                'reject_equality': p_value < 0.05,
                'interpretation': f"Remaining eigenvalues are {'not ' if p_value >= 0.05 else ''}equal"
            }
        
        return results
    
    def _calculate_component_scores(self, data: pd.DataFrame,
                                  pca_results: Dict[str, Any]) -> pd.DataFrame:
        """Calculate component scores for each observation.
        
        Args:
            data: Original data
            pca_results: PCA results
            
        Returns:
            DataFrame with component scores
        """
        scores = self.pca.transform(data)
        
        component_names = [f'PC{i+1}' for i in range(scores.shape[1])]
        component_scores = pd.DataFrame(
            scores,
            index=data.index,
            columns=component_names
        )
        
        return component_scores
    
    def _calculate_reconstruction_error(self, data: pd.DataFrame,
                                      pca_results: Dict[str, Any]) -> pd.DataFrame:
        """Calculate reconstruction error for each observation.
        
        Args:
            data: Original data
            pca_results: PCA results
            
        Returns:
            DataFrame with reconstruction errors
        """
        # Transform and inverse transform
        scores = self.pca.transform(data)
        reconstructed = self.pca.inverse_transform(scores)
        
        # Calculate errors
        errors = data.values - reconstructed
        
        return pd.DataFrame(
            errors,
            index=data.index,
            columns=data.columns
        )
    
    def _analyze_component_stability(self, data: pd.DataFrame,
                                   pca_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze stability of principal components.
        
        Args:
            data: Original data
            pca_results: PCA results
            
        Returns:
            Dictionary with stability analysis
        """
        stability = {}
        
        # 1. Bootstrap stability
        n_bootstrap = 100
        bootstrap_loadings = []
        
        for _ in range(n_bootstrap):
            # Resample data
            indices = np.random.choice(len(data), len(data), replace=True)
            bootstrap_data = data.iloc[indices]
            
            # Fit PCA
            bootstrap_pca = PCA(n_components=self.pca.n_components_, random_state=42)
            bootstrap_pca.fit(bootstrap_data)
            
            # Align components (handle sign flips)
            aligned_components = self._align_components(
                self.pca.components_, bootstrap_pca.components_
            )
            bootstrap_loadings.append(aligned_components.T)
        
        # Calculate stability metrics
        bootstrap_loadings = np.array(bootstrap_loadings)
        loading_std = np.std(bootstrap_loadings, axis=0)
        loading_cv = loading_std / (np.abs(pca_results['loadings'].values) + 1e-10)
        
        stability['bootstrap'] = {
            'loading_std': pd.DataFrame(
                loading_std,
                index=pca_results['loadings'].index,
                columns=pca_results['loadings'].columns
            ),
            'loading_cv': pd.DataFrame(
                loading_cv,
                index=pca_results['loadings'].index,
                columns=pca_results['loadings'].columns
            ),
            'stable_loadings': (loading_cv < 0.5).sum().to_dict()
        }
        
        # 2. Leave-one-out stability
        loo_angles = []
        for i in range(min(len(data), 50)):  # Limit to 50 for computational efficiency
            loo_data = data.drop(data.index[i])
            loo_pca = PCA(n_components=self.pca.n_components_, random_state=42)
            loo_pca.fit(loo_data)
            
            # Calculate angles between subspaces
            angles = self._subspace_angles(self.pca.components_, loo_pca.components_)
            loo_angles.append(angles)
        
        loo_angles = np.array(loo_angles)
        
        stability['leave_one_out'] = {
            'mean_angles': np.mean(loo_angles, axis=0),
            'max_angles': np.max(loo_angles, axis=0),
            'stable_components': np.sum(np.max(loo_angles, axis=0) < 10)  # Less than 10 degrees
        }
        
        return stability
    
    def _align_components(self, reference: np.ndarray,
                         components: np.ndarray) -> np.ndarray:
        """Align component signs to match reference.
        
        Args:
            reference: Reference components
            components: Components to align
            
        Returns:
            Aligned components
        """
        aligned = components.copy()
        for i in range(len(components)):
            if np.dot(reference[i], components[i]) < 0:
                aligned[i] = -aligned[i]
        return aligned
    
    def _subspace_angles(self, basis1: np.ndarray,
                        basis2: np.ndarray) -> np.ndarray:
        """Calculate principal angles between subspaces.
        
        Args:
            basis1: First basis (components as rows)
            basis2: Second basis (components as rows)
            
        Returns:
            Array of principal angles in degrees
        """
        # Use QR decomposition for numerical stability
        q1, _ = np.linalg.qr(basis1.T)
        q2, _ = np.linalg.qr(basis2.T)
        
        # Calculate principal angles
        cos_angles = np.linalg.svd(q1.T @ q2, compute_uv=False)
        cos_angles = np.clip(cos_angles, -1, 1)
        angles = np.arccos(cos_angles) * 180 / np.pi
        
        return angles[:min(basis1.shape[0], basis2.shape[0])]
    
    def _interpret_components(self, pca_results: Dict[str, Any],
                            variable_names: pd.Index) -> Dict[str, Any]:
        """Interpret principal components based on loadings.
        
        Args:
            pca_results: PCA results
            variable_names: Names of variables
            
        Returns:
            Dictionary with component interpretations
        """
        loadings = pca_results['loadings']
        interpretations = {}
        
        for comp in loadings.columns:
            # Get variables with high loadings (abs > 0.3)
            high_loadings = loadings[loadings[comp].abs() > 0.3][comp]
            high_loadings = high_loadings.sort_values(key=abs, ascending=False)
            
            # Calculate contribution to component
            squared_loadings = loadings[comp] ** 2
            contributions = squared_loadings / squared_loadings.sum()
            
            # Identify positive and negative loadings
            positive_vars = high_loadings[high_loadings > 0]
            negative_vars = high_loadings[high_loadings < 0]
            
            interpretation = {
                'high_positive_loadings': positive_vars.to_dict(),
                'high_negative_loadings': negative_vars.abs().to_dict(),
                'top_contributors': contributions.nlargest(5).to_dict(),
                'variance_explained': pca_results['variance_explained']['individual'][comp],
                'eigenvalue': pca_results['eigenvalues'][comp],
                'suggested_interpretation': self._suggest_component_interpretation(
                    positive_vars, negative_vars, comp
                )
            }
            
            interpretations[comp] = interpretation
        
        return interpretations
    
    def _suggest_component_interpretation(self, positive_vars: pd.Series,
                                        negative_vars: pd.Series,
                                        component_name: str) -> str:
        """Suggest interpretation for a principal component.
        
        Args:
            positive_vars: Variables with positive loadings
            negative_vars: Variables with negative loadings
            component_name: Name of component
            
        Returns:
            Suggested interpretation
        """
        # Component number
        comp_num = int(component_name.replace('PC', ''))
        
        if comp_num == 1:
            # First component often represents overall level
            if len(positive_vars) > len(negative_vars) * 2:
                return "Overall market price level"
            elif len(negative_vars) > len(positive_vars) * 2:
                return "Inverse price pattern"
            else:
                return "Price contrast pattern"
        
        elif comp_num == 2:
            # Second component often represents main contrast
            if len(positive_vars) > 0 and len(negative_vars) > 0:
                return "Market segmentation pattern"
            else:
                return "Secondary price variation"
        
        else:
            # Higher components
            if len(positive_vars) + len(negative_vars) < 3:
                return f"Localized variation (few markets)"
            else:
                return f"Complex interaction pattern {comp_num}"
    
    def diagnose(self, model: PCAModel,
                result: EstimationResult) -> Dict[str, Any]:
        """Run diagnostic tests for PCA.
        
        Args:
            model: PCA model
            result: Estimation results
            
        Returns:
            Dictionary with diagnostic test results
        """
        # Most diagnostics are in significance tests
        diagnostics = result.diagnostics.copy()
        
        # Add reconstruction error analysis
        if hasattr(result, 'residuals') and result.residuals is not None:
            reconstruction_errors = result.residuals
            
            # RMSE by variable
            rmse_by_var = np.sqrt((reconstruction_errors ** 2).mean())
            
            # Outlier detection using reconstruction error
            total_error = (reconstruction_errors ** 2).sum(axis=1)
            error_threshold = total_error.mean() + 3 * total_error.std()
            outliers = total_error[total_error > error_threshold]
            
            diagnostics['reconstruction'] = {
                'rmse_by_variable': rmse_by_var.to_dict(),
                'mean_rmse': rmse_by_var.mean(),
                'n_outliers': len(outliers),
                'outlier_indices': outliers.index.tolist(),
                'outlier_threshold': error_threshold
            }
        
        return diagnostics
    
    def predict(self, model: PCAModel,
               result: EstimationResult,
               new_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate component scores for new data.
        
        Args:
            model: PCA model
            result: Estimation results
            new_data: New observations
            
        Returns:
            Component scores for new data
        """
        # Prepare new data
        prepared_data = model.prepare_data(new_data)
        
        # Standardize if the original model was standardized
        if model.standardize:
            standardized_data = self.scaler.transform(prepared_data)
            data_for_scoring = pd.DataFrame(
                standardized_data,
                index=prepared_data.index,
                columns=prepared_data.columns
            )
        else:
            data_for_scoring = prepared_data
        
        # Calculate scores
        scores = self.pca.transform(data_for_scoring)
        
        component_names = [f'PC{i+1}' for i in range(scores.shape[1])]
        return pd.DataFrame(
            scores,
            index=data_for_scoring.index,
            columns=component_names
        )