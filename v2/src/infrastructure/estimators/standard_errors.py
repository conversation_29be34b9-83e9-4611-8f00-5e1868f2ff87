"""Standard error correction implementations for panel models.

This module provides various robust standard error estimators including
Dr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HAC, clustered, and bootstrap standard errors.
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, Union
from scipy import stats

# Attempt to import linearmodels for advanced panel functionality
try:
    from linearmodels.panel.data import PanelData
    from linearmodels.panel.model import PanelOLS
    LINEARMODELS_AVAILABLE = True
except ImportError:
    LINEARMODELS_AVAILABLE = False
    PanelData = None
    PanelOLS = None

from infrastructure.logging import Logger

logger = Logger(__name__)


class StandardErrorEstimator:
    """Provides various standard error corrections for panel models."""
    
    def __init__(self):
        """Initialize the standard error estimator."""
        if not LINEARMODELS_AVAILABLE:
            logger.warning("linearmodels package not available. Some SE corrections will be limited.")
    
    def driscoll_kraay(self, residuals: np.ndarray,
                      X: np.ndarray,
                      entity_ids: np.ndarray,
                      time_ids: np.ndarray,
                      kernel: str = 'bartlett',
                      bandwidth: Optional[int] = None) -> Dict[str, Any]:
        """Calculate Driscoll-<PERSON>raay standard errors.
        
        Driscoll-Kraay SEs are robust to both heteroskedasticity and 
        cross-sectional/temporal dependence.
        
        Args:
            residuals: Model residuals (n_obs,)
            X: Design matrix (n_obs, k_vars)
            entity_ids: Entity identifiers for each observation
            time_ids: Time identifiers for each observation
            kernel: Kernel for HAC estimation ('bartlett', 'parzen', 'quadratic_spectral')
            bandwidth: Bandwidth for kernel (if None, uses automatic selection)
            
        Returns:
            Dictionary containing:
            - standard_errors: Corrected standard errors
            - variance_matrix: Full variance-covariance matrix
            - bandwidth_used: Actual bandwidth used
        """
        logger.info(f"Calculating Driscoll-Kraay standard errors with {kernel} kernel")
        
        # Get dimensions
        n_obs, k_vars = X.shape
        unique_times = np.unique(time_ids)
        T = len(unique_times)
        
        # Automatic bandwidth selection if not provided
        if bandwidth is None:
            bandwidth = int(np.floor(4 * (T/100)**(2/9)))
            logger.info(f"Using automatic bandwidth selection: {bandwidth}")
        
        # Initialize the meat matrix
        meat = np.zeros((k_vars, k_vars))
        
        # Calculate cross-sectional averages for each time period
        for t in unique_times:
            # Get observations for this time period
            t_mask = time_ids == t
            X_t = X[t_mask]
            resid_t = residuals[t_mask]
            
            # Calculate cross-sectional average of score
            score_t = X_t.T @ resid_t / n_obs
            
            # Add to meat matrix with kernel weights
            for s in unique_times:
                lag = abs(t - s)
                if lag <= bandwidth:
                    # Calculate kernel weight
                    weight = self._kernel_weight(lag, bandwidth, kernel)
                    
                    # Get score for time s
                    s_mask = time_ids == s
                    X_s = X[s_mask]
                    resid_s = residuals[s_mask]
                    score_s = X_s.T @ resid_s / n_obs
                    
                    # Add to meat matrix
                    if t == s:
                        meat += weight * np.outer(score_t, score_s)
                    else:
                        meat += weight * (np.outer(score_t, score_s) + np.outer(score_s, score_t))
        
        # Calculate bread matrix (X'X)^{-1}
        bread = np.linalg.inv(X.T @ X / n_obs)
        
        # Calculate variance-covariance matrix
        vcov = (n_obs / T) * bread @ meat @ bread
        
        # Extract standard errors
        standard_errors = np.sqrt(np.diag(vcov))
        
        return {
            'standard_errors': standard_errors,
            'variance_matrix': vcov,
            'bandwidth_used': bandwidth,
            'kernel': kernel,
            'n_time_periods': T
        }
    
    def clustered(self, residuals: np.ndarray,
                 X: np.ndarray,
                 cluster_ids: np.ndarray,
                 finite_sample_correction: bool = True) -> Dict[str, Any]:
        """Calculate cluster-robust standard errors.
        
        Args:
            residuals: Model residuals (n_obs,)
            X: Design matrix (n_obs, k_vars)
            cluster_ids: Cluster identifiers for each observation
            finite_sample_correction: Whether to apply finite sample correction
            
        Returns:
            Dictionary containing standard errors and variance matrix
        """
        logger.info("Calculating cluster-robust standard errors")
        
        n_obs, k_vars = X.shape
        unique_clusters = np.unique(cluster_ids)
        n_clusters = len(unique_clusters)
        
        # Initialize meat matrix
        meat = np.zeros((k_vars, k_vars))
        
        # Calculate cluster contributions
        for cluster in unique_clusters:
            cluster_mask = cluster_ids == cluster
            X_c = X[cluster_mask]
            resid_c = residuals[cluster_mask]
            
            # Cluster score
            score_c = X_c.T @ resid_c
            
            # Add to meat matrix
            meat += np.outer(score_c, score_c)
        
        # Bread matrix
        bread = np.linalg.inv(X.T @ X)
        
        # Finite sample correction
        if finite_sample_correction:
            correction = (n_clusters / (n_clusters - 1)) * (n_obs / (n_obs - k_vars))
        else:
            correction = 1.0
        
        # Variance-covariance matrix
        vcov = correction * bread @ meat @ bread
        
        # Standard errors
        standard_errors = np.sqrt(np.diag(vcov))
        
        return {
            'standard_errors': standard_errors,
            'variance_matrix': vcov,
            'n_clusters': n_clusters,
            'finite_sample_correction': finite_sample_correction
        }
    
    def heteroskedasticity_robust(self, residuals: np.ndarray,
                                 X: np.ndarray,
                                 hc_type: str = 'HC3') -> Dict[str, Any]:
        """Calculate heteroskedasticity-robust (White) standard errors.
        
        Args:
            residuals: Model residuals
            X: Design matrix
            hc_type: Type of HC estimator ('HC0', 'HC1', 'HC2', 'HC3')
            
        Returns:
            Dictionary containing standard errors and variance matrix
        """
        logger.info(f"Calculating {hc_type} heteroskedasticity-robust standard errors")
        
        n_obs, k_vars = X.shape
        
        # Calculate leverage if needed
        if hc_type in ['HC2', 'HC3']:
            hat_matrix_diag = np.diag(X @ np.linalg.inv(X.T @ X) @ X.T)
        
        # Weight residuals based on HC type
        if hc_type == 'HC0':
            # Basic White standard errors
            weights = residuals ** 2
        elif hc_type == 'HC1':
            # With finite sample correction
            weights = residuals ** 2 * (n_obs / (n_obs - k_vars))
        elif hc_type == 'HC2':
            # Adjust for leverage
            weights = residuals ** 2 / (1 - hat_matrix_diag)
        elif hc_type == 'HC3':
            # More aggressive leverage adjustment
            weights = residuals ** 2 / (1 - hat_matrix_diag) ** 2
        else:
            raise ValueError(f"Unknown HC type: {hc_type}")
        
        # Meat matrix
        weighted_X = X * np.sqrt(weights).reshape(-1, 1)
        meat = weighted_X.T @ weighted_X
        
        # Bread matrix
        bread = np.linalg.inv(X.T @ X)
        
        # Variance-covariance matrix
        vcov = bread @ meat @ bread
        
        # Standard errors
        standard_errors = np.sqrt(np.diag(vcov))
        
        return {
            'standard_errors': standard_errors,
            'variance_matrix': vcov,
            'hc_type': hc_type
        }
    
    def newey_west(self, residuals: np.ndarray,
                  X: np.ndarray,
                  lags: Optional[int] = None) -> Dict[str, Any]:
        """Calculate Newey-West HAC standard errors.
        
        Args:
            residuals: Model residuals
            X: Design matrix
            lags: Number of lags (if None, uses automatic selection)
            
        Returns:
            Dictionary containing standard errors and variance matrix
        """
        logger.info("Calculating Newey-West HAC standard errors")
        
        n_obs, k_vars = X.shape
        
        # Automatic lag selection if not provided
        if lags is None:
            lags = int(np.floor(4 * (n_obs/100)**(2/9)))
            logger.info(f"Using automatic lag selection: {lags}")
        
        # Calculate scores
        scores = X * residuals.reshape(-1, 1)
        
        # Initialize meat matrix with lag 0
        meat = scores.T @ scores
        
        # Add lagged terms
        for lag in range(1, lags + 1):
            # Bartlett kernel weight
            weight = 1 - lag / (lags + 1)
            
            # Lagged cross-products
            for t in range(lag, n_obs):
                score_t = scores[t].reshape(-1, 1)
                score_t_lag = scores[t - lag].reshape(-1, 1)
                
                meat += weight * (score_t @ score_t_lag.T + score_t_lag @ score_t.T)
        
        # Scale by n
        meat = meat / n_obs
        
        # Bread matrix
        bread = np.linalg.inv(X.T @ X / n_obs)
        
        # Variance-covariance matrix
        vcov = n_obs * bread @ meat @ bread
        
        # Standard errors
        standard_errors = np.sqrt(np.diag(vcov))
        
        return {
            'standard_errors': standard_errors,
            'variance_matrix': vcov,
            'lags': lags
        }
    
    def bootstrap(self, y: np.ndarray,
                 X: np.ndarray,
                 entity_ids: Optional[np.ndarray] = None,
                 n_bootstrap: int = 1000,
                 method: str = 'pairs',
                 confidence_level: float = 0.95) -> Dict[str, Any]:
        """Calculate bootstrap standard errors.
        
        Args:
            y: Dependent variable
            X: Design matrix
            entity_ids: Entity identifiers for cluster bootstrap
            n_bootstrap: Number of bootstrap replications
            method: Bootstrap method ('pairs', 'residual', 'cluster')
            confidence_level: Confidence level for intervals
            
        Returns:
            Dictionary containing standard errors and confidence intervals
        """
        logger.info(f"Calculating bootstrap standard errors ({method} method, {n_bootstrap} reps)")
        
        n_obs, k_vars = X.shape
        bootstrap_coefs = np.zeros((n_bootstrap, k_vars))
        
        # Original OLS estimates
        beta_hat = np.linalg.inv(X.T @ X) @ X.T @ y
        residuals = y - X @ beta_hat
        
        for b in range(n_bootstrap):
            if method == 'pairs':
                # Resample observations
                idx = np.random.choice(n_obs, n_obs, replace=True)
                y_b = y[idx]
                X_b = X[idx]
                
            elif method == 'residual':
                # Resample residuals
                resid_b = residuals[np.random.choice(n_obs, n_obs, replace=True)]
                y_b = X @ beta_hat + resid_b
                X_b = X
                
            elif method == 'cluster' and entity_ids is not None:
                # Resample clusters
                unique_entities = np.unique(entity_ids)
                sampled_entities = np.random.choice(
                    unique_entities, len(unique_entities), replace=True
                )
                
                # Build bootstrap sample
                idx = []
                for entity in sampled_entities:
                    idx.extend(np.where(entity_ids == entity)[0])
                
                y_b = y[idx]
                X_b = X[idx]
                
            else:
                raise ValueError(f"Unknown bootstrap method: {method}")
            
            # Calculate bootstrap coefficients
            try:
                bootstrap_coefs[b] = np.linalg.inv(X_b.T @ X_b) @ X_b.T @ y_b
            except np.linalg.LinAlgError:
                # Skip singular matrices
                bootstrap_coefs[b] = np.nan
        
        # Remove failed bootstrap samples
        valid_samples = ~np.isnan(bootstrap_coefs).any(axis=1)
        bootstrap_coefs = bootstrap_coefs[valid_samples]
        
        # Calculate statistics
        standard_errors = np.std(bootstrap_coefs, axis=0)
        
        # Confidence intervals
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        conf_intervals = np.percentile(
            bootstrap_coefs, [lower_percentile, upper_percentile], axis=0
        )
        
        return {
            'standard_errors': standard_errors,
            'confidence_intervals': conf_intervals.T,
            'bootstrap_coefficients': bootstrap_coefs,
            'n_valid_samples': len(bootstrap_coefs),
            'method': method
        }
    
    def _kernel_weight(self, lag: int, bandwidth: int, kernel: str) -> float:
        """Calculate kernel weight for given lag.
        
        Args:
            lag: Lag distance
            bandwidth: Kernel bandwidth
            kernel: Kernel type
            
        Returns:
            Kernel weight
        """
        if kernel == 'bartlett':
            if lag <= bandwidth:
                return 1 - lag / (bandwidth + 1)
            else:
                return 0
                
        elif kernel == 'parzen':
            z = lag / (bandwidth + 1)
            if z <= 0.5:
                return 1 - 6 * z**2 + 6 * z**3
            elif z <= 1:
                return 2 * (1 - z)**3
            else:
                return 0
                
        elif kernel == 'quadratic_spectral':
            if lag == 0:
                return 1
            else:
                x = 6 * np.pi * lag / (5 * (bandwidth + 1))
                return 3 / x**2 * (np.sin(x) / x - np.cos(x))
                
        else:
            raise ValueError(f"Unknown kernel: {kernel}")
    
    def compare_standard_errors(self, beta: np.ndarray,
                              se_dict: Dict[str, np.ndarray],
                              var_names: Optional[List[str]] = None) -> pd.DataFrame:
        """Compare different standard error estimates.
        
        Args:
            beta: Coefficient estimates
            se_dict: Dictionary mapping SE type to standard errors
            var_names: Variable names (optional)
            
        Returns:
            DataFrame comparing standard errors and t-statistics
        """
        if var_names is None:
            var_names = [f'X{i}' for i in range(len(beta))]
        
        # Build comparison DataFrame
        comparison = pd.DataFrame(index=var_names)
        comparison['Coefficient'] = beta
        
        for se_type, se_values in se_dict.items():
            comparison[f'SE_{se_type}'] = se_values
            comparison[f't_stat_{se_type}'] = beta / se_values
            comparison[f'p_value_{se_type}'] = 2 * (1 - stats.norm.cdf(np.abs(beta / se_values)))
        
        return comparison