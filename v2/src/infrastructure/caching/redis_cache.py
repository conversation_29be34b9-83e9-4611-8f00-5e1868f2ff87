"""Redis cache implementation."""

import json
import pickle
from typing import Any, Optional

import redis.asyncio as redis

from ...application.interfaces import Cache


class RedisCache(Cache):
    """Redis implementation of cache interface."""
    
    def __init__(
        self,
        redis_url: str = "redis://localhost:6379",
        default_ttl: int = 3600,
        key_prefix: str = "yemen_market:"
    ):
        """Initialize Redis cache."""
        self.redis_url = redis_url
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        self._client: Optional[redis.Redis] = None
    
    async def _get_client(self) -> redis.Redis:
        """Get or create Redis client."""
        if not self._client:
            self._client = await redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=False
            )
        return self._client
    
    def _make_key(self, key: str) -> str:
        """Create prefixed key."""
        return f"{self.key_prefix}{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        client = await self._get_client()
        prefixed_key = self._make_key(key)
        
        value = await client.get(prefixed_key)
        if value is None:
            return None
        
        try:
            # Try to deserialize as JSON first
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            # Fall back to pickle for complex objects
            return pickle.loads(value)
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with optional TTL in seconds."""
        client = await self._get_client()
        prefixed_key = self._make_key(key)
        ttl = ttl or self.default_ttl
        
        try:
            # Try to serialize as JSON first (more portable)
            serialized = json.dumps(value)
        except (TypeError, ValueError):
            # Fall back to pickle for complex objects
            serialized = pickle.dumps(value)
        
        await client.setex(prefixed_key, ttl, serialized)
    
    async def delete(self, key: str) -> None:
        """Delete value from cache."""
        client = await self._get_client()
        prefixed_key = self._make_key(key)
        await client.delete(prefixed_key)
    
    async def clear(self) -> None:
        """Clear all cache entries with our prefix."""
        client = await self._get_client()
        
        # Use SCAN to find all keys with our prefix
        cursor = 0
        pattern = f"{self.key_prefix}*"
        
        while True:
            cursor, keys = await client.scan(
                cursor=cursor,
                match=pattern,
                count=100
            )
            
            if keys:
                await client.delete(*keys)
            
            if cursor == 0:
                break
    
    async def close(self) -> None:
        """Close Redis connection."""
        if self._client:
            await self._client.close()
            self._client = None