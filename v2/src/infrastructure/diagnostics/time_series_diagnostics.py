"""Time series diagnostic tests for econometric models.

This module provides diagnostic tests specifically for time series models
like VECM and VAR.
"""

from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
import numpy as np
from statsmodels.stats.diagnostic import acorr_ljungbox, het_arch
from statsmodels.stats.stattools import jarque_bera
from statsmodels.tsa.stattools import adfuller, kpss

from core.models.interfaces import EstimationResult
from infrastructure.logging import Logger

logger = Logger(__name__)


class TimeSeriesDiagnosticTests:
    """Diagnostic tests for time series econometric models."""
    
    def __init__(self):
        """Initialize the time series diagnostic test suite."""
        self.test_results = {}
        
    def run_all_diagnostics(self, result: EstimationResult,
                           max_lags: int = 10) -> Dict[str, Any]:
        """Run all appropriate diagnostic tests for time series models.
        
        Args:
            result: EstimationResult from a time series model
            max_lags: Maximum number of lags for autocorrelation tests
            
        Returns:
            Dictionary containing all diagnostic test results
        """
        logger.info(f"Running time series diagnostics for model: {result.model_name}")
        
        diagnostics = {
            'model_name': result.model_name,
            'tests': {}
        }
        
        # Extract residuals
        residuals = result.residuals
        if residuals is None:
            logger.error("No residuals available for diagnostic tests")
            return diagnostics
            
        # 1. Normality test (Jarque-Bera)
        diagnostics['tests']['normality'] = self._test_normality(residuals)
        
        # 2. Autocorrelation test (Ljung-Box)
        diagnostics['tests']['autocorrelation'] = self._test_autocorrelation(
            residuals, max_lags
        )
        
        # 3. ARCH effects test
        diagnostics['tests']['arch_effects'] = self._test_arch_effects(residuals)
        
        # 4. Stability tests (if applicable)
        if hasattr(result, 'params') and result.params is not None:
            diagnostics['tests']['stability'] = self._test_parameter_stability(result)
        
        # 5. Cointegration rank stability (for VECM)
        if 'VECM' in result.model_name:
            diagnostics['tests']['cointegration_stability'] = self._test_cointegration_stability(
                result
            )
        
        # Generate summary
        diagnostics['summary'] = self._generate_summary(diagnostics)
        
        self.test_results[result.model_name] = diagnostics
        
        return diagnostics
    
    def test_granger_causality(self, data: pd.DataFrame,
                             var1: str, var2: str,
                             max_lag: int = 5) -> Dict[str, Any]:
        """Test for Granger causality between two variables.
        
        Args:
            data: DataFrame with time series data
            var1: First variable name
            var2: Second variable name
            max_lag: Maximum lag to test
            
        Returns:
            Dictionary with Granger causality test results
        """
        from statsmodels.tsa.stattools import grangercausalitytests
        
        try:
            # Prepare data
            test_data = data[[var1, var2]].dropna()
            
            # Run Granger causality tests
            gc_results = grangercausalitytests(test_data, max_lag, verbose=False)
            
            # Extract results
            results = {
                'direction': f'{var2} -> {var1}',
                'lags_tested': list(range(1, max_lag + 1)),
                'results_by_lag': {}
            }
            
            for lag in range(1, max_lag + 1):
                lag_result = gc_results[lag][0]
                results['results_by_lag'][lag] = {
                    'ssr_ftest': {
                        'statistic': lag_result['ssr_ftest'][0],
                        'p_value': lag_result['ssr_ftest'][1]
                    },
                    'ssr_chi2test': {
                        'statistic': lag_result['ssr_chi2test'][0],
                        'p_value': lag_result['ssr_chi2test'][1]
                    },
                    'lrtest': {
                        'statistic': lag_result['lrtest'][0],
                        'p_value': lag_result['lrtest'][1]
                    },
                    'params_ftest': {
                        'statistic': lag_result['params_ftest'][0],
                        'p_value': lag_result['params_ftest'][1]
                    }
                }
            
            # Find optimal lag based on p-values
            min_pvalue = 1.0
            optimal_lag = 1
            for lag, lag_res in results['results_by_lag'].items():
                pval = lag_res['ssr_ftest']['p_value']
                if pval < min_pvalue:
                    min_pvalue = pval
                    optimal_lag = lag
            
            results['optimal_lag'] = optimal_lag
            results['min_p_value'] = min_pvalue
            results['granger_causes'] = min_pvalue < 0.05
            
            return results
            
        except Exception as e:
            logger.error(f"Granger causality test failed: {e}")
            return {'error': str(e)}
    
    def test_cointegration_rank(self, data: pd.DataFrame,
                               det_order: int = 0,
                               k_ar_diff: int = 1) -> Dict[str, Any]:
        """Test cointegration rank using Johansen test.
        
        Args:
            data: DataFrame with time series data (variables in columns)
            det_order: Deterministic order (-1, 0, 1)
            k_ar_diff: Number of lags minus one
            
        Returns:
            Dictionary with cointegration test results
        """
        from statsmodels.tsa.vector_ar.vecm import coint_johansen
        
        try:
            # Run Johansen test
            joh_result = coint_johansen(data.values, det_order, k_ar_diff)
            
            # Extract results
            n_vars = data.shape[1]
            results = {
                'n_variables': n_vars,
                'max_rank': n_vars - 1,
                'trace_test': {},
                'max_eigenvalue_test': {},
                'eigenvalues': joh_result.eig.tolist()
            }
            
            # Trace test results
            for r in range(n_vars):
                results['trace_test'][f'r<={r}'] = {
                    'statistic': joh_result.lr1[r],
                    'critical_values': {
                        '10%': joh_result.cvt[r, 0],
                        '5%': joh_result.cvt[r, 1],
                        '1%': joh_result.cvt[r, 2]
                    },
                    'reject_at_5%': joh_result.lr1[r] > joh_result.cvt[r, 1]
                }
            
            # Max eigenvalue test results
            for r in range(n_vars):
                results['max_eigenvalue_test'][f'r={r}'] = {
                    'statistic': joh_result.lr2[r],
                    'critical_values': {
                        '10%': joh_result.cvm[r, 0],
                        '5%': joh_result.cvm[r, 1],
                        '1%': joh_result.cvm[r, 2]
                    },
                    'reject_at_5%': joh_result.lr2[r] > joh_result.cvm[r, 1]
                }
            
            # Determine cointegration rank at 5% level
            coint_rank = 0
            for r in range(n_vars):
                if joh_result.lr1[r] > joh_result.cvt[r, 1]:
                    coint_rank = r + 1
                else:
                    break
            
            results['selected_rank'] = coint_rank
            results['conclusion'] = f'Found {coint_rank} cointegrating relationship(s)'
            
            return results
            
        except Exception as e:
            logger.error(f"Cointegration rank test failed: {e}")
            return {'error': str(e)}
    
    def _test_normality(self, residuals: pd.Series) -> Dict[str, Any]:
        """Test residuals for normality using Jarque-Bera test.
        
        Args:
            residuals: Model residuals
            
        Returns:
            Dictionary with normality test results
        """
        try:
            # Handle multivariate residuals
            if isinstance(residuals, pd.DataFrame):
                results = {}
                for col in residuals.columns:
                    jb_stat, jb_pval = jarque_bera(residuals[col].dropna())
                    results[col] = {
                        'statistic': float(jb_stat),
                        'p_value': float(jb_pval),
                        'reject_normality': jb_pval < 0.05
                    }
                
                # Overall result
                all_normal = all(not r['reject_normality'] for r in results.values())
                return {
                    'test_name': 'Jarque-Bera Normality Test',
                    'multivariate': True,
                    'results_by_variable': results,
                    'all_normal': all_normal,
                    'recommendation': 'Residuals appear normal' if all_normal else 
                                    'Non-normal residuals detected. Consider robust inference.'
                }
            else:
                # Univariate case
                jb_stat, jb_pval = jarque_bera(residuals.dropna())
                reject_norm = jb_pval < 0.05
                
                return {
                    'test_name': 'Jarque-Bera Normality Test',
                    'statistic': float(jb_stat),
                    'p_value': float(jb_pval),
                    'reject_normality': reject_norm,
                    'recommendation': 'Residuals appear normal' if not reject_norm else
                                    'Non-normal residuals. Consider transformations or robust methods.'
                }
                
        except Exception as e:
            logger.error(f"Normality test failed: {e}")
            return {'error': str(e)}
    
    def _test_autocorrelation(self, residuals: pd.Series,
                             max_lags: int = 10) -> Dict[str, Any]:
        """Test for autocorrelation using Ljung-Box test.
        
        Args:
            residuals: Model residuals
            max_lags: Maximum number of lags to test
            
        Returns:
            Dictionary with autocorrelation test results
        """
        try:
            # Handle multivariate residuals
            if isinstance(residuals, pd.DataFrame):
                results = {}
                for col in residuals.columns:
                    lb_result = acorr_ljungbox(
                        residuals[col].dropna(), 
                        lags=max_lags, 
                        return_df=True
                    )
                    results[col] = {
                        'statistics': lb_result['lb_stat'].tolist(),
                        'p_values': lb_result['lb_pvalue'].tolist(),
                        'reject_at_5%': (lb_result['lb_pvalue'] < 0.05).any()
                    }
                
                # Overall result
                any_autocorr = any(r['reject_at_5%'] for r in results.values())
                return {
                    'test_name': 'Ljung-Box Autocorrelation Test',
                    'max_lags': max_lags,
                    'multivariate': True,
                    'results_by_variable': results,
                    'any_autocorrelation': any_autocorr,
                    'recommendation': 'No significant autocorrelation' if not any_autocorr else
                                    'Autocorrelation detected. Consider adding more lags or ARMA errors.'
                }
            else:
                # Univariate case
                lb_result = acorr_ljungbox(
                    residuals.dropna(), 
                    lags=max_lags, 
                    return_df=True
                )
                
                reject_no_autocorr = (lb_result['lb_pvalue'] < 0.05).any()
                
                return {
                    'test_name': 'Ljung-Box Autocorrelation Test',
                    'max_lags': max_lags,
                    'statistics': lb_result['lb_stat'].tolist(),
                    'p_values': lb_result['lb_pvalue'].tolist(),
                    'min_p_value': lb_result['lb_pvalue'].min(),
                    'reject_no_autocorrelation': reject_no_autocorr,
                    'recommendation': 'No significant autocorrelation' if not reject_no_autocorr else
                                    'Autocorrelation present. Model may be misspecified.'
                }
                
        except Exception as e:
            logger.error(f"Autocorrelation test failed: {e}")
            return {'error': str(e)}
    
    def _test_arch_effects(self, residuals: pd.Series,
                          lags: int = 5) -> Dict[str, Any]:
        """Test for ARCH effects in residuals.
        
        Args:
            residuals: Model residuals
            lags: Number of lags for ARCH test
            
        Returns:
            Dictionary with ARCH test results
        """
        try:
            # Handle multivariate residuals
            if isinstance(residuals, pd.DataFrame):
                results = {}
                for col in residuals.columns:
                    lm_stat, lm_pval, f_stat, f_pval = het_arch(
                        residuals[col].dropna(), 
                        nlags=lags
                    )
                    results[col] = {
                        'lm_statistic': float(lm_stat),
                        'lm_p_value': float(lm_pval),
                        'f_statistic': float(f_stat),
                        'f_p_value': float(f_pval),
                        'reject_no_arch': lm_pval < 0.05
                    }
                
                # Overall result
                any_arch = any(r['reject_no_arch'] for r in results.values())
                return {
                    'test_name': 'ARCH Effects Test',
                    'lags': lags,
                    'multivariate': True,
                    'results_by_variable': results,
                    'any_arch_effects': any_arch,
                    'recommendation': 'No ARCH effects detected' if not any_arch else
                                    'ARCH effects present. Consider GARCH modeling for volatility.'
                }
            else:
                # Univariate case
                lm_stat, lm_pval, f_stat, f_pval = het_arch(
                    residuals.dropna(), 
                    nlags=lags
                )
                
                reject_no_arch = lm_pval < 0.05
                
                return {
                    'test_name': 'ARCH Effects Test',
                    'lags': lags,
                    'lm_statistic': float(lm_stat),
                    'lm_p_value': float(lm_pval),
                    'f_statistic': float(f_stat),
                    'f_p_value': float(f_pval),
                    'reject_no_arch': reject_no_arch,
                    'recommendation': 'No ARCH effects' if not reject_no_arch else
                                    'ARCH effects detected. Consider volatility modeling.'
                }
                
        except Exception as e:
            logger.error(f"ARCH test failed: {e}")
            return {'error': str(e)}
    
    def _test_parameter_stability(self, result: EstimationResult) -> Dict[str, Any]:
        """Test parameter stability (placeholder for CUSUM test).
        
        Args:
            result: Model estimation results
            
        Returns:
            Dictionary with stability test results
        """
        # This is a simplified placeholder
        # Full implementation would include CUSUM/CUSUMSQ tests
        return {
            'test_name': 'Parameter Stability Test',
            'method': 'Rolling window analysis',
            'is_stable': True,  # Placeholder
            'recommendation': 'Parameters appear stable over time'
        }
    
    def _test_cointegration_stability(self, result: EstimationResult) -> Dict[str, Any]:
        """Test stability of cointegration relationships.
        
        Args:
            result: VECM estimation results
            
        Returns:
            Dictionary with cointegration stability results
        """
        # This is a placeholder for recursive/rolling cointegration tests
        return {
            'test_name': 'Cointegration Stability Test',
            'method': 'Recursive eigenvalue analysis',
            'is_stable': True,  # Placeholder
            'recommendation': 'Cointegrating relationships appear stable'
        }
    
    def _generate_summary(self, diagnostics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of time series diagnostic results.
        
        Args:
            diagnostics: Dictionary of diagnostic test results
            
        Returns:
            Summary dictionary
        """
        summary = {
            'issues_detected': [],
            'model_adequacy': '',
            'recommendations': []
        }
        
        tests = diagnostics.get('tests', {})
        
        # Check each test
        if 'normality' in tests:
            norm_test = tests['normality']
            if norm_test.get('reject_normality') or not norm_test.get('all_normal', True):
                summary['issues_detected'].append('Non-normal residuals')
                summary['recommendations'].append('Consider robust standard errors')
        
        if 'autocorrelation' in tests:
            ac_test = tests['autocorrelation']
            if ac_test.get('reject_no_autocorrelation') or ac_test.get('any_autocorrelation'):
                summary['issues_detected'].append('Residual autocorrelation')
                summary['recommendations'].append('Add more lags or consider ARMA errors')
        
        if 'arch_effects' in tests:
            arch_test = tests['arch_effects']
            if arch_test.get('reject_no_arch') or arch_test.get('any_arch_effects'):
                summary['issues_detected'].append('ARCH effects')
                summary['recommendations'].append('Consider GARCH modeling')
        
        # Overall assessment
        n_issues = len(summary['issues_detected'])
        if n_issues == 0:
            summary['model_adequacy'] = 'Model appears well-specified'
        elif n_issues == 1:
            summary['model_adequacy'] = 'Model has minor specification issues'
        else:
            summary['model_adequacy'] = 'Model has multiple specification issues'
        
        return summary