"""Panel diagnostic tests for econometric models.

This module provides a high-level interface for running panel diagnostic tests
on econometric model results.
"""

from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

from core.models.interfaces import EstimationResult
from infrastructure.logging import Logger
from .test_implementations import (
    wooldridge_serial_correlation,
    pesaran_cd_test,
    ips_unit_root_test,
    modified_wald_heteroskedasticity,
    breusch_pagan_lm_test,
    ramsey_reset_test,
    chow_structural_break_test,
    quandt_likelihood_ratio_test
)

logger = Logger(__name__)


class PanelDiagnosticTests:
    """Runs comprehensive diagnostic tests for panel data models."""
    
    def __init__(self):
        """Initialize the diagnostic test suite."""
        self.test_results = {}
        
    def run_all_diagnostics(self, result: EstimationResult) -> Dict[str, Any]:
        """Run all appropriate diagnostic tests for the given model.
        
        Args:
            result: EstimationResult from a panel model estimation
            
        Returns:
            Dictionary containing all diagnostic test results
        """
        logger.info(f"Running diagnostic tests for model: {result.model_name}")
        
        diagnostics = {
            'model_name': result.model_name,
            'tests': {}
        }
        
        # Extract needed data
        residuals = result.residuals
        panel_info = self._extract_panel_info(result)
        
        # 1. Test for serial correlation (Wooldridge test)
        if residuals is not None and panel_info is not None:
            try:
                f_stat, p_value, recommendation = wooldridge_serial_correlation(
                    residuals, panel_info
                )
                diagnostics['tests']['serial_correlation'] = {
                    'test_name': 'Wooldridge Serial Correlation Test',
                    'statistic': f_stat,
                    'p_value': p_value,
                    'recommendation': recommendation,
                    'reject_null': p_value < 0.05 if not np.isnan(p_value) else None
                }
            except Exception as e:
                logger.error(f"Wooldridge test failed: {e}")
                diagnostics['tests']['serial_correlation'] = {
                    'error': str(e)
                }
        
        # 2. Test for cross-sectional dependence (Pesaran CD)
        if residuals is not None and panel_info is not None:
            try:
                cd_stat, p_value, recommendation = pesaran_cd_test(
                    residuals, panel_info
                )
                diagnostics['tests']['cross_sectional_dependence'] = {
                    'test_name': 'Pesaran CD Test',
                    'statistic': cd_stat,
                    'p_value': p_value,
                    'recommendation': recommendation,
                    'reject_null': p_value < 0.05 if not np.isnan(p_value) else None
                }
            except Exception as e:
                logger.error(f"Pesaran CD test failed: {e}")
                diagnostics['tests']['cross_sectional_dependence'] = {
                    'error': str(e)
                }
        
        # 3. Test for heteroskedasticity (Modified Wald)
        if residuals is not None and panel_info is not None:
            try:
                chi2_stat, p_value, recommendation = modified_wald_heteroskedasticity(
                    residuals, panel_info, result.fitted_values
                )
                diagnostics['tests']['heteroskedasticity'] = {
                    'test_name': 'Modified Wald Test for Groupwise Heteroskedasticity',
                    'statistic': chi2_stat,
                    'p_value': p_value,
                    'recommendation': recommendation,
                    'reject_null': p_value < 0.05 if not np.isnan(p_value) else None
                }
            except Exception as e:
                logger.error(f"Modified Wald test failed: {e}")
                diagnostics['tests']['heteroskedasticity'] = {
                    'error': str(e)
                }
        
        # 4. Functional form test (RESET) - only if we have fitted values and X
        if (result.fitted_values is not None and 
            hasattr(result, 'X') and result.X is not None and
            hasattr(result, 'y') and result.y is not None):
            try:
                f_stat, p_value, recommendation = ramsey_reset_test(
                    result.y, result.X, result.fitted_values
                )
                diagnostics['tests']['functional_form'] = {
                    'test_name': 'Ramsey RESET Test',
                    'statistic': f_stat,
                    'p_value': p_value,
                    'recommendation': recommendation,
                    'reject_null': p_value < 0.05 if not np.isnan(p_value) else None
                }
            except Exception as e:
                logger.error(f"RESET test failed: {e}")
                diagnostics['tests']['functional_form'] = {
                    'error': str(e)
                }
        
        # 5. Apply corrections if needed
        diagnostics['corrections_applied'] = self._apply_corrections(result, diagnostics)
        
        # 6. Generate summary
        diagnostics['summary'] = self._generate_summary(diagnostics)
        
        self.test_results[result.model_name] = diagnostics
        
        return diagnostics
    
    def run_unit_root_tests(self, data: pd.DataFrame, 
                           variables: List[str]) -> Dict[str, Any]:
        """Run unit root tests on specified variables.
        
        Args:
            data: Panel data DataFrame with MultiIndex (entity, time)
            variables: List of variable names to test
            
        Returns:
            Dictionary of unit root test results
        """
        results = {}
        
        # Extract panel info from data
        panel_info = {
            'entity_id': data.index.names[0],
            'time_id': data.index.names[1],
            'entities': data.index.get_level_values(0).unique().tolist(),
            'N': data.index.get_level_values(0).nunique()
        }
        
        for var in variables:
            if var not in data.columns:
                logger.warning(f"Variable {var} not found in data")
                continue
                
            try:
                ips_stat, p_value, recommendation = ips_unit_root_test(
                    data[var], panel_info
                )
                results[var] = {
                    'test_name': 'Im-Pesaran-Shin Unit Root Test',
                    'statistic': ips_stat,
                    'p_value': p_value,
                    'recommendation': recommendation,
                    'reject_unit_root': p_value < 0.05 if not np.isnan(p_value) else None
                }
            except Exception as e:
                logger.error(f"IPS test failed for {var}: {e}")
                results[var] = {'error': str(e)}
        
        return results
    
    def test_structural_breaks(self, data: pd.DataFrame, 
                             formula: str,
                             known_break_date: Optional[str] = None) -> Dict[str, Any]:
        """Test for structural breaks in the model.
        
        Args:
            data: Panel data DataFrame
            formula: Model formula (e.g., 'price ~ conflict + exchange_rate')
            known_break_date: If provided, runs Chow test; otherwise runs QLR test
            
        Returns:
            Dictionary with structural break test results
        """
        if known_break_date:
            # Chow test for known break date
            try:
                f_stat, p_value, recommendation = chow_structural_break_test(
                    data, formula, known_break_date
                )
                return {
                    'test_name': 'Chow Structural Break Test',
                    'break_date': known_break_date,
                    'statistic': f_stat,
                    'p_value': p_value,
                    'recommendation': recommendation,
                    'reject_stability': p_value < 0.05 if not np.isnan(p_value) else None
                }
            except Exception as e:
                logger.error(f"Chow test failed: {e}")
                return {'error': str(e)}
        else:
            # QLR test for unknown break date
            try:
                max_f_stat, break_date, p_value, recommendation = quandt_likelihood_ratio_test(
                    data, formula
                )
                return {
                    'test_name': 'Quandt Likelihood Ratio Test',
                    'estimated_break_date': break_date,
                    'statistic': max_f_stat,
                    'p_value': p_value,
                    'recommendation': recommendation,
                    'reject_stability': p_value < 0.05 if not np.isnan(p_value) else None
                }
            except Exception as e:
                logger.error(f"QLR test failed: {e}")
                return {'error': str(e)}
    
    def _extract_panel_info(self, result: EstimationResult) -> Optional[Dict[str, Any]]:
        """Extract panel structure information from estimation results.
        
        Args:
            result: EstimationResult object
            
        Returns:
            Dictionary with panel information or None if extraction fails
        """
        try:
            # Try to get panel info from various sources
            if hasattr(result, 'panel_info') and result.panel_info:
                return result.panel_info
            
            # Try to infer from residuals index
            if result.residuals is not None and isinstance(result.residuals.index, pd.MultiIndex):
                return {
                    'entity_id': result.residuals.index.names[0],
                    'time_id': result.residuals.index.names[1],
                    'N': result.residuals.index.get_level_values(0).nunique(),
                    'T': result.residuals.index.get_level_values(1).nunique(),
                    'nobs': len(result.residuals),
                    'entities': result.residuals.index.get_level_values(0).unique().tolist()
                }
            
            # Try from metadata
            if hasattr(result, 'metadata') and result.metadata:
                meta = result.metadata
                return {
                    'entity_id': meta.get('entity_col', 'entity'),
                    'time_id': meta.get('time_col', 'time'),
                    'N': meta.get('n_entities'),
                    'T': meta.get('n_periods'),
                    'nobs': meta.get('n_obs')
                }
            
            logger.warning("Could not extract panel information from results")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting panel info: {e}")
            return None
    
    def _apply_corrections(self, result: EstimationResult, 
                          diagnostics: Dict[str, Any]) -> Dict[str, bool]:
        """Apply corrections based on diagnostic test results.
        
        Args:
            result: EstimationResult to potentially modify
            diagnostics: Dictionary of diagnostic test results
            
        Returns:
            Dictionary indicating which corrections were applied
        """
        corrections = {
            'driscoll_kraay': False,
            'clustered_se': False,
            'robust_se': False
        }
        
        tests = diagnostics.get('tests', {})
        
        # Check for serial correlation and cross-sectional dependence
        serial_corr = tests.get('serial_correlation', {}).get('reject_null', False)
        cross_dep = tests.get('cross_sectional_dependence', {}).get('reject_null', False)
        hetero = tests.get('heteroskedasticity', {}).get('reject_null', False)
        
        # Apply Driscoll-Kraay if both serial correlation and cross-sectional dependence
        if serial_corr and cross_dep:
            logger.info("Applying Driscoll-Kraay standard errors due to serial correlation and cross-sectional dependence")
            corrections['driscoll_kraay'] = True
            # Note: Actual correction would be applied here if we had access to the model
            
        # Apply clustered SEs if only serial correlation
        elif serial_corr:
            logger.info("Applying clustered standard errors due to serial correlation")
            corrections['clustered_se'] = True
            
        # Apply robust SEs if heteroskedasticity
        elif hetero:
            logger.info("Applying robust standard errors due to heteroskedasticity")
            corrections['robust_se'] = True
        
        return corrections
    
    def _generate_summary(self, diagnostics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary of diagnostic test results.
        
        Args:
            diagnostics: Dictionary of diagnostic test results
            
        Returns:
            Summary dictionary with key findings and recommendations
        """
        summary = {
            'problems_detected': [],
            'corrections_needed': [],
            'overall_assessment': ''
        }
        
        tests = diagnostics.get('tests', {})
        
        # Check each test result
        for test_name, test_result in tests.items():
            if isinstance(test_result, dict) and test_result.get('reject_null'):
                summary['problems_detected'].append(test_name)
                
                if test_name == 'serial_correlation':
                    summary['corrections_needed'].append('Use clustered or Driscoll-Kraay standard errors')
                elif test_name == 'cross_sectional_dependence':
                    summary['corrections_needed'].append('Use Driscoll-Kraay standard errors or spatial models')
                elif test_name == 'heteroskedasticity':
                    summary['corrections_needed'].append('Use robust standard errors')
                elif test_name == 'functional_form':
                    summary['corrections_needed'].append('Consider non-linear transformations or interaction terms')
        
        # Overall assessment
        n_problems = len(summary['problems_detected'])
        if n_problems == 0:
            summary['overall_assessment'] = 'Model passes all diagnostic tests. Standard inference is appropriate.'
        elif n_problems == 1:
            summary['overall_assessment'] = f'Model has {n_problems} issue that should be addressed.'
        else:
            summary['overall_assessment'] = f'Model has {n_problems} issues that should be addressed.'
        
        return summary