"""PostgreSQL implementation of price repository."""

from datetime import datetime
from decimal import Decimal
from typing import List
from uuid import UUID

import asyncpg

from .....core.domain.market.entities import PriceObservation
from .....core.domain.market.repositories import PriceRepository
from .....core.domain.market.value_objects import MarketId, Commodity, Price


class PostgresPriceRepository(PriceRepository):
    """PostgreSQL implementation of price repository."""
    
    def __init__(self, connection: asyncpg.Connection):
        """Initialize with database connection."""
        self.connection = connection
    
    async def find_by_market_and_commodity(
        self,
        market_id: MarketId,
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for a market and commodity within date range."""
        rows = await self.connection.fetch(
            """
            SELECT po.id, po.market_id, po.commodity_code, po.price_amount,
                   po.price_currency, po.price_unit, po.observed_date,
                   po.source, po.quality, po.observations_count,
                   c.name as commodity_name, c.category, c.standard_unit
            FROM price_observations po
            JOIN commodities c ON po.commodity_code = c.code
            WHERE po.market_id = $1
              AND po.commodity_code = $2
              AND po.observed_date >= $3
              AND po.observed_date <= $4
            ORDER BY po.observed_date
            """,
            market_id.value,
            commodity.code,
            start_date,
            end_date
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def find_by_markets_and_commodity(
        self,
        market_ids: List[MarketId],
        commodity: Commodity,
        start_date: datetime,
        end_date: datetime
    ) -> List[PriceObservation]:
        """Find price observations for multiple markets and a commodity."""
        rows = await self.connection.fetch(
            """
            SELECT po.id, po.market_id, po.commodity_code, po.price_amount,
                   po.price_currency, po.price_unit, po.observed_date,
                   po.source, po.quality, po.observations_count,
                   c.name as commodity_name, c.category, c.standard_unit
            FROM price_observations po
            JOIN commodities c ON po.commodity_code = c.code
            WHERE po.market_id = ANY($1::text[])
              AND po.commodity_code = $2
              AND po.observed_date >= $3
              AND po.observed_date <= $4
            ORDER BY po.market_id, po.observed_date
            """,
            [mid.value for mid in market_ids],
            commodity.code,
            start_date,
            end_date
        )
        
        return [self._to_domain(row) for row in rows]
    
    async def save(self, observation: PriceObservation) -> None:
        """Save price observation."""
        await self.connection.execute(
            """
            INSERT INTO price_observations (
                id, market_id, commodity_code, price_amount, price_currency,
                price_unit, observed_date, source, quality, observations_count
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            ON CONFLICT (market_id, commodity_code, observed_date, source) 
            DO UPDATE SET
                price_amount = EXCLUDED.price_amount,
                price_currency = EXCLUDED.price_currency,
                price_unit = EXCLUDED.price_unit,
                quality = EXCLUDED.quality,
                observations_count = EXCLUDED.observations_count
            """,
            observation.id,
            observation.market_id.value,
            observation.commodity.code,
            observation.price.amount,
            observation.price.currency,
            observation.price.unit,
            observation.observed_date,
            observation.source,
            observation.quality,
            observation.observations_count
        )
    
    async def save_batch(self, observations: List[PriceObservation]) -> None:
        """Save multiple price observations."""
        # Prepare data for batch insert
        data = [
            (
                obs.id,
                obs.market_id.value,
                obs.commodity.code,
                obs.price.amount,
                obs.price.currency,
                obs.price.unit,
                obs.observed_date,
                obs.source,
                obs.quality,
                obs.observations_count
            )
            for obs in observations
        ]
        
        # Use COPY for efficient batch insert
        await self.connection.copy_records_to_table(
            'price_observations',
            records=data,
            columns=[
                'id', 'market_id', 'commodity_code', 'price_amount',
                'price_currency', 'price_unit', 'observed_date', 'source',
                'quality', 'observations_count'
            ]
        )
    
    async def delete_by_date_range(
        self,
        market_id: MarketId,
        start_date: datetime,
        end_date: datetime
    ) -> int:
        """Delete observations within date range. Returns count of deleted records."""
        result = await self.connection.execute(
            """
            DELETE FROM price_observations
            WHERE market_id = $1
              AND observed_date >= $2
              AND observed_date <= $3
            """,
            market_id.value,
            start_date,
            end_date
        )
        
        # Extract count from result
        count_str = result.split()[-1]  # "DELETE n"
        return int(count_str)
    
    def _to_domain(self, row: asyncpg.Record) -> PriceObservation:
        """Convert database row to domain entity."""
        # Create commodity from row data
        commodity = Commodity(
            code=row['commodity_code'],
            name=row['commodity_name'],
            category=row['category'],
            standard_unit=row['standard_unit']
        )
        
        # Create price value object
        price = Price(
            amount=Decimal(str(row['price_amount'])),
            currency=row['price_currency'],
            unit=row['price_unit']
        )
        
        # Create observation
        observation = PriceObservation(
            market_id=MarketId(row['market_id']),
            commodity=commodity,
            price=price,
            observed_date=row['observed_date'],
            source=row['source'],
            quality=row['quality'],
            observations_count=row['observations_count']
        )
        
        # Set internal ID
        observation.id = row['id']
        
        return observation