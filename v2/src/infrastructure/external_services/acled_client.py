"""ACLED (Armed Conflict Location & Event Data) API client."""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx

from ...core.domain.conflict.entities import ConflictEvent
from ...core.domain.conflict.value_objects import ConflictType, ConflictIntensity
from ...core.domain.market.value_objects import Coordinates


class ACLEDClient:
    """Client for ACLED API."""
    
    BASE_URL = "https://api.acleddata.com/acled/read"
    
    def __init__(self, api_key: str, email: str):
        """Initialize ACLED client with credentials."""
        self.api_key = api_key
        self.email = email
    
    async def get_conflict_events(
        self,
        country: str = "Yemen",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        event_types: Optional[List[str]] = None,
        limit: int = 5000
    ) -> List[Dict[str, Any]]:
        """Fetch conflict events from ACLED API."""
        params = {
            "key": self.api_key,
            "email": self.email,
            "country": country,
            "limit": limit,
            "page": 1
        }
        
        if start_date:
            params["event_date_min"] = start_date.strftime("%Y-%m-%d")
        if end_date:
            params["event_date_max"] = end_date.strftime("%Y-%m-%d")
        if event_types:
            params["event_type"] = ":OR:".join(event_types)
        
        all_events = []
        
        async with httpx.AsyncClient() as client:
            while True:
                response = await client.get(
                    self.BASE_URL,
                    params=params,
                    timeout=60
                )
                response.raise_for_status()
                
                data = response.json()
                events = data.get("data", [])
                
                if not events:
                    break
                
                all_events.extend(events)
                
                # Check if we got all events
                if len(events) < limit:
                    break
                
                params["page"] += 1
        
        return all_events
    
    def to_conflict_events(
        self,
        acled_data: List[Dict[str, Any]]
    ) -> List[ConflictEvent]:
        """Convert ACLED data to domain conflict events."""
        events = []
        
        for record in acled_data:
            try:
                # Parse coordinates
                lat = float(record.get("latitude", 0))
                lon = float(record.get("longitude", 0))
                
                if lat == 0 or lon == 0:
                    continue  # Skip events without coordinates
                
                location = Coordinates(latitude=lat, longitude=lon)
                
                # Parse event type
                event_type = self._map_event_type(record.get("event_type", ""))
                
                # Parse fatalities
                fatalities = int(record.get("fatalities", 0))
                intensity = ConflictIntensity.from_fatalities(fatalities)
                
                # Parse actors
                actors = []
                if record.get("actor1"):
                    actors.append(record["actor1"])
                if record.get("actor2"):
                    actors.append(record["actor2"])
                
                # Create event
                event = ConflictEvent(
                    event_date=datetime.strptime(record["event_date"], "%Y-%m-%d"),
                    location=location,
                    conflict_type=event_type,
                    intensity=intensity,
                    fatalities=fatalities,
                    actors=actors,
                    description=record.get("notes", ""),
                    source="ACLED"
                )
                
                events.append(event)
                
            except (ValueError, KeyError) as e:
                # Skip malformed records
                continue
        
        return events
    
    def _map_event_type(self, acled_type: str) -> ConflictType:
        """Map ACLED event type to domain conflict type."""
        mapping = {
            "Battles": ConflictType.BATTLE,
            "Explosions/Remote violence": ConflictType.EXPLOSION,
            "Violence against civilians": ConflictType.VIOLENCE_AGAINST_CIVILIANS,
            "Protests": ConflictType.PROTESTS,
            "Riots": ConflictType.RIOTS,
            "Strategic developments": ConflictType.STRATEGIC_DEVELOPMENT
        }
        
        return mapping.get(acled_type, ConflictType.BATTLE)
    
    async def get_events_near_location(
        self,
        location: Coordinates,
        radius_km: float,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ConflictEvent]:
        """Get conflict events near a specific location."""
        # Fetch all events for the time period
        raw_events = await self.get_conflict_events(
            start_date=start_date,
            end_date=end_date
        )
        
        # Convert to domain events
        all_events = self.to_conflict_events(raw_events)
        
        # Filter by distance
        nearby_events = []
        for event in all_events:
            distance = event.distance_to_point(location)
            if distance <= radius_km:
                nearby_events.append(event)
        
        return nearby_events