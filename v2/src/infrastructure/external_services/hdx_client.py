"""HDX (Humanitarian Data Exchange) API client."""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type
)


class HDXClient:
    """Client for interacting with HDX API."""
    
    BASE_URL = "https://data.humdata.org/api/3"
    
    def __init__(self, timeout: int = 30):
        """Initialize HDX client."""
        self.timeout = timeout
        self.headers = {
            "User-Agent": "Yemen-Market-Integration/2.0"
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.TimeoutException, httpx.NetworkError))
    )
    async def search_datasets(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Search for datasets on HDX."""
        params = {
            "q": query,
            "rows": limit,
            "start": 0
        }
        
        if filters:
            # Add filters like organization, tags, etc.
            for key, value in filters.items():
                params[f"fq_{key}"] = value
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.BASE_URL}/action/package_search",
                params=params,
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get("success"):
                return data["result"]["results"]
            else:
                raise Exception(f"HDX API error: {data.get('error', 'Unknown error')}")
    
    async def get_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific dataset."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.BASE_URL}/action/package_show",
                params={"id": dataset_id},
                headers=self.headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get("success"):
                return data["result"]
            else:
                raise Exception(f"HDX API error: {data.get('error', 'Unknown error')}")
    
    async def download_resource(
        self,
        resource_url: str,
        destination_path: str,
        progress_callback: Optional[callable] = None
    ) -> None:
        """Download a resource file from HDX."""
        async with httpx.AsyncClient() as client:
            async with client.stream("GET", resource_url, headers=self.headers) as response:
                response.raise_for_status()
                
                total_size = int(response.headers.get("content-length", 0))
                downloaded = 0
                
                with open(destination_path, "wb") as file:
                    async for chunk in response.aiter_bytes(chunk_size=8192):
                        file.write(chunk)
                        downloaded += len(chunk)
                        
                        if progress_callback and total_size > 0:
                            progress = downloaded / total_size
                            await progress_callback(progress)
    
    async def get_yemen_datasets(self) -> List[Dict[str, Any]]:
        """Get all Yemen-related datasets."""
        # Search for Yemen datasets
        datasets = await self.search_datasets(
            query="yemen",
            filters={
                "tags": "prices OR markets OR food OR commodities",
                "organization": "wfp OR acaps OR ocha"
            }
        )
        
        # Filter for relevant datasets
        relevant_datasets = []
        for dataset in datasets:
            tags = [tag["name"] for tag in dataset.get("tags", [])]
            
            # Check if dataset is relevant for market analysis
            if any(tag in tags for tag in ["prices", "markets", "commodities", "food-prices"]):
                relevant_datasets.append(dataset)
        
        return relevant_datasets
    
    async def get_latest_market_data(self) -> Optional[Dict[str, Any]]:
        """Get the latest market price dataset for Yemen."""
        datasets = await self.search_datasets(
            query="yemen market prices",
            filters={"organization": "wfp"},
            limit=10
        )
        
        if not datasets:
            return None
        
        # Sort by metadata_modified to get the latest
        sorted_datasets = sorted(
            datasets,
            key=lambda d: d.get("metadata_modified", ""),
            reverse=True
        )
        
        return sorted_datasets[0] if sorted_datasets else None