"""Event bus implementations for domain event handling."""

import asyncio
from collections import defaultdict
from typing import Any, Callable, Dict, List, Set

from ...application.interfaces import EventBus
from ...core.domain.shared.events import DomainEvent


class InMemoryEventBus(EventBus):
    """In-memory implementation of event bus for single-process applications."""
    
    def __init__(self):
        """Initialize event bus."""
        self._handlers: Dict[str, List[Callable]] = defaultdict(list)
        self._global_handlers: List[Callable] = []
    
    def subscribe(self, event_type: str, handler: Callable) -> None:
        """Subscribe a handler to a specific event type."""
        self._handlers[event_type].append(handler)
    
    def subscribe_all(self, handler: Callable) -> None:
        """Subscribe a handler to all events."""
        self._global_handlers.append(handler)
    
    async def publish(self, event: DomainEvent) -> None:
        """Publish a domain event."""
        # Call specific handlers
        handlers = self._handlers.get(event.event_name, [])
        
        # Add global handlers
        all_handlers = handlers + self._global_handlers
        
        # Execute all handlers
        tasks = []
        for handler in all_handlers:
            if asyncio.iscoroutinefunction(handler):
                tasks.append(handler(event))
            else:
                # Wrap sync handlers
                tasks.append(asyncio.create_task(self._run_sync(handler, event)))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def publish_batch(self, events: List[DomainEvent]) -> None:
        """Publish multiple domain events."""
        tasks = [self.publish(event) for event in events]
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _run_sync(self, handler: Callable, event: DomainEvent) -> None:
        """Run synchronous handler in executor."""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, handler, event)


class AsyncEventBus(EventBus):
    """Asynchronous event bus with background processing."""
    
    def __init__(self, max_queue_size: int = 10000):
        """Initialize async event bus."""
        self._handlers: Dict[str, List[Callable]] = defaultdict(list)
        self._global_handlers: List[Callable] = []
        self._queue: asyncio.Queue = asyncio.Queue(maxsize=max_queue_size)
        self._running = False
        self._worker_task: Optional[asyncio.Task] = None
    
    def subscribe(self, event_type: str, handler: Callable) -> None:
        """Subscribe a handler to a specific event type."""
        self._handlers[event_type].append(handler)
    
    def subscribe_all(self, handler: Callable) -> None:
        """Subscribe a handler to all events."""
        self._global_handlers.append(handler)
    
    async def start(self) -> None:
        """Start the event processing worker."""
        if self._running:
            return
        
        self._running = True
        self._worker_task = asyncio.create_task(self._process_events())
    
    async def stop(self) -> None:
        """Stop the event processing worker."""
        self._running = False
        
        if self._worker_task:
            # Put sentinel value to wake up worker
            await self._queue.put(None)
            await self._worker_task
            self._worker_task = None
    
    async def publish(self, event: DomainEvent) -> None:
        """Publish a domain event."""
        if not self._running:
            raise RuntimeError("Event bus is not running")
        
        await self._queue.put(event)
    
    async def publish_batch(self, events: List[DomainEvent]) -> None:
        """Publish multiple domain events."""
        if not self._running:
            raise RuntimeError("Event bus is not running")
        
        for event in events:
            await self._queue.put(event)
    
    async def _process_events(self) -> None:
        """Process events from the queue."""
        while self._running:
            try:
                # Wait for event with timeout
                event = await asyncio.wait_for(
                    self._queue.get(),
                    timeout=1.0
                )
                
                if event is None:  # Sentinel value
                    break
                
                # Process event
                await self._handle_event(event)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                # Log error but continue processing
                print(f"Error processing event: {e}")
    
    async def _handle_event(self, event: DomainEvent) -> None:
        """Handle a single event."""
        # Get specific handlers
        handlers = self._handlers.get(event.event_name, [])
        
        # Add global handlers
        all_handlers = handlers + self._global_handlers
        
        # Execute all handlers
        tasks = []
        for handler in all_handlers:
            if asyncio.iscoroutinefunction(handler):
                tasks.append(handler(event))
            else:
                # Wrap sync handlers
                loop = asyncio.get_event_loop()
                tasks.append(
                    loop.run_in_executor(None, handler, event)
                )
        
        if tasks:
            # Run handlers concurrently but wait for all
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check for exceptions
            for result in results:
                if isinstance(result, Exception):
                    # Log error
                    print(f"Handler error: {result}")