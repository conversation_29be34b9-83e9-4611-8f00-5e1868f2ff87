"""Dependency injection container configuration."""

from dependency_injector import containers, providers

from ..application.commands import (
    AnalyzeMarketIntegrationHandler,
)
from ..application.services import (
    AnalysisOrchestrator,
    DataPreparationService,
)
from ..core.domain.market.services import (
    MarketIntegrationService,
    PriceTransmissionService,
)
from ..infrastructure.caching import RedisCache, MemoryCache
from ..infrastructure.external_services import HDXClient, WFPClient, ACLEDClient
from ..infrastructure.messaging import InMemoryEventBus, AsyncEventBus
from ..infrastructure.persistence.unit_of_work import PostgresUnitOfWork


class Container(containers.DeclarativeContainer):
    """Dependency injection container."""
    
    # Configuration
    config = providers.Configuration()
    
    # Infrastructure - Database
    database_url = providers.Callable(
        lambda: config.database.url
    )
    
    unit_of_work = providers.Factory(
        PostgresUnitOfWork,
        connection_string=database_url
    )
    
    # Infrastructure - Caching
    cache = providers.Selector(
        config.cache.type,
        redis=providers.Singleton(
            RedisCache,
            redis_url=config.cache.redis.url,
            default_ttl=config.cache.default_ttl
        ),
        memory=providers.Singleton(
            MemoryCache,
            default_ttl=config.cache.default_ttl,
            max_size=config.cache.memory.max_size
        )
    )
    
    # Infrastructure - Event Bus
    event_bus = providers.Selector(
        config.events.type,
        inmemory=providers.Singleton(
            InMemoryEventBus
        ),
        async=providers.Singleton(
            AsyncEventBus,
            max_queue_size=config.events.queue_size
        )
    )
    
    # Infrastructure - External Services
    hdx_client = providers.Singleton(
        HDXClient,
        timeout=config.external.hdx.timeout
    )
    
    wfp_client = providers.Singleton(
        WFPClient,
        api_key=config.external.wfp.api_key
    )
    
    acled_client = providers.Singleton(
        ACLEDClient,
        api_key=config.external.acled.api_key,
        email=config.external.acled.email
    )
    
    # Domain Services
    price_transmission_service = providers.Factory(
        PriceTransmissionService
    )
    
    market_integration_service = providers.Factory(
        MarketIntegrationService,
        transmission_service=price_transmission_service
    )
    
    # Application Services
    data_preparation_service = providers.Factory(
        DataPreparationService,
        wfp_client=wfp_client,
        hdx_client=hdx_client
    )
    
    analysis_orchestrator = providers.Factory(
        AnalysisOrchestrator,
        price_repo=providers.Callable(
            lambda uow: uow.prices,
            unit_of_work
        ),
        transmission_service=price_transmission_service,
        integration_service=market_integration_service,
        event_bus=event_bus,
        cache=cache
    )
    
    # Command Handlers
    analyze_market_integration_handler = providers.Factory(
        AnalyzeMarketIntegrationHandler,
        uow=unit_of_work,
        market_repo=providers.Callable(
            lambda uow: uow.markets,
            unit_of_work
        ),
        price_repo=providers.Callable(
            lambda uow: uow.prices,
            unit_of_work
        ),
        integration_service=market_integration_service,
        orchestrator=analysis_orchestrator,
        event_bus=event_bus
    )