"""Plugin registry for managing loaded plugins."""

from typing import Dict, List, Optional

from .interfaces import Plugin


class PluginRegistry:
    """Registry for managing loaded plugins."""
    
    def __init__(self):
        """Initialize registry."""
        self._plugins: Dict[str, Dict[str, Plugin]] = {
            "models": {},
            "data_sources": {},
            "outputs": {}
        }
    
    def register(self, plugin_type: str, plugin_name: str, plugin: Plugin) -> None:
        """Register a plugin."""
        if plugin_type not in self._plugins:
            raise ValueError(f"Invalid plugin type: {plugin_type}")
        
        if plugin_name in self._plugins[plugin_type]:
            raise ValueError(f"Plugin already registered: {plugin_type}/{plugin_name}")
        
        self._plugins[plugin_type][plugin_name] = plugin
    
    def unregister(self, plugin_type: str, plugin_name: str) -> None:
        """Unregister a plugin."""
        if plugin_type in self._plugins and plugin_name in self._plugins[plugin_type]:
            del self._plugins[plugin_type][plugin_name]
    
    def get_plugin(self, plugin_type: str, plugin_name: str) -> Optional[Plugin]:
        """Get a registered plugin."""
        if plugin_type in self._plugins:
            return self._plugins[plugin_type].get(plugin_name)
        return None
    
    def is_registered(self, plugin_type: str, plugin_name: str) -> bool:
        """Check if a plugin is registered."""
        return (plugin_type in self._plugins and 
                plugin_name in self._plugins[plugin_type])
    
    def list_plugins(self, plugin_type: Optional[str] = None) -> Dict[str, List[str]]:
        """List registered plugins."""
        if plugin_type:
            if plugin_type in self._plugins:
                return {plugin_type: list(self._plugins[plugin_type].keys())}
            else:
                return {plugin_type: []}
        else:
            return {
                ptype: list(plugins.keys())
                for ptype, plugins in self._plugins.items()
            }
    
    def get_all_plugins(self, plugin_type: str) -> Dict[str, Plugin]:
        """Get all plugins of a specific type."""
        return self._plugins.get(plugin_type, {}).copy()
    
    def clear(self, plugin_type: Optional[str] = None) -> None:
        """Clear registered plugins."""
        if plugin_type:
            if plugin_type in self._plugins:
                self._plugins[plugin_type].clear()
        else:
            for ptype in self._plugins:
                self._plugins[ptype].clear()