"""Interfaces for application layer components."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from ..core.domain.shared.events import DomainEvent


class UnitOfWork(ABC):
    """Unit of Work pattern for transactional consistency."""
    
    @abstractmethod
    async def __aenter__(self) -> 'UnitOfWork':
        """Enter context manager."""
        pass
    
    @abstractmethod
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Exit context manager."""
        pass
    
    @abstractmethod
    async def commit(self) -> None:
        """Commit the unit of work."""
        pass
    
    @abstractmethod
    async def rollback(self) -> None:
        """Rollback the unit of work."""
        pass


class EventBus(ABC):
    """Event bus for publishing domain events."""
    
    @abstractmethod
    async def publish(self, event: DomainEvent) -> None:
        """Publish a domain event."""
        pass
    
    @abstractmethod
    async def publish_batch(self, events: List[DomainEvent]) -> None:
        """Publish multiple domain events."""
        pass


class QueryBus(ABC):
    """Query bus for handling queries."""
    
    @abstractmethod
    async def execute(self, query: Any) -> Any:
        """Execute a query and return result."""
        pass


class CommandBus(ABC):
    """Command bus for handling commands."""
    
    @abstractmethod
    async def execute(self, command: Any) -> Any:
        """Execute a command and return result."""
        pass


class Cache(ABC):
    """Cache interface for application layer."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with optional TTL in seconds."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> None:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    async def clear(self) -> None:
        """Clear all cache entries."""
        pass


class LegacyAnalysisAdapter(ABC):
    """Adapter interface for v1 analysis compatibility."""
    
    @abstractmethod
    async def run_analysis(
        self,
        market_ids: List[str],
        commodity_ids: List[str],
        start_date: str,
        end_date: str
    ) -> Any:
        """Run v1 analysis and return results."""
        pass