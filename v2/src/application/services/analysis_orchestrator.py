"""Orchestrator for complex analysis workflows."""

import asyncio
from datetime import datetime
from typing import Any, Dict, List
from uuid import UUID

from ...core.domain.market.entities import Market
from ...core.domain.market.repositories import PriceRepository
from ...core.domain.market.services import (
    MarketIntegrationService,
    PriceTransmissionService,
)
from ...core.models import PooledPanelModel, VECMModel
from ..interfaces import Cache, EventBus


class AnalysisOrchestrator:
    """Orchestrates complex multi-step analysis workflows."""
    
    def __init__(
        self,
        price_repo: PriceRepository,
        transmission_service: PriceTransmissionService,
        integration_service: MarketIntegrationService,
        event_bus: EventBus,
        cache: Cache
    ):
        """Initialize orchestrator with dependencies."""
        self.price_repo = price_repo
        self.transmission_service = transmission_service
        self.integration_service = integration_service
        self.event_bus = event_bus
        self.cache = cache
    
    async def start_analysis(
        self,
        analysis_id: UUID,
        markets: List[Market],
        commodity_ids: List[str],
        start_date: datetime,
        end_date: datetime,
        config: Dict[str, Any]
    ) -> None:
        """Start asynchronous analysis workflow."""
        # Create background task for analysis
        asyncio.create_task(
            self._run_analysis(
                analysis_id, markets, commodity_ids,
                start_date, end_date, config
            )
        )
    
    async def _run_analysis(
        self,
        analysis_id: UUID,
        markets: List[Market],
        commodity_ids: List[str],
        start_date: datetime,
        end_date: datetime,
        config: Dict[str, Any]
    ) -> None:
        """Run the actual analysis workflow."""
        start_time = datetime.utcnow()
        
        try:
            # Phase 1: Data preparation
            await self._publish_progress(analysis_id, "data_preparation", 0)
            price_data = await self._prepare_price_data(
                markets, commodity_ids, start_date, end_date
            )
            
            # Phase 2: Market integration analysis
            await self._publish_progress(analysis_id, "integration_analysis", 25)
            integration_results = await self._run_integration_analysis(
                markets, price_data, config
            )
            
            # Phase 3: Price transmission analysis
            await self._publish_progress(analysis_id, "transmission_analysis", 50)
            transmission_results = await self._run_transmission_analysis(
                markets, price_data, config
            )
            
            # Phase 4: Econometric modeling
            await self._publish_progress(analysis_id, "econometric_modeling", 75)
            model_results = await self._run_econometric_models(
                price_data, config
            )
            
            # Phase 5: Results compilation
            await self._publish_progress(analysis_id, "compilation", 90)
            final_results = self._compile_results(
                integration_results,
                transmission_results,
                model_results
            )
            
            # Cache results
            await self.cache.set(
                f"analysis:{analysis_id}",
                final_results,
                ttl=86400  # 24 hours
            )
            
            # Publish completion event
            duration = (datetime.utcnow() - start_time).total_seconds()
            await self.event_bus.publish(
                AnalysisCompletedEvent(
                    aggregate_id=analysis_id,
                    duration_seconds=duration,
                    results_location=f"cache:analysis:{analysis_id}",
                    success=True
                )
            )
            
        except Exception as e:
            # Publish failure event
            duration = (datetime.utcnow() - start_time).total_seconds()
            await self.event_bus.publish(
                AnalysisCompletedEvent(
                    aggregate_id=analysis_id,
                    duration_seconds=duration,
                    results_location="",
                    success=False,
                    error_message=str(e)
                )
            )
            raise
    
    async def _prepare_price_data(
        self,
        markets: List[Market],
        commodity_ids: List[str],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Prepare price data for analysis."""
        # Fetch price observations for all market-commodity pairs
        price_data = {}
        
        # Use asyncio.gather for parallel fetching
        tasks = []
        for market in markets:
            for commodity_id in commodity_ids:
                task = self._fetch_market_commodity_prices(
                    market, commodity_id, start_date, end_date
                )
                tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # Organize results
        for (market, commodity_id), prices in results:
            if market.market_id not in price_data:
                price_data[market.market_id] = {}
            price_data[market.market_id][commodity_id] = prices
        
        return price_data
    
    async def _fetch_market_commodity_prices(
        self,
        market: Market,
        commodity_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> Tuple[Tuple[Market, str], List[Any]]:
        """Fetch prices for a specific market-commodity pair."""
        from ...core.domain.market.value_objects import Commodity
        
        # Create commodity object (in real implementation, would fetch from repo)
        commodity = Commodity(
            code=commodity_id,
            name=commodity_id,
            category="food",
            standard_unit="kg"
        )
        
        prices = await self.price_repo.find_by_market_and_commodity(
            market.market_id, commodity, start_date, end_date
        )
        
        return ((market, commodity_id), prices)
    
    async def _run_integration_analysis(
        self,
        markets: List[Market],
        price_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run market integration analysis."""
        # Convert price data to format expected by integration service
        price_observations = {}
        for market_id, commodity_prices in price_data.items():
            all_prices = []
            for commodity_id, prices in commodity_prices.items():
                all_prices.extend(prices)
            price_observations[market_id] = all_prices
        
        # Run integration analysis
        integration_metrics = self.integration_service.analyze_integration(
            markets=markets,
            price_observations=price_observations,
            distance_threshold=config.get("distance_threshold", 500.0),
            correlation_threshold=config.get("correlation_threshold", 0.7)
        )
        
        return {
            "metrics": integration_metrics,
            "timestamp": datetime.utcnow()
        }
    
    async def _run_transmission_analysis(
        self,
        markets: List[Market],
        price_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run price transmission analysis."""
        transmission_results = []
        
        # Analyze transmission between all market pairs
        for i, market1 in enumerate(markets):
            for market2 in markets[i+1:]:
                # Check if markets have common commodities
                commodities1 = set(price_data.get(market1.market_id, {}).keys())
                commodities2 = set(price_data.get(market2.market_id, {}).keys())
                common_commodities = commodities1 & commodities2
                
                for commodity_id in common_commodities:
                    prices1 = price_data[market1.market_id][commodity_id]
                    prices2 = price_data[market2.market_id][commodity_id]
                    
                    if prices1 and prices2:
                        try:
                            from ...core.domain.market.value_objects import MarketPair
                            
                            market_pair = MarketPair(
                                source=market1.market_id,
                                target=market2.market_id,
                                distance_km=market1.coordinates.distance_to(market2.coordinates)
                            )
                            
                            transmission = self.transmission_service.calculate_transmission(
                                source_prices=prices1,
                                target_prices=prices2,
                                market_pair=market_pair
                            )
                            
                            transmission_results.append({
                                "market_pair": market_pair,
                                "commodity": commodity_id,
                                "metrics": transmission
                            })
                        except Exception as e:
                            # Log error but continue
                            pass
        
        return {
            "transmission_pairs": transmission_results,
            "total_pairs_analyzed": len(transmission_results)
        }
    
    async def _run_econometric_models(
        self,
        price_data: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run econometric models on the data."""
        # This is a placeholder for actual model execution
        # In real implementation, would use model estimators
        
        model_results = {
            "pooled_panel": None,
            "vecm": None,
            "threshold_vecm": None
        }
        
        # Run models if configured
        if config.get("run_pooled_panel", True):
            # Create and estimate pooled panel model
            pass
        
        if config.get("run_vecm", True):
            # Create and estimate VECM
            pass
        
        if config.get("run_threshold_vecm", False):
            # Create and estimate threshold VECM
            pass
        
        return model_results
    
    def _compile_results(
        self,
        integration_results: Dict[str, Any],
        transmission_results: Dict[str, Any],
        model_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compile all results into final format."""
        return {
            "analysis_type": "market_integration",
            "generated_at": datetime.utcnow().isoformat(),
            "integration_analysis": integration_results,
            "transmission_analysis": transmission_results,
            "econometric_models": model_results,
            "summary": self._generate_summary(
                integration_results,
                transmission_results,
                model_results
            )
        }
    
    def _generate_summary(
        self,
        integration_results: Dict[str, Any],
        transmission_results: Dict[str, Any],
        model_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate executive summary of results."""
        metrics = integration_results.get("metrics", {})
        
        return {
            "total_markets_analyzed": len(metrics.integrated_pairs) + len(metrics.non_integrated_pairs),
            "integrated_market_pairs": len(metrics.integrated_pairs),
            "integration_score": metrics.integration_score,
            "average_transmission_speed": metrics.average_transmission_speed,
            "spatial_decay_parameter": metrics.spatial_decay_parameter,
            "transmission_pairs_analyzed": transmission_results.get("total_pairs_analyzed", 0)
        }
    
    async def _publish_progress(
        self,
        analysis_id: UUID,
        phase: str,
        progress: int
    ) -> None:
        """Publish analysis progress event."""
        from ..commands.analyze_market_integration import AnalysisProgressEvent
        
        await self.event_bus.publish(
            AnalysisProgressEvent(
                aggregate_id=analysis_id,
                phase=phase,
                progress_percentage=progress,
                timestamp=datetime.utcnow()
            )
        )


# Additional events
from dataclasses import dataclass
from typing import Tuple

from ..commands.analyze_market_integration import AnalysisCompletedEvent
from ...core.domain.shared.events import DomainEvent


@dataclass
class AnalysisProgressEvent(DomainEvent):
    """Event for tracking analysis progress."""
    
    phase: str
    progress_percentage: int
    timestamp: datetime