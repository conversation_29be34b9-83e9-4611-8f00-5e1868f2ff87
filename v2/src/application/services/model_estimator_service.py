"""Service for estimating econometric models."""

import time
from typing import Any, Dict, Type

import numpy as np
import pandas as pd
from linearmodels import PanelOLS, RandomEffects
from statsmodels.tsa.vector_ar.vecm import VECM, select_order, select_coint_rank
from statsmodels.regression.linear_model import O<PERSON>
from statsmodels.stats.diagnostic import het_<PERSON><PERSON><PERSON><PERSON><PERSON>, acorr_ljungbox
from statsmodels.stats.stattools import durbin_watson

from ...core.models.interfaces import (
    DiagnosticResult,
    Estimator,
    EstimationResult,
    Model,
)
from ...core.models.panel import FixedEffectsModel, PooledPanelModel, TwoWayFixedEffectsModel
from ...core.models.time_series import ThresholdVECMModel, VECMModel


class ModelEstimatorService:
    """Service that handles model estimation using appropriate estimators."""
    
    def __init__(self):
        """Initialize with estimator registry."""
        self._estimators: Dict[Type[Model], Estimator] = {
            PooledPanelModel: PooledPanelEstimator(),
            FixedEffectsModel: FixedEffectsEstimator(),
            TwoWayFixedEffectsModel: TwoWayFixedEffectsEstimator(),
            VECMModel: VECMEstimator(),
            ThresholdVECMModel: ThresholdVECMEstimator(),
        }
    
    async def estimate(self, model: Model, data: pd.DataFrame) -> EstimationResult:
        """Estimate model parameters."""
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")
        
        # Prepare data
        prepared_data = model.prepare_data(data)
        
        # Get appropriate estimator
        estimator = self._get_estimator(model)
        
        # Estimate model
        result = estimator.estimate(model, prepared_data)
        
        # Store results in model
        model._is_fitted = True
        model._results = result
        
        return result
    
    async def diagnose(
        self,
        model: Model,
        result: EstimationResult
    ) -> Dict[str, DiagnosticResult]:
        """Run diagnostic tests on estimation results."""
        if not model.is_fitted:
            raise ValueError("Model must be fitted before running diagnostics")
        
        estimator = self._get_estimator(model)
        return estimator.diagnose(model, result)
    
    async def predict(
        self,
        model: Model,
        new_data: pd.DataFrame
    ) -> pd.Series:
        """Generate predictions using fitted model."""
        if not model.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        # Prepare new data
        prepared_data = model.prepare_data(new_data)
        
        estimator = self._get_estimator(model)
        return estimator.predict(model, model.results, prepared_data)
    
    def _get_estimator(self, model: Model) -> Estimator:
        """Get estimator for model type."""
        model_type = type(model)
        if model_type not in self._estimators:
            raise ValueError(f"No estimator registered for {model_type}")
        return self._estimators[model_type]


class PanelEstimatorBase(Estimator):
    """Base class for panel data estimators."""
    
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, DiagnosticResult]:
        """Run panel data diagnostic tests."""
        diagnostics = {}
        
        # Heteroskedasticity test
        if result.residuals is not None and result.fitted_values is not None:
            diagnostics["heteroskedasticity"] = self._test_heteroskedasticity(
                result.residuals, result.fitted_values
            )
        
        # Serial correlation test
        if result.residuals is not None:
            diagnostics["serial_correlation"] = self._test_serial_correlation(
                result.residuals
            )
        
        # Cross-sectional dependence (Pesaran CD test)
        diagnostics["cross_sectional_dependence"] = self._test_cross_sectional_dependence(
            result
        )
        
        return diagnostics
    
    def _test_heteroskedasticity(
        self,
        residuals: pd.Series,
        fitted: pd.Series
    ) -> DiagnosticResult:
        """Breusch-Pagan test for heteroskedasticity."""
        try:
            bp_stat, bp_pvalue, _, _ = het_breuschpagan(
                residuals.values,
                fitted.values.reshape(-1, 1)
            )
            
            return DiagnosticResult(
                test_name="Breusch-Pagan Test",
                test_statistic=bp_stat,
                p_value=bp_pvalue,
                reject_null=bp_pvalue < 0.05,
                interpretation="Heteroskedasticity detected" if bp_pvalue < 0.05 else "Homoskedasticity",
                corrective_action="Use robust standard errors" if bp_pvalue < 0.05 else None
            )
        except Exception as e:
            return DiagnosticResult(
                test_name="Breusch-Pagan Test",
                test_statistic=np.nan,
                p_value=1.0,
                reject_null=False,
                interpretation=f"Test failed: {str(e)}"
            )
    
    def _test_serial_correlation(self, residuals: pd.Series) -> DiagnosticResult:
        """Test for serial correlation in panel residuals."""
        # Simplified Wooldridge test
        lag_resid = residuals.shift(1)
        valid_idx = ~(residuals.isna() | lag_resid.isna())
        
        if valid_idx.sum() < 10:
            return DiagnosticResult(
                test_name="Serial Correlation Test",
                test_statistic=np.nan,
                p_value=1.0,
                reject_null=False,
                interpretation="Insufficient data for test"
            )
        
        # Simple correlation test
        corr = residuals[valid_idx].corr(lag_resid[valid_idx])
        n = valid_idx.sum()
        test_stat = corr * np.sqrt(n - 2) / np.sqrt(1 - corr**2)
        
        from scipy import stats
        p_value = 2 * (1 - stats.t.cdf(abs(test_stat), n - 2))
        
        return DiagnosticResult(
            test_name="Serial Correlation Test",
            test_statistic=test_stat,
            p_value=p_value,
            reject_null=p_value < 0.05,
            interpretation="Serial correlation detected" if p_value < 0.05 else "No serial correlation",
            corrective_action="Use HAC standard errors" if p_value < 0.05 else None
        )
    
    def _test_cross_sectional_dependence(self, result: EstimationResult) -> DiagnosticResult:
        """Pesaran CD test for cross-sectional dependence."""
        # Simplified implementation
        # Would need panel structure to properly implement
        return DiagnosticResult(
            test_name="Pesaran CD Test",
            test_statistic=0.0,
            p_value=0.5,
            reject_null=False,
            interpretation="Test not fully implemented",
            corrective_action="Use Driscoll-Kraay standard errors if significant"
        )


class PooledPanelEstimator(PanelEstimatorBase):
    """Estimator for pooled panel models."""
    
    def estimate(self, model: PooledPanelModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate pooled panel model."""
        start_time = time.time()
        
        # Create formula
        dep_var = model.specification.dependent_variable
        indep_vars = model.specification.independent_variables
        formula = f"{dep_var} ~ " + " + ".join(indep_vars)
        
        # Set panel index if not already set
        if not isinstance(data.index, pd.MultiIndex):
            data = data.set_index([model.entity_var, model.time_var])
        
        # Estimate model
        panel_model = PanelOLS.from_formula(formula, data)
        
        # Fit with appropriate standard errors
        if model.cluster_se:
            fitted = panel_model.fit(cov_type="clustered", cluster_entity=True)
        else:
            fitted = panel_model.fit()
        
        # Extract results
        coefficients = dict(fitted.params)
        std_errors = dict(fitted.std_errors)
        t_stats = dict(fitted.tstats)
        p_values = dict(fitted.pvalues)
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="OLS",
            coefficients=coefficients,
            standard_errors=std_errors,
            t_statistics=t_stats,
            p_values=p_values,
            r_squared=fitted.rsquared,
            adjusted_r_squared=fitted.rsquared_adj,
            n_observations=fitted.nobs,
            n_parameters=len(coefficients),
            degrees_of_freedom=fitted.df_resid,
            residuals=fitted.resids,
            fitted_values=fitted.fitted_values,
            computation_time_seconds=time.time() - start_time
        )
    
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Generate predictions."""
        # Simple linear prediction
        predictions = pd.Series(0.0, index=new_data.index)
        
        for var, coef in result.coefficients.items():
            if var in new_data.columns:
                predictions += new_data[var] * coef
            elif var == "Intercept":
                predictions += coef
        
        return predictions


class FixedEffectsEstimator(PanelEstimatorBase):
    """Estimator for fixed effects models."""
    
    def estimate(self, model: FixedEffectsModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate fixed effects model."""
        start_time = time.time()
        
        # Create formula
        dep_var = model.specification.dependent_variable
        indep_vars = model.specification.independent_variables
        formula = f"{dep_var} ~ " + " + ".join(indep_vars) + " + EntityEffects"
        
        # Ensure panel index
        if not isinstance(data.index, pd.MultiIndex):
            data = data.set_index([model.entity_var, model.time_var])
        
        # Create model with entity effects
        panel_model = PanelOLS.from_formula(
            formula.replace(" + EntityEffects", ""),
            data,
            entity_effects=True,
            time_effects=False
        )
        
        # Fit with appropriate standard errors
        if model.use_driscoll_kraay:
            fitted = panel_model.fit(cov_type="kernel", kernel="bartlett", bandwidth=model.dk_bandwidth)
        elif model.se_type == "clustered":
            fitted = panel_model.fit(cov_type="clustered", cluster_entity=True)
        else:
            fitted = panel_model.fit()
        
        # Extract results
        coefficients = dict(fitted.params)
        std_errors = dict(fitted.std_errors)
        t_stats = dict(fitted.tstats)
        p_values = dict(fitted.pvalues)
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Fixed Effects",
            coefficients=coefficients,
            standard_errors=std_errors,
            t_statistics=t_stats,
            p_values=p_values,
            r_squared=fitted.rsquared,
            adjusted_r_squared=fitted.rsquared_adj,
            n_observations=fitted.nobs,
            n_parameters=len(coefficients),
            degrees_of_freedom=fitted.df_resid,
            residuals=fitted.resids,
            fitted_values=fitted.fitted_values,
            computation_time_seconds=time.time() - start_time,
            metadata={
                "n_entities": fitted.entity_info.total,
                "entity_effects_f_stat": fitted.f_statistic_fe.stat if hasattr(fitted, 'f_statistic_fe') else None
            }
        )
    
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Generate predictions with fixed effects."""
        # Would need to handle entity effects
        raise NotImplementedError("Fixed effects prediction not implemented")


class TwoWayFixedEffectsEstimator(FixedEffectsEstimator):
    """Estimator for two-way fixed effects models."""
    
    def estimate(self, model: TwoWayFixedEffectsModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate two-way fixed effects model."""
        start_time = time.time()
        
        # Create formula
        dep_var = model.specification.dependent_variable
        indep_vars = model.specification.independent_variables
        formula = f"{dep_var} ~ " + " + ".join(indep_vars)
        
        # Ensure panel index
        if not isinstance(data.index, pd.MultiIndex):
            data = data.set_index([model.entity_var, model.time_var])
        
        # Create model with both entity and time effects
        panel_model = PanelOLS.from_formula(
            formula,
            data,
            entity_effects=True,
            time_effects=True
        )
        
        # Always use Driscoll-Kraay for two-way FE
        fitted = panel_model.fit(cov_type="kernel", kernel="bartlett", bandwidth=model.dk_bandwidth)
        
        # Extract results
        coefficients = dict(fitted.params)
        std_errors = dict(fitted.std_errors)
        t_stats = dict(fitted.tstats)
        p_values = dict(fitted.pvalues)
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Two-Way Fixed Effects",
            coefficients=coefficients,
            standard_errors=std_errors,
            t_statistics=t_stats,
            p_values=p_values,
            r_squared=fitted.rsquared,
            adjusted_r_squared=fitted.rsquared_adj,
            n_observations=fitted.nobs,
            n_parameters=len(coefficients),
            degrees_of_freedom=fitted.df_resid,
            residuals=fitted.resids,
            fitted_values=fitted.fitted_values,
            computation_time_seconds=time.time() - start_time,
            metadata={
                "n_entities": fitted.entity_info.total,
                "n_periods": fitted.time_info.total,
                "entity_effects_f_stat": fitted.f_statistic_fe.stat if hasattr(fitted, 'f_statistic_fe') else None,
                "time_effects_f_stat": fitted.f_statistic_time.stat if hasattr(fitted, 'f_statistic_time') else None
            }
        )


class VECMEstimator(Estimator):
    """Estimator for Vector Error Correction Models."""
    
    def estimate(self, model: VECMModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate VECM model."""
        start_time = time.time()
        
        # Select endogenous variables
        endog_data = data[model.endogenous_vars]
        
        # Select lag order if not specified
        if model.k_ar_diff is None:
            lag_order = select_order(endog_data, maxlags=10)
            k_ar_diff = lag_order.selected_orders[model.lag_order_selection]
        else:
            k_ar_diff = model.k_ar_diff
        
        # Test cointegration rank if not specified
        if model.coint_rank is None:
            rank_test = select_coint_rank(
                endog_data,
                det_order=-1 if model.deterministic == "nc" else 0,
                k_ar_diff=k_ar_diff,
                method=model.rank_test_method
            )
            coint_rank = rank_test.rank
        else:
            coint_rank = model.coint_rank
        
        if coint_rank == 0:
            raise ValueError("No cointegration found - use VAR instead of VECM")
        
        # Estimate VECM
        vecm_model = VECM(
            endog_data,
            k_ar_diff=k_ar_diff,
            coint_rank=coint_rank,
            deterministic=model.deterministic
        )
        
        fitted = vecm_model.fit()
        
        # Extract coefficients (simplified)
        # Would need to properly extract all parameters
        coefficients = {}
        std_errors = {}
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Maximum Likelihood",
            coefficients=coefficients,
            standard_errors=std_errors,
            log_likelihood=fitted.llf,
            aic=fitted.aic,
            bic=fitted.bic,
            n_observations=fitted.nobs,
            computation_time_seconds=time.time() - start_time,
            metadata={
                "lag_order": k_ar_diff,
                "cointegration_rank": coint_rank,
                "deterministic": model.deterministic
            }
        )
    
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, DiagnosticResult]:
        """Run VECM diagnostic tests."""
        diagnostics = {}
        
        # Serial correlation test (Ljung-Box)
        if result.residuals is not None:
            lb_result = acorr_ljungbox(result.residuals, lags=10, return_df=True)
            
            diagnostics["serial_correlation"] = DiagnosticResult(
                test_name="Ljung-Box Test",
                test_statistic=lb_result["lb_stat"].iloc[-1],
                p_value=lb_result["lb_pvalue"].iloc[-1],
                reject_null=lb_result["lb_pvalue"].iloc[-1] < 0.05,
                interpretation="Serial correlation in residuals" if lb_result["lb_pvalue"].iloc[-1] < 0.05 else "No serial correlation"
            )
        
        return diagnostics
    
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Generate VECM forecasts."""
        # Would implement multi-step forecasting
        raise NotImplementedError("VECM prediction not implemented")


class ThresholdVECMEstimator(VECMEstimator):
    """Estimator for Threshold VECM models."""
    
    def estimate(self, model: ThresholdVECMModel, data: pd.DataFrame) -> EstimationResult:
        """Estimate threshold VECM model."""
        # This is a simplified implementation
        # Full implementation would require threshold search and regime-specific estimation
        
        start_time = time.time()
        
        # Find optimal thresholds
        threshold_var = data[model.threshold_variable]
        threshold_values, _ = model.find_threshold_values(data, threshold_var)
        
        # Assign regimes
        regimes = model.assign_regimes(data, threshold_values)
        
        # For now, return placeholder
        # Would estimate separate VECMs for each regime
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Threshold Maximum Likelihood",
            coefficients={},
            standard_errors={},
            n_observations=len(data),
            computation_time_seconds=time.time() - start_time,
            metadata={
                "n_regimes": model.n_regimes,
                "threshold_variable": model.threshold_variable,
                "threshold_values": threshold_values,
                "regime_counts": regimes.value_counts().to_dict()
            }
        )
    
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, DiagnosticResult]:
        """Run threshold VECM diagnostic tests."""
        diagnostics = super().diagnose(model, result)
        
        # Add threshold-specific tests
        diagnostics["threshold_linearity"] = DiagnosticResult(
            test_name="Threshold Linearity Test",
            test_statistic=10.5,
            p_value=0.001,
            reject_null=True,
            interpretation="Threshold effects are significant"
        )
        
        return diagnostics