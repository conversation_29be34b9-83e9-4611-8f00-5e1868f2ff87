"""Service for preparing and transforming data for analysis."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd

from ...core.domain.market.value_objects import MarketId, Commodity
from ...infrastructure.external_services import WFPClient, HDXClient


class DataPreparationService:
    """Orchestrates data preparation from various sources."""
    
    def __init__(
        self,
        wfp_client: WFPClient,
        hdx_client: HDXClient
    ):
        """Initialize with data source clients."""
        self.wfp_client = wfp_client
        self.hdx_client = hdx_client
    
    async def prepare_market_data(
        self,
        market_ids: List[str],
        commodity_codes: List[str],
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """Prepare market price data from all sources."""
        # Fetch data from multiple sources
        wfp_data = await self._fetch_wfp_data(
            commodity_codes, start_date, end_date
        )
        
        # Convert to unified format
        unified_data = self._unify_data_format(wfp_data)
        
        # Filter by requested markets
        filtered_data = unified_data[
            unified_data["market_id"].isin(market_ids)
        ]
        
        # Apply data quality checks
        clean_data = self._apply_quality_checks(filtered_data)
        
        # Add derived features
        final_data = self._add_features(clean_data)
        
        return final_data
    
    async def _fetch_wfp_data(
        self,
        commodity_codes: List[str],
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """Fetch data from WFP."""
        raw_data = await self.wfp_client.get_market_prices(
            country_code="YEM",
            start_date=start_date,
            end_date=end_date,
            commodity_codes=commodity_codes
        )
        
        # Convert to DataFrame
        df = pd.DataFrame(raw_data)
        
        return df
    
    def _unify_data_format(self, data: pd.DataFrame) -> pd.DataFrame:
        """Convert data to unified format."""
        # Standardize column names
        column_mapping = {
            "market": "market_name",
            "commodity": "commodity_name",
            "price": "price_amount",
            "currency": "price_currency",
            "unit": "price_unit",
            "date": "observed_date",
            "admin1": "governorate",
            "admin2": "district"
        }
        
        data = data.rename(columns=column_mapping)
        
        # Create market ID from location info
        data["market_id"] = data.apply(
            lambda row: f"{row['governorate']}_{row['market_name']}".upper(),
            axis=1
        )
        
        # Convert date to datetime
        data["observed_date"] = pd.to_datetime(data["observed_date"])
        
        # Ensure numeric price
        data["price_amount"] = pd.to_numeric(data["price_amount"], errors="coerce")
        
        return data
    
    def _apply_quality_checks(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply data quality checks and cleaning."""
        # Remove rows with missing critical values
        critical_columns = ["market_id", "commodity_name", "price_amount", "observed_date"]
        clean_data = data.dropna(subset=critical_columns)
        
        # Remove zero or negative prices
        clean_data = clean_data[clean_data["price_amount"] > 0]
        
        # Remove extreme outliers (prices > 100x median)
        for commodity in clean_data["commodity_name"].unique():
            commodity_data = clean_data[clean_data["commodity_name"] == commodity]
            median_price = commodity_data["price_amount"].median()
            
            # Flag extreme outliers
            outlier_mask = commodity_data["price_amount"] > (median_price * 100)
            clean_data.loc[outlier_mask, "quality_flag"] = "extreme_outlier"
        
        # Remove duplicates
        clean_data = clean_data.drop_duplicates(
            subset=["market_id", "commodity_name", "observed_date"],
            keep="first"
        )
        
        return clean_data
    
    def _add_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add derived features for analysis."""
        # Sort by date
        data = data.sort_values(["market_id", "commodity_name", "observed_date"])
        
        # Add time features
        data["year"] = data["observed_date"].dt.year
        data["month"] = data["observed_date"].dt.month
        data["week"] = data["observed_date"].dt.isocalendar().week
        data["day_of_year"] = data["observed_date"].dt.dayofyear
        
        # Add price changes
        data["price_change"] = data.groupby(["market_id", "commodity_name"])["price_amount"].diff()
        data["price_change_pct"] = data.groupby(["market_id", "commodity_name"])["price_amount"].pct_change()
        
        # Add rolling statistics
        for window in [7, 14, 30]:
            data[f"price_ma{window}"] = (
                data.groupby(["market_id", "commodity_name"])["price_amount"]
                .transform(lambda x: x.rolling(window, min_periods=1).mean())
            )
            
            data[f"price_std{window}"] = (
                data.groupby(["market_id", "commodity_name"])["price_amount"]
                .transform(lambda x: x.rolling(window, min_periods=1).std())
            )
        
        # Add volatility measure
        data["price_volatility"] = data["price_std30"] / data["price_ma30"]
        
        return data
    
    def create_balanced_panel(
        self,
        data: pd.DataFrame,
        frequency: str = "W"
    ) -> pd.DataFrame:
        """Create a balanced panel dataset."""
        # Get unique combinations
        markets = data["market_id"].unique()
        commodities = data["commodity_name"].unique()
        
        # Create date range
        date_range = pd.date_range(
            start=data["observed_date"].min(),
            end=data["observed_date"].max(),
            freq=frequency
        )
        
        # Create multi-index
        index = pd.MultiIndex.from_product(
            [markets, commodities, date_range],
            names=["market_id", "commodity_name", "date"]
        )
        
        # Reindex data
        data_indexed = data.set_index(["market_id", "commodity_name", "observed_date"])
        
        # Create balanced panel
        balanced = data_indexed.reindex(index)
        
        # Forward fill missing values (up to 2 periods)
        balanced = balanced.groupby(level=[0, 1]).fillna(method="ffill", limit=2)
        
        # Reset index
        balanced = balanced.reset_index()
        
        return balanced