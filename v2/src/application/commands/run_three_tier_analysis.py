"""Command to run the complete three-tier econometric analysis."""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.models import (
    PooledPanelModel,
    TwoWayFixedEffectsModel,
    ThresholdVECMModel,
)
from ...core.models.interfaces import ModelSpecification
from ...core.models.validation import (
    ConflictValidationModel,
    FactorModel,
    PCAModel,
    CrossValidationModel
)
from ...infrastructure.diagnostics import PanelDiagnosticTests, TimeSeriesDiagnosticTests
from ...infrastructure.estimators.standard_errors import StandardErrorEstimator
from ..interfaces import Command, CommandHandler
from ..services import AnalysisOrchestrator, ModelEstimatorService


@dataclass
class RunThreeTierAnalysisCommand(Command):
    """Command to execute three-tier econometric analysis."""
    
    start_date: datetime
    end_date: datetime
    market_ids: Optional[List[str]] = None  # None means all markets
    commodity_ids: Optional[List[str]] = None  # None means all commodities
    
    # Tier-specific configurations
    tier1_config: Dict[str, Any] = None
    tier2_config: Dict[str, Any] = None
    tier3_config: Dict[str, Any] = None
    
    # Analysis options
    run_diagnostics: bool = True
    apply_corrections: bool = True
    save_intermediate: bool = True
    
    def __post_init__(self):
        """Initialize default configurations."""
        if self.tier1_config is None:
            self.tier1_config = {
                "model": "two_way_fixed_effects",
                "se_type": "driscoll_kraay",
                "entity_trends": False,
                "log_transform": True
            }
        
        if self.tier2_config is None:
            self.tier2_config = {
                "model": "threshold_vecm",
                "min_obs": 100,
                "n_regimes": 2,
                "threshold_variable": "conflict_intensity"
            }
        
        if self.tier3_config is None:
            self.tier3_config = {
                "validation_methods": ["cross_validation", "structural_break"],
                "factor_analysis": True,
                "spatial_analysis": True
            }


class RunThreeTierAnalysisHandler(CommandHandler):
    """Handler for three-tier analysis command."""
    
    def __init__(
        self,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        orchestrator: AnalysisOrchestrator,
        estimator_service: ModelEstimatorService
    ):
        """Initialize handler with dependencies."""
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.orchestrator = orchestrator
        self.estimator_service = estimator_service
    
    async def handle(self, command: RunThreeTierAnalysisCommand) -> str:
        """Execute three-tier analysis."""
        # Start analysis job
        analysis_id = await self.orchestrator.start_analysis(
            analysis_type="three_tier",
            parameters={
                "start_date": command.start_date,
                "end_date": command.end_date,
                "market_ids": command.market_ids,
                "commodity_ids": command.commodity_ids,
                "tier_configs": {
                    "tier1": command.tier1_config,
                    "tier2": command.tier2_config,
                    "tier3": command.tier3_config
                },
                "options": {
                    "diagnostics": command.run_diagnostics,
                    "corrections": command.apply_corrections,
                    "save_intermediate": command.save_intermediate
                }
            }
        )
        
        # Execute tiers in sequence
        try:
            # Tier 1: Pooled panel analysis
            tier1_result = await self._run_tier1(command, analysis_id)
            
            # Tier 2: Commodity-specific analysis
            tier2_results = await self._run_tier2(
                command, analysis_id, tier1_result
            )
            
            # Tier 3: Cross-validation and robustness
            tier3_result = await self._run_tier3(
                command, analysis_id, tier1_result, tier2_results
            )
            
            # Finalize analysis
            await self.orchestrator.complete_analysis(
                analysis_id,
                results={
                    "tier1": tier1_result,
                    "tier2": tier2_results,
                    "tier3": tier3_result
                }
            )
            
        except Exception as e:
            await self.orchestrator.fail_analysis(analysis_id, str(e))
            raise
        
        return analysis_id
    
    async def _run_tier1(
        self,
        command: RunThreeTierAnalysisCommand,
        analysis_id: str
    ) -> Dict[str, Any]:
        """Run Tier 1: Pooled panel analysis."""
        # Update progress
        await self.orchestrator.update_progress(
            analysis_id, "tier1", 0, "Loading panel data..."
        )
        
        # Load panel data
        panel_data = await self._load_panel_data(command)
        
        # Create model specification
        spec = ModelSpecification(
            model_type="panel_regression",
            dependent_variable="log_price" if command.tier1_config.get("log_transform") else "price",
            independent_variables=self._get_tier1_variables(command.tier1_config),
            parameters={
                "entity_var": "market_id",
                "time_var": "date",
                **command.tier1_config
            }
        )
        
        # Create model
        if command.tier1_config["model"] == "two_way_fixed_effects":
            model = TwoWayFixedEffectsModel(spec)
        else:
            model = PooledPanelModel(spec)
        
        await self.orchestrator.update_progress(
            analysis_id, "tier1", 25, "Estimating model..."
        )
        
        # Estimate model
        result = await self.estimator_service.estimate(model, panel_data)
        
        # Run diagnostics if requested
        if command.run_diagnostics:
            await self.orchestrator.update_progress(
                analysis_id, "tier1", 75, "Running diagnostics..."
            )
            
            # Use v2 diagnostic tests
            panel_diagnostics = PanelDiagnosticTests()
            diagnostics = panel_diagnostics.run_all_diagnostics(result)
            
            # Apply corrections if needed
            if command.apply_corrections and self._needs_correction(diagnostics):
                result = await self._apply_corrections_v2(
                    model, panel_data, result, diagnostics
                )
        
        await self.orchestrator.update_progress(
            analysis_id, "tier1", 100, "Tier 1 complete"
        )
        
        return {
            "model": model.name,
            "result": result,
            "diagnostics": diagnostics if command.run_diagnostics else None,
            "metadata": {
                "n_markets": panel_data["market_id"].nunique(),
                "n_commodities": panel_data["commodity"].nunique(),
                "n_observations": len(panel_data),
                "time_span": f"{panel_data['date'].min()} to {panel_data['date'].max()}"
            }
        }
    
    async def _run_tier2(
        self,
        command: RunThreeTierAnalysisCommand,
        analysis_id: str,
        tier1_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run Tier 2: Commodity-specific analysis."""
        await self.orchestrator.update_progress(
            analysis_id, "tier2", 0, "Starting commodity-specific analysis..."
        )
        
        # Get commodities to analyze
        commodities = command.commodity_ids or await self._get_all_commodities()
        results = {}
        
        for i, commodity in enumerate(commodities):
            progress = int((i / len(commodities)) * 100)
            await self.orchestrator.update_progress(
                analysis_id, "tier2", progress, f"Analyzing {commodity}..."
            )
            
            # Load commodity-specific data
            commodity_data = await self._load_commodity_data(command, commodity)
            
            # Skip if insufficient data
            if len(commodity_data) < command.tier2_config.get("min_obs", 100):
                continue
            
            # Create TVECM specification
            spec = ModelSpecification(
                model_type="threshold_vecm",
                dependent_variable=None,  # VECM uses multiple endogenous
                independent_variables=[],
                parameters={
                    "endogenous_vars": ["price_source", "price_target"],
                    "threshold_variable": command.tier2_config.get("threshold_variable"),
                    "n_regimes": command.tier2_config.get("n_regimes", 2),
                    **command.tier2_config
                }
            )
            
            # Create and estimate model
            model = ThresholdVECMModel(spec)
            
            try:
                result = await self.estimator_service.estimate(model, commodity_data)
                
                # Run diagnostics
                if command.run_diagnostics:
                    diagnostics = await self.estimator_service.diagnose(model, result)
                else:
                    diagnostics = None
                
                results[commodity] = {
                    "model": model.name,
                    "result": result,
                    "diagnostics": diagnostics,
                    "metadata": {
                        "n_observations": len(commodity_data),
                        "n_market_pairs": commodity_data["market_pair"].nunique()
                    }
                }
                
            except Exception as e:
                results[commodity] = {
                    "error": str(e),
                    "status": "failed"
                }
        
        await self.orchestrator.update_progress(
            analysis_id, "tier2", 100, "Tier 2 complete"
        )
        
        return results
    
    async def _run_tier3(
        self,
        command: RunThreeTierAnalysisCommand,
        analysis_id: str,
        tier1_result: Dict[str, Any],
        tier2_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run Tier 3: Cross-validation and robustness checks using v2 models."""
        await self.orchestrator.update_progress(
            analysis_id, "tier3", 0, "Starting validation..."
        )
        
        validation_results = {}
        panel_data = await self._load_panel_data(command)
        
        # 1. Cross-validation using v2 CrossValidationModel
        if "cross_validation" in command.tier3_config["validation_methods"]:
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 20, "Running cross-validation..."
            )
            
            cv_spec = ModelSpecification(
                model_type="cross_validation",
                dependent_variable=tier1_result["result"].model.specification.dependent_variable,
                independent_variables=tier1_result["result"].model.specification.independent_variables,
                parameters={
                    "cv_method": "time_series",
                    "n_splits": 5,
                    "test_size": 0.2
                }
            )
            cv_model = CrossValidationModel(cv_spec)
            
            # Define model function for CV
            def model_func(train_data):
                return self.estimator_service.estimate(
                    tier1_result["result"].model,
                    train_data
                )
            
            validation_results["cross_validation"] = cv_model.cross_validate(
                panel_data, model_func
            )
        
        # 2. Conflict validation using v2 ConflictValidationModel
        if "conflict_validation" in command.tier3_config["validation_methods"]:
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 40, "Analyzing conflict effects..."
            )
            
            conflict_spec = ModelSpecification(
                model_type="conflict_validation",
                dependent_variable=tier1_result["result"].model.specification.dependent_variable,
                independent_variables=tier1_result["result"].model.specification.independent_variables,
                parameters={
                    "conflict_variable": "conflict_events",
                    "threshold_quantiles": [0.25, 0.5, 0.75]
                }
            )
            conflict_model = ConflictValidationModel(conflict_spec)
            
            from ...infrastructure.estimators.validation import ConflictValidator
            conflict_validator = ConflictValidator()
            validation_results["conflict_analysis"] = await conflict_validator.estimate(
                conflict_model, panel_data
            )
        
        # 3. Factor analysis using v2 FactorModel
        if command.tier3_config.get("factor_analysis", True):
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 60, "Conducting factor analysis..."
            )
            
            factor_spec = ModelSpecification(
                model_type="factor_analysis",
                dependent_variable=None,
                independent_variables=[],
                parameters={
                    "n_factors": 3,
                    "rotation": "varimax",
                    "standardize": True
                }
            )
            factor_model = FactorModel(factor_spec)
            
            # Extract residuals from tier1 and tier2
            residuals_data = self._extract_residuals_for_factor_analysis(
                tier1_result, tier2_results
            )
            
            validation_results["factor_analysis"] = factor_model.extract_factors(
                residuals_data
            )
        
        # 4. PCA analysis using v2 PCAModel
        if command.tier3_config.get("pca_analysis", True):
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 80, "Running PCA..."
            )
            
            pca_spec = ModelSpecification(
                model_type="pca",
                dependent_variable=None,
                independent_variables=[],
                parameters={
                    "n_components": None,  # Auto-select
                    "variance_threshold": 0.95,
                    "standardize": True
                }
            )
            pca_model = PCAModel(pca_spec)
            
            validation_results["pca_analysis"] = pca_model.fit_pca(panel_data)
        
        # 5. Structural break tests using v2 diagnostics
        if "structural_break" in command.tier3_config["validation_methods"]:
            await self.orchestrator.update_progress(
                analysis_id, "tier3", 90, "Testing structural breaks..."
            )
            
            panel_diagnostics = PanelDiagnosticTests()
            validation_results["structural_breaks"] = panel_diagnostics.test_structural_breaks(
                panel_data,
                f"{tier1_result['result'].model.specification.dependent_variable} ~ " + 
                " + ".join(tier1_result['result'].model.specification.independent_variables)
            )
        
        await self.orchestrator.update_progress(
            analysis_id, "tier3", 100, "Tier 3 complete"
        )
        
        return validation_results
    
    async def _load_panel_data(
        self,
        command: RunThreeTierAnalysisCommand
    ) -> pd.DataFrame:
        """Load and prepare panel data for Tier 1."""
        # This would use repositories to load data
        # For now, return placeholder
        import pandas as pd
        return pd.DataFrame()
    
    async def _load_commodity_data(
        self,
        command: RunThreeTierAnalysisCommand,
        commodity: str
    ) -> pd.DataFrame:
        """Load commodity-specific time series data."""
        # This would load market pair data for specific commodity
        import pandas as pd
        return pd.DataFrame()
    
    async def _get_all_commodities(self) -> List[str]:
        """Get list of all commodities."""
        # This would query from repository
        return ["wheat_flour", "rice", "sugar", "fuel_diesel"]
    
    def _get_tier1_variables(self, config: Dict[str, Any]) -> List[str]:
        """Get independent variables for Tier 1."""
        variables = ["conflict_intensity", "distance_to_port", "rainfall"]
        
        # Add interaction terms if specified
        if config.get("interactions", False):
            variables.extend([
                "conflict_intensity_x_distance",
                "conflict_intensity_x_commodity"
            ])
        
        return variables
    
    def _needs_correction(self, diagnostics: Dict[str, Any]) -> bool:
        """Check if model needs correction based on diagnostics."""
        # Check for serial correlation or cross-sectional dependence
        if diagnostics.get("serial_correlation", {}).get("reject_null", False):
            return True
        
        if diagnostics.get("cross_sectional_dependence", {}).get("reject_null", False):
            return True
        
        return False
    
    async def _apply_corrections(
        self,
        model,
        data: pd.DataFrame,
        result: Any,
        diagnostics: Dict[str, Any]
    ) -> Any:
        """Apply corrections based on diagnostic results."""
        # Re-estimate with appropriate standard errors
        corrected_spec = model.specification
        
        # Use Driscoll-Kraay if cross-sectional dependence
        if diagnostics.get("cross_sectional_dependence", {}).get("reject_null", False):
            corrected_spec.parameters["se_type"] = "driscoll_kraay"
            corrected_spec.parameters["driscoll_kraay"] = True
        
        # Re-estimate
        return await self.estimator_service.estimate(model, data)
    
    async def _apply_corrections_v2(
        self,
        model,
        data: pd.DataFrame,
        result: Any,
        diagnostics: Dict[str, Any]
    ) -> Any:
        """Apply corrections using v2 standard error estimators."""
        se_estimator = StandardErrorEstimator()
        tests = diagnostics.get('tests', {})
        
        # Extract data components from result
        residuals = result.residuals
        X = result.X if hasattr(result, 'X') else None
        entity_ids = data.index.get_level_values(0) if isinstance(data.index, pd.MultiIndex) else None
        time_ids = data.index.get_level_values(1) if isinstance(data.index, pd.MultiIndex) else None
        
        # Determine which correction to apply
        serial_corr = tests.get('serial_correlation', {}).get('reject_null', False)
        cross_dep = tests.get('cross_sectional_dependence', {}).get('reject_null', False)
        hetero = tests.get('heteroskedasticity', {}).get('reject_null', False)
        
        if serial_corr and cross_dep:
            # Apply Driscoll-Kraay standard errors
            dk_results = se_estimator.driscoll_kraay(
                residuals=residuals.values,
                X=X.values if X is not None else data[model.specification.independent_vars].values,
                entity_ids=entity_ids.values,
                time_ids=time_ids.values
            )
            result.standard_errors = pd.Series(
                dk_results['standard_errors'],
                index=result.params.index
            )
            result.se_type = 'Driscoll-Kraay'
            
        elif serial_corr:
            # Apply clustered standard errors
            cluster_results = se_estimator.clustered(
                residuals=residuals.values,
                X=X.values if X is not None else data[model.specification.independent_vars].values,
                cluster_ids=entity_ids.values
            )
            result.standard_errors = pd.Series(
                cluster_results['standard_errors'],
                index=result.params.index
            )
            result.se_type = 'Clustered'
            
        elif hetero:
            # Apply heteroskedasticity-robust standard errors
            hc_results = se_estimator.heteroskedasticity_robust(
                residuals=residuals.values,
                X=X.values if X is not None else data[model.specification.independent_vars].values,
                hc_type='HC3'
            )
            result.standard_errors = pd.Series(
                hc_results['standard_errors'],
                index=result.params.index
            )
            result.se_type = 'HC3'
        
        return result
    
    async def _cross_validate_tiers(
        self,
        tier1_result: Dict[str, Any],
        tier2_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Cross-validate results between tiers."""
        # Compare estimates, check consistency
        return {
            "consistency_score": 0.85,
            "discrepancies": []
        }
    
    async def _test_structural_breaks(
        self,
        command: RunThreeTierAnalysisCommand,
        tier1_result: Dict[str, Any],
        tier2_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Test for structural breaks in the analysis period."""
        # Would implement Chow test or similar
        return {
            "break_dates": ["2020-03-15"],  # COVID-19
            "significance": 0.001
        }
    
    async def _run_factor_analysis(
        self,
        tier1_result: Dict[str, Any],
        tier2_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run factor analysis on residuals."""
        # Extract common factors from model residuals
        return {
            "n_factors": 2,
            "variance_explained": 0.75,
            "factor_loadings": {}
        }