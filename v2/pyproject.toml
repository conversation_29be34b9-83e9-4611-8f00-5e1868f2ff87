[tool.poetry]
name = "yemen-market-integration"
version = "2.0.0"
description = "Yemen Market Integration Analysis - v2 Architecture"
authors = ["World Bank Development Research Group"]
readme = "README.md"
python = "^3.11"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.0"
pydantic = "^2.5.0"
typer = "^0.9.0"
asyncio = "^3.4.3"
asyncpg = "^0.29.0"
redis = "^5.0.1"
dependency-injector = "^4.41.0"
structlog = "^23.2.0"
uvicorn = "^0.24.0"
httpx = "^0.25.0"
numpy = "^1.26.0"
pandas = "^2.1.0"
statsmodels = "^0.14.0"
scikit-learn = "^1.3.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
mypy = "^1.7.1"
ruff = "^0.1.6"
black = "^23.11.0"
isort = "^5.12.0"
pre-commit = "^3.5.0"

[tool.ruff]
line-length = 88
select = ["E", "F", "I", "N", "W", "UP", "B", "C90", "RUF"]
ignore = ["E501", "B008"]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"