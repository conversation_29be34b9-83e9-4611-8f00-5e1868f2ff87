#!/usr/bin/env python3
"""Data migration tool from v1 to v2."""

import asyncio
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List

import click
import pandas as pd
from rich.console import Console
from rich.progress import Progress

# Add v2 to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.infrastructure.adapters import V1Adapter
from src.infrastructure.persistence.unit_of_work import PostgresUnitOfWork
from src.shared.container import Container


console = Console()


class DataMigrator:
    """Handles data migration from v1 to v2."""
    
    def __init__(self, v1_path: Path, container: Container):
        """Initialize migrator."""
        self.v1_adapter = V1Adapter(v1_path)
        self.container = container
        self.stats = {
            "markets": {"total": 0, "migrated": 0, "failed": 0},
            "prices": {"total": 0, "migrated": 0, "failed": 0},
            "conflicts": {"total": 0, "migrated": 0, "failed": 0}
        }
    
    async def migrate_all(self) -> Dict[str, Dict[str, int]]:
        """Migrate all data from v1 to v2."""
        console.print("[bold blue]Starting data migration from v1 to v2...[/bold blue]")
        
        # Import v1 data
        v1_data = self.v1_adapter.import_v1_data()
        
        # Migrate each dataset
        with Progress() as progress:
            if "markets" in v1_data:
                await self._migrate_markets(v1_data["markets"], progress)
            
            if "prices" in v1_data:
                await self._migrate_prices(v1_data["prices"], progress)
            
            if "conflicts" in v1_data:
                await self._migrate_conflicts(v1_data["conflicts"], progress)
        
        return self.stats
    
    async def _migrate_markets(self, markets_df: pd.DataFrame, progress: Progress) -> None:
        """Migrate market data."""
        task = progress.add_task("[cyan]Migrating markets...", total=len(markets_df))
        
        # Convert to domain objects
        markets = self.v1_adapter.convert_markets(markets_df)
        self.stats["markets"]["total"] = len(markets)
        
        # Save to v2 database
        async with self.container.unit_of_work() as uow:
            for market in markets:
                try:
                    await uow.markets.save(market)
                    self.stats["markets"]["migrated"] += 1
                except Exception as e:
                    console.print(f"[red]Failed to migrate market {market.market_id}: {e}[/red]")
                    self.stats["markets"]["failed"] += 1
                
                progress.update(task, advance=1)
            
            await uow.commit()
    
    async def _migrate_prices(self, prices_df: pd.DataFrame, progress: Progress) -> None:
        """Migrate price data."""
        task = progress.add_task("[cyan]Migrating prices...", total=len(prices_df))
        
        # Convert to domain objects
        observations = self.v1_adapter.convert_prices(prices_df)
        self.stats["prices"]["total"] = len(observations)
        
        # Batch save for efficiency
        batch_size = 1000
        
        async with self.container.unit_of_work() as uow:
            for i in range(0, len(observations), batch_size):
                batch = observations[i:i + batch_size]
                
                try:
                    await uow.prices.save_batch(batch)
                    self.stats["prices"]["migrated"] += len(batch)
                except Exception as e:
                    console.print(f"[red]Failed to migrate price batch: {e}[/red]")
                    self.stats["prices"]["failed"] += len(batch)
                
                progress.update(task, advance=len(batch))
            
            await uow.commit()
    
    async def _migrate_conflicts(self, conflicts_df: pd.DataFrame, progress: Progress) -> None:
        """Migrate conflict data."""
        # Implementation would go here
        console.print("[yellow]Conflict migration not yet implemented[/yellow]")
    
    def print_summary(self) -> None:
        """Print migration summary."""
        console.print("\n[bold green]Migration Summary:[/bold green]")
        
        for data_type, stats in self.stats.items():
            if stats["total"] > 0:
                success_rate = (stats["migrated"] / stats["total"]) * 100
                console.print(
                    f"{data_type.capitalize()}: "
                    f"{stats['migrated']}/{stats['total']} migrated "
                    f"({success_rate:.1f}% success)"
                )
                if stats["failed"] > 0:
                    console.print(f"  [red]Failed: {stats['failed']}[/red]")


@click.command()
@click.option(
    "--v1-path",
    type=click.Path(exists=True, path_type=Path),
    default="../",
    help="Path to v1 codebase"
)
@click.option(
    "--db-url",
    default="postgresql://localhost/yemen_market_v2",
    help="Database URL for v2"
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Run without actually migrating data"
)
def main(v1_path: Path, db_url: str, dry_run: bool):
    """Migrate data from v1 to v2."""
    if dry_run:
        console.print("[yellow]DRY RUN - No data will be migrated[/yellow]")
    
    # Create container
    container = Container()
    container.config.database.url.from_value(db_url)
    
    # Create migrator
    migrator = DataMigrator(v1_path, container)
    
    # Run migration
    try:
        stats = asyncio.run(migrator.migrate_all())
        migrator.print_summary()
        
        if not dry_run:
            console.print("\n[bold green]Migration completed successfully![/bold green]")
        
    except Exception as e:
        console.print(f"\n[bold red]Migration failed: {e}[/bold red]")
        sys.exit(1)


if __name__ == "__main__":
    main()