# Models API Reference

This section documents the econometric models implemented for the Yemen market integration analysis.

## Overview

The models package implements a **three-tier econometric framework** for analyzing market integration in conflict-affected settings:

- **Tier 1**: Pooled panel models with fixed effects
- **Tier 2**: Commodity-specific threshold models
- **Tier 3**: Factor analysis and external validation

## Three-Tier Framework Structure

### Core Components

- [three_tier/core/](three_tier/README.md#core) - Base classes and utilities
  - `BaseThreeTierModel` - Abstract base class for all models
  - `ResultsContainer` - Standardized results storage
  - `PanelDataHandler` - 3D panel data management
  - `DataValidator` - Input validation

### Tier Implementations

- [three_tier/tier1_pooled/](three_tier/README.md#tier1) - Pooled panel analysis
  - `PooledPanelModel` - Main Tier 1 model
  - Fixed effects estimation
  - Driscoll-Kraay standard errors

- [three_tier/tier2_commodity/](three_tier/README.md#tier2) - Commodity-specific models
  - `CommoditySpecificModel` - Main Tier 2 model
  - `ThresholdVECM` - Threshold cointegration
  - `CommodityExtractor` - Data extraction utilities

- [three_tier/tier3_validation/](three_tier/README.md#tier3) - Validation and factor analysis
  - `ConflictValidation` - External validation
  - `FactorModels` - Market integration factors
  - `PCAAnalysis` - Principal component analysis

### Integrated Analysis

- [three_tier/integration/](three_tier/README.md#integration) - Framework orchestration
  - `ThreeTierAnalysis` - Main runner class
  - `CrossTierValidation` - Cross-tier consistency checks
  - Automatic diagnostic testing

### Diagnostics (NEW)

- [three_tier/diagnostics/](three_tier/README.md#diagnostics) - Econometric tests
  - `ThreeTierPanelDiagnostics` - Main diagnostic orchestrator
  - World Bank-standard tests (Wooldridge, Pesaran CD, etc.)
  - Automatic corrections for test failures
  - Publication-ready reports

## Usage Examples

### Basic Three-Tier Analysis

```python
from yemen_market.models.three_tier.integration import ThreeTierAnalysis

# Configure analysis
config = {
    'tier1_config': {
        'include_time_effects': True,
        'include_entity_effects': True
    },
    'run_diagnostics': True,  # Automatic diagnostic testing
    'output_dir': 'results/analysis'
}

# Run analysis
analysis = ThreeTierAnalysis(config)
results = analysis.run_full_analysis(data)

# Access tier-specific results
tier1_results = results['tier1']
tier2_results = results['tier2']  # Dict by commodity
tier3_results = results['tier3']
```

### Direct Tier Usage

```python
# Tier 1: Pooled analysis
from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel

model = PooledPanelModel(config)
model.fit(panel_data)
results = model.get_results()

# Tier 2: Commodity-specific
from yemen_market.models.three_tier.tier2_commodity import CommoditySpecificModel

wheat_model = CommoditySpecificModel(config)
wheat_model.fit(wheat_data)

# Tier 3: Validation
from yemen_market.models.three_tier.tier3_validation import ConflictValidation

validator = ConflictValidation()
validation_results = validator.validate(model_results, conflict_data)
```

## Model Comparison

```python
from yemen_market.models import ModelComparison

# Compare different configurations
comparison = ModelComparison()
comparison.add_model('baseline', baseline_results)
comparison.add_model('with_conflict', conflict_results)
comparison.generate_comparison_table()
```

## Key Features

### Automatic Diagnostics

All models include automatic diagnostic testing:
- Serial correlation (Wooldridge test)
- Cross-sectional dependence (Pesaran CD test)
- Heteroskedasticity (Modified Wald test)
- Unit roots (Im-Pesaran-Shin test)

### Standardized Results

All models return results in `ResultsContainer` format:
- Consistent interface across tiers
- Easy comparison and aggregation
- Diagnostic results included

### Panel Data Support

Full support for 3D panel structures:
- Market × Commodity × Time
- Automatic handling of unbalanced panels
- Missing data interpolation

## Migration from Old Models

If you're migrating from the old dual-track approach:

```python
from yemen_market.models.three_tier.migration import ModelMigrationHelper

# Migrate configuration
new_config = ModelMigrationHelper.migrate_config(old_config)

# Convert results
new_results = ModelMigrationHelper.convert_results(old_results)
```

## See Also

- [Three-Tier Methodology](../../models/yemen_panel_methodology.md)
- [Modeling Guide](../../guides/modeling_guide.md)
- [Migration Guide](../../../MIGRATION_GUIDE.md)