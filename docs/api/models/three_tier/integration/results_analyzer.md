# Results Analyzer

`yemen_market.models.three_tier.integration.results_analyzer`

This module provides comprehensive analysis and interpretation of three-tier econometric model results, including coefficient extraction, market integration analysis, and policy insights generation.

## Overview

The ResultsAnalyzer class provides a unified interface for:

- **Loading Results**: From JSON files saved by the three-tier analysis
- **Coefficient Extraction**: With automatic p-value calculation and significance testing
- **Impact Analysis**: Quantifying conflict effects on prices
- **Market Integration**: Analyzing price correlations and market connectivity
- **Policy Insights**: Generating actionable recommendations from results

## Class: ResultsAnalyzer

### Initialization

```python
ResultsAnalyzer(results_dir: Path)
```

Initialize the analyzer with a results directory.

**Parameters:**
- `results_dir`: Path to directory containing three-tier analysis results

### Methods

#### load_results

```python
load_results() -> None
```

Load all results from the analysis directory structure:
- `tier1/tier1_results.json`: Pooled panel results
- `tier2/*_results.json`: Commodity-specific results
- `tier3/tier3_results.json`: Factor analysis results

#### extract_tier1_coefficients

```python
extract_tier1_coefficients(model_object: Optional[Any] = None) -> Dict[str, Any]
```

Extract and process Tier 1 pooled panel regression coefficients.

**Parameters:**
- `model_object`: Optional fitted model object (if available)

**Returns:**
Dictionary containing:
- `coefficients`: Parameter estimates
- `standard_errors`: Standard errors for each coefficient
- `statistics`: Model statistics (R-squared, observations, etc.)
- `p_values`: Calculated p-values using t-distribution
- `significance`: Significance stars (***: p<0.01, **: p<0.05, *: p<0.10)

#### analyze_conflict_impact

```python
analyze_conflict_impact(coefficients: Dict[str, Any]) -> Dict[str, float]
```

Analyze the impact of conflict on prices from regression results.

**Parameters:**
- `coefficients`: Dictionary with coefficient estimates

**Returns:**
Dictionary with:
- `impact_per_event`: Percentage price change per conflict event
- `monthly_impact`: Average monthly conflict impact (based on ~24 events/month)
- `high_conflict_premium`: Price premium in high conflict areas
- `control_zone_effect`: Price differential by control zone
- `monthly_trend`: Monthly price trend (conflict-adjusted)
- `annual_trend`: Annual price trend

**Interpretation:**
- For log-linear models: coefficient × 100 = percentage impact
- For dummy variables: (exp(β) - 1) × 100 = percentage change

#### extract_tier2_results

```python
extract_tier2_results() -> Dict[str, Dict[str, Any]]
```

Extract and summarize Tier 2 commodity-specific results.

**Returns:**
Dictionary keyed by commodity with:
- `status`: 'completed' or 'error'
- `has_threshold`: Whether threshold was identified
- `threshold_value`: Price threshold for regime switching
- `integration_level`: Market integration classification
- `n_observations`: Sample size
- `r_squared`: Model fit

#### analyze_market_integration

```python
analyze_market_integration(panel_data: pd.DataFrame) -> Dict[str, Any]
```

Analyze market integration patterns from panel data.

**Parameters:**
- `panel_data`: Panel DataFrame with price information

**Returns:**
Dictionary containing:
- `correlation_matrix`: Market-to-market price correlations
- `high_integration_pairs`: Market pairs with correlation > 0.8
- `zone_correlations`: Cross-zone price correlations
- `integration_summary`: Summary statistics

#### generate_policy_insights

```python
generate_policy_insights(panel_data: pd.DataFrame) -> Dict[str, Any]
```

Generate policy-relevant insights from the analysis.

**Parameters:**
- `panel_data`: Panel DataFrame with price and conflict information

**Returns:**
Dictionary with:
- `price_volatility_by_conflict`: Volatility analysis by conflict regime
- `market_accessibility`: Market data coverage statistics
- `essential_commodities_trends`: Price trends for key commodities
- `geographic_disparities`: Cross-market price variation
- `key_recommendations`: List of actionable policy recommendations

#### save_analysis_report

```python
save_analysis_report(output_path: Path) -> None
```

Save a comprehensive analysis report in JSON format.

**Parameters:**
- `output_path`: Path for output file

## Usage Example

```python
from pathlib import Path
import pandas as pd
from yemen_market.models.three_tier.integration.results_analyzer import ResultsAnalyzer

# Initialize analyzer
results_dir = Path("results/three_tier_analysis_new")
analyzer = ResultsAnalyzer(results_dir)

# Load all results
analyzer.load_results()

# Extract Tier 1 coefficients
tier1_coefficients = analyzer.extract_tier1_coefficients()
print(f"Conflict impact per event: {tier1_coefficients['coefficients']['events_total'] * 100:.2f}%")

# Analyze conflict impact
conflict_analysis = analyzer.analyze_conflict_impact(tier1_coefficients)
print(f"Monthly conflict impact: {conflict_analysis['monthly_impact']:.1f}%")
print(f"High conflict premium: {conflict_analysis['high_conflict_premium']:.1f}%")

# Extract Tier 2 results
tier2_summary = analyzer.extract_tier2_results()
for commodity, results in tier2_summary.items():
    if results['status'] == 'completed':
        print(f"{commodity}: Threshold at {results.get('threshold_value', 'N/A')}")

# Load panel data for further analysis
panel_data = pd.read_parquet("data/processed/modeling_ready/panel_prepared_for_modeling.parquet")

# Analyze market integration
integration = analyzer.analyze_market_integration(panel_data)
print(f"Mean market correlation: {integration['integration_summary']['mean_correlation']:.3f}")
print(f"Highly integrated pairs: {integration['integration_summary']['n_high_integration_pairs']}")

# Generate policy insights
insights = analyzer.generate_policy_insights(panel_data)
print("\nKey Policy Recommendations:")
for i, rec in enumerate(insights['key_recommendations'], 1):
    print(f"{i}. {rec}")

# Save comprehensive report
analyzer.save_analysis_report(results_dir / "analysis_report.json")
```

## Policy Insights Framework

The analyzer generates insights across five key dimensions:

### 1. Price Volatility Analysis
- Measures price volatility by conflict intensity regime
- Identifies commodities most sensitive to conflict
- Threshold: >20% volatility triggers stabilization recommendation

### 2. Market Accessibility
- Assesses data coverage as proxy for market functioning
- Identifies markets with limited price reporting
- Threshold: <90% coverage suggests monitoring improvements

### 3. Essential Commodities
- Tracks price trends for wheat, rice, oil, sugar, and fuel
- Calculates total price changes and conflict premiums
- Threshold: >100% inflation triggers intervention recommendation

### 4. Geographic Disparities
- Measures coefficient of variation across markets
- Classifies dispersion as low (<10%), moderate (10-20%), or high (>20%)
- High dispersion suggests need for trade corridor improvements

### 5. Automated Recommendations
The system generates recommendations based on thresholds:
- Price stabilization for volatile commodities
- Market monitoring improvements
- Priority interventions for hyperinflation
- Trade corridor strengthening for high disparities
- Conflict monitoring as early warning system

## Integration with Scripts

The ResultsAnalyzer is used by analysis scripts as a thin wrapper:

```python
# In extract_and_analyze_results.py
from yemen_market.models.three_tier.integration.results_analyzer import ResultsAnalyzer

analyzer = ResultsAnalyzer(results_dir)
analyzer.load_results()
# ... perform analysis
```

## Best Practices

1. **Always load results first** before attempting extraction
2. **Check coefficient significance** before interpreting impacts
3. **Use panel data** for integration and policy analysis
4. **Save reports** for documentation and reproducibility
5. **Interpret carefully** - coefficients from log models represent semi-elasticities

## See Also

- [Three-Tier Runner](./three_tier_runner.md) - For running the analysis
- [Data Preparation](../../../features/data_preparation.md) - For preparing input data
- [Panel Diagnostics](../diagnostics/panel_diagnostics.md) - For diagnostic testing