# Three-Tier Methodology API Documentation

The three-tier methodology provides a comprehensive framework for analyzing market integration in conflict-affected settings.

## Overview

The three-tier approach addresses the challenges of 3D panel data (market × commodity × time) through:

1. **Tier 1**: Pooled panel analysis with multi-way fixed effects
2. **Tier 2**: Commodity-specific models with threshold effects
3. **Tier 3**: Factor analysis and external validation

## Main Components

### Integration Layer

#### `ThreeTierAnalysis`

The main orchestrator for running all three tiers.

```python
from yemen_market.models.three_tier.integration import ThreeTierAnalysis

# Initialize with configuration
analysis = ThreeTierAnalysis(config={
    'tier1_config': {...},
    'tier2_config': {...},
    'tier3_config': {...},
    'output_dir': 'results/'
})

# Run complete analysis
results = analysis.run_full_analysis(data, conflict_data=None)
```

**Parameters:**

- `config` (dict): Configuration for all tiers
  - `tier1_config`: Settings for pooled panel analysis
  - `tier2_config`: Settings for commodity models
  - `tier3_config`: Settings for validation models
  - `output_dir`: Directory for saving results

**Methods:**

- `run_full_analysis(data, conflict_data=None)`: Run all three tiers
- `get_commodity_comparison()`: Compare results across commodities
- `visualize_results()`: Generate visualizations (future)

#### `CrossTierValidator`

Validates consistency across tier results.

```python
from yemen_market.models.three_tier.integration import CrossTierValidator

validator = CrossTierValidator()
validation_results = validator.validate_all_tiers(
    tier1_results, tier2_results, tier3_results
)
```

### Tier 1: Pooled Panel Models

#### `PooledPanelModel`

Implements pooled panel regression with entity and time fixed effects.

```python
from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel

model = PooledPanelModel(config={
    'fixed_effects': ['entity', 'time'],
    'cluster_var': 'entity',
    'driscoll_kraay': True
})

model.fit(data)
results = model.get_results()
```

**Key Features:**

- Multi-way fixed effects (entity and time)
- Clustered standard errors
- Driscoll-Kraay correction for spatial/temporal dependence

### Tier 2: Commodity-Specific Models

#### `CommodityExtractor`

Analyzes individual commodities with threshold effects.

```python
from yemen_market.models.three_tier.tier2_commodity import CommodityExtractor

extractor = CommodityExtractor(config={
    'test_thresholds': True,
    'max_lags': 4,
    'threshold_var': 'conflict_intensity'
})

# Analyze specific commodity
wheat_results = extractor.analyze_commodity(data, 'wheat')
```

**Features:**

- Automatic threshold detection
- Regime-specific models
- Cointegration testing

### Tier 3: Validation Models

#### `StaticFactorModel`

Extracts common factors using PCA.

```python
from yemen_market.models.three_tier.tier3_validation import StaticFactorModel

model = StaticFactorModel(config={
    'n_factors': None,  # Auto-select
    'min_variance_explained': 0.8
})

model.fit(data)
factors = model.predict()
```

#### `PCAMarketIntegration`

Comprehensive PCA-based integration analysis.

```python
from yemen_market.models.three_tier.tier3_validation import PCAMarketIntegration

analyzer = PCAMarketIntegration()

# Overall integration strength
integration = analyzer.analyze_integration_strength(data)

# Rolling analysis
rolling_integration = analyzer.rolling_pca_analysis(data)

# Commodity-specific patterns
commodity_patterns = analyzer.commodity_specific_pca(data)
```

#### `ConflictIntegrationValidator`

Validates integration patterns against conflict events.

```python
from yemen_market.models.three_tier.tier3_validation import ConflictIntegrationValidator

validator = ConflictIntegrationValidator(config={
    'conflict_threshold': 10,
    'window_before': 30,
    'window_after': 30
})

validator.fit(data, conflict_data=conflict_df)
impact_summary = validator.get_conflict_impact_summary()
```

## Migration Tools

### `ModelMigrationHelper`

Helps transition from old dual-track models.

```python
from yemen_market.models.three_tier.migration import ModelMigrationHelper

migrator = ModelMigrationHelper()

# Migrate configuration
new_config = migrator.migrate_configuration(old_config)

# Migrate results
new_results = migrator.migrate_results(old_results, 'threshold_vecm')

# Validate migration
validation = migrator.validate_migration(old_results, new_results)
```

## Results Structure

All models return standardized `ResultsContainer` objects:

```python
results = model.get_results()

# Access coefficients
results.coefficients  # Dict[str, float]
results.standard_errors  # Dict[str, float]

# Model fit statistics
results.comparison_metrics.r_squared
results.comparison_metrics.aic
results.comparison_metrics.bic

# Tier-specific results
results.tier_specific  # Dict with model-specific outputs

# Metadata
results.metadata['n_observations']
results.metadata['warnings']
```

## Configuration Examples

### Complete Configuration

```python
config = {
    'tier1_config': {
        'fixed_effects': ['entity', 'time'],
        'cluster_var': 'entity',
        'driscoll_kraay': True,
        'cov_type': 'clustered'
    },
    'tier2_config': {
        'min_observations': 50,
        'test_thresholds': True,
        'max_lags': 4,
        'threshold_var': 'conflict_intensity',
        'n_regimes': 2
    },
    'tier3_config': {
        'n_factors': 3,
        'standardize': True,
        'min_variance_explained': 0.8,
        'conflict_validation': True,
        'conflict_threshold': 10,
        'window_size': 52  # For rolling analysis
    },
    'output_dir': 'results/three_tier_analysis',
    'run_parallel': False
}
```

### Minimal Configuration

```python
# Use defaults for most settings
config = {
    'output_dir': 'results/'
}

analysis = ThreeTierAnalysis(config)
```

## Data Requirements

Input data should be a pandas DataFrame with:

**Required columns:**

- `date`: datetime
- `governorate`: str (market identifier)
- `commodity`: str
- `usd_price`: float

**Optional columns:**

- `conflict_intensity`: float (for threshold analysis)
- `fatalities`: int (for conflict validation)
- Additional control variables

**Data structure:**

```python
data = pd.DataFrame({
    'date': pd.date_range('2019-01-01', periods=100, freq='W'),
    'governorate': ['Sana\'a'] * 100,
    'commodity': ['wheat'] * 100,
    'usd_price': np.random.randn(100) + 100
})
```

## Error Handling

All models use the enhanced logging system:

```python
from yemen_market.utils.logging import setup_logging

setup_logging("INFO")  # or "DEBUG" for more detail

# Errors are logged with context
try:
    analysis.run_full_analysis(data)
except ValueError as e:
    # Check logs for detailed error context
    pass
```

## Performance Considerations

- **Memory**: Large panels may require chunking
- **Speed**: Use `run_parallel=True` for faster execution (future feature)
- **Missing data**: Handled automatically with interpolation

## See Also

- [Methodology Documentation](../../models/yemen_panel_methodology.md)
- [Migration Guide](../../../../MIGRATION_GUIDE.md)
- [Example Notebooks](../../../../notebooks/04_models/)
