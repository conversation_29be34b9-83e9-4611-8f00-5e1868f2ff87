# Diagnostic Test Implementations API Reference

## Overview

The `test_implementations` module provides econometric diagnostic tests for panel data models, including tests for serial correlation, cross-sectional dependence, heteroskedasticity, unit roots, and structural breaks.

## Standard Panel Tests

### `wooldridge_serial_correlation(residuals, panel_info)`

Wooldridge test for serial correlation in panel data (Drukker 2003 implementation).

**Parameters:**
- `residuals` (pd.Series): Residuals from panel model, indexed by entity and time
- `panel_info` (dict): Panel structure information

**Returns:**
- `tuple`: (F-statistic, p-value, recommendation)

### `pesaran_cd_test(residuals, panel_info)`

Pesaran (2004, 2015) test for cross-sectional dependence in panels.

**Parameters:**
- `residuals` (pd.Series): Residuals from panel model
- `panel_info` (dict): Panel structure information

**Returns:**
- `tuple`: (test statistic, p-value, recommendation)

### `modified_wald_heteroskedasticity(residuals, panel_info, fitted_values=None)`

Modified Wald test for groupwise heteroskedasticity in panel data.

**Parameters:**
- `residuals` (pd.Series): Residuals from panel model
- `panel_info` (dict): Panel structure information
- `fitted_values` (pd.Series, optional): Fitted values from model

**Returns:**
- `tuple`: (Chi-squared statistic, p-value, recommendation)

### `breusch_pagan_lm_test(residuals, panel_info, scaled=True)`

Breusch-Pagan LM test for cross-sectional dependence (alternative to Pesaran CD).

**Parameters:**
- `residuals` (pd.Series): Residuals from panel model
- `panel_info` (dict): Panel structure information
- `scaled` (bool): Use scaled version for finite samples (default: True)

**Returns:**
- `tuple`: (test statistic, p-value, recommendation)

### `ips_unit_root_test(series, panel_info)`

Im-Pesaran-Shin (2003) panel unit root test.

**Parameters:**
- `series` (pd.Series): Series to test for unit roots
- `panel_info` (dict): Panel structure information

**Returns:**
- `tuple`: (test statistic, p-value, recommendation)

## New Advanced Tests 🆕

### `ramsey_reset_test(y, X, fitted_values, powers=[2, 3])` 🆕

Ramsey RESET test for functional form misspecification.

**Parameters:**
- `y` (pd.Series): Dependent variable
- `X` (pd.DataFrame): Independent variables from original regression
- `fitted_values` (pd.Series): Fitted values from original model
- `powers` (list): Powers of fitted values to include (default: [2, 3])

**Returns:**
- `tuple`: (F-statistic, p-value, recommendation)

**Example:**
```python
# Test for misspecification
f_stat, p_value, rec = ramsey_reset_test(y, X, model.fitted_values)
if p_value < 0.05:
    print("Evidence of functional form misspecification")
    print(f"Recommendation: {rec}")
```

### `chow_structural_break_test(data, formula, break_date, entity_col='entity', time_col='date')` 🆕

Chow test for structural break at a known date in panel data.

**Parameters:**
- `data` (pd.DataFrame): Panel data with entity and time identifiers
- `formula` (str): Model formula (e.g., 'price ~ conflict + exchange_rate')
- `break_date` (str): Date of potential structural break
- `entity_col` (str): Name of entity column
- `time_col` (str): Name of time column

**Returns:**
- `tuple`: (F-statistic, p-value, recommendation)

**Example:**
```python
# Test for structural break on Jan 1, 2021
f_stat, p_value, rec = chow_structural_break_test(
    panel_data,
    formula='log_price ~ conflict_intensity + exchange_rate',
    break_date='2021-01-01'
)
```

### `quandt_likelihood_ratio_test(data, formula, trim_pct=0.15, entity_col='entity', time_col='date')` 🆕

Quandt Likelihood Ratio (QLR) test for unknown structural break date.

**Parameters:**
- `data` (pd.DataFrame): Panel data
- `formula` (str): Model formula
- `trim_pct` (float): Percentage to trim from start/end (default: 0.15)
- `entity_col` (str): Name of entity column
- `time_col` (str): Name of time column

**Returns:**
- `tuple`: (Max F-statistic, break date, p-value approximation, recommendation)

**Example:**
```python
# Find unknown structural break
max_f, break_date, p_val, rec = quandt_likelihood_ratio_test(
    panel_data,
    formula='log_price ~ conflict_intensity + exchange_rate'
)
print(f"Most likely break date: {break_date}")
```

## Usage in Diagnostic Framework

These tests are integrated into the `ThreeTierPanelDiagnostics` class:

```python
from yemen_market.models.three_tier.diagnostics import ThreeTierPanelDiagnostics

# Run diagnostics for Tier 1
diagnostics = ThreeTierPanelDiagnostics(tier=1)
report = diagnostics.run_diagnostics(
    results_container,
    panel_data,
    diagnostic_config={
        'tests_to_run': [
            'serial_correlation',
            'cross_sectional_dependence',
            'heteroskedasticity',
            'ramsey_reset',
            'structural_breaks'
        ],
        'structural_break_dates': ['2021-01-01', '2022-03-01']
    }
)

# Check if corrections are needed
if report.has_critical_failures():
    print("Model requires corrections")
    for test in report.failed_tests:
        print(f"Failed: {test.name} (p={test.p_value:.4f})")
```

## Recommendations by Test Result

### Serial Correlation Detected
- Use cluster-robust standard errors
- Consider Driscoll-Kraay standard errors
- For severe cases: dynamic panel models (Arellano-Bond)

### Cross-Sectional Dependence Detected
- Use Driscoll-Kraay standard errors (strongly recommended)
- Consider spatial panel models
- Common correlated effects (CCE) estimators

### Heteroskedasticity Detected
- Use robust standard errors clustered by entity
- Consider weighted least squares
- Feasible GLS if efficiency is important

### Functional Form Misspecification
- Add quadratic/interaction terms
- Try log transformations of variables
- Consider different model specifications

### Structural Break Detected
- Estimate separate models for each period
- Use time-varying parameter models
- Add interaction terms with period indicators