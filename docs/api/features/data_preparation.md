# Data Preparation API Reference

## Overview

The `data_preparation` module provides functions for preparing panel data for econometric analysis, including data quality assessment, outlier treatment, stationarity testing, and feature engineering.

## Functions

### `generate_data_quality_report(panel)`

Generate comprehensive data quality metrics for the panel dataset.

**Parameters:**
- `panel` (pd.DataFrame): The panel DataFrame to analyze

**Returns:**
- `dict`: Quality metrics including coverage, extreme values, and conflict statistics

### `winsorize_prices(panel, limits=(0.01, 0.01))`

Winsorize extreme price values by commodity to handle outliers.

**Parameters:**
- `panel` (pd.DataFrame): Panel DataFrame with price data
- `limits` (tuple): Lower and upper percentiles for winsorization

**Returns:**
- `pd.DataFrame`: Panel with winsorized price columns added

### `test_panel_stationarity(panel, min_obs=30)`

Run panel unit root tests on price series.

**Parameters:**
- `panel` (pd.DataFrame): Panel DataFrame with price data
- `min_obs` (int): Minimum observations required for testing

**Returns:**
- `tuple`: (detailed_results, summary_statistics)

### `define_conflict_regimes(panel)`

Define conflict intensity regimes based on distribution quantiles.

**Parameters:**
- `panel` (pd.DataFrame): Panel DataFrame with conflict data

**Returns:**
- `pd.DataFrame`: Panel with conflict regime indicators

### `add_econometric_features(panel)`

Add standard econometric features including log transformations and time trends.

**Parameters:**
- `panel` (pd.DataFrame): Panel DataFrame

**Returns:**
- `pd.DataFrame`: Panel with econometric features added

### `add_spatial_features(panel, k=3, distance_matrix=None)` 🆕

Add K-nearest neighbor spatial features to panel data.

**Parameters:**
- `panel` (pd.DataFrame): Panel data with market locations
- `k` (int): Number of nearest neighbors (default: 3)
- `distance_matrix` (pd.DataFrame, optional): Pre-calculated distance matrix

**Returns:**
- `pd.DataFrame`: Panel with spatial features:
  - `spatial_lag_price`: Average price of k nearest neighbors
  - `spatial_lag_conflict`: Average conflict of k nearest neighbors
  - `inverse_distance_weighted_price`: Distance-weighted average price
  - `spatial_price_deviation`: Deviation from spatial average

**Example:**
```python
# Add spatial features with 3 nearest neighbors
panel_with_spatial = add_spatial_features(panel, k=3)

# Use pre-calculated distance matrix
distance_matrix = calculate_market_distances(panel)
panel_with_spatial = add_spatial_features(panel, k=5, distance_matrix=distance_matrix)
```

### `calculate_market_distances(markets_df)` 🆕

Calculate haversine distances between all market pairs.

**Parameters:**
- `markets_df` (pd.DataFrame): DataFrame with columns: market, latitude, longitude

**Returns:**
- `pd.DataFrame`: Distance matrix with markets as both index and columns

### `add_exchange_rate_features(panel)` 🆕

Add dual exchange rate system features for Yemen's parallel currency markets.

**Parameters:**
- `panel` (pd.DataFrame): Panel data with exchange rate information

**Returns:**
- `pd.DataFrame`: Panel with exchange rate features:
  - `er_premium`: Premium of parallel over official rate (%)
  - `er_premium_x_DFA`: Interaction with DFA control zones
  - `er_premium_ma3`: 3-month moving average of premium
  - `er_volatility`: Rolling standard deviation of exchange rate
  - `log_parallel_rate`, `log_official_rate`: Log transformations

**Example:**
```python
# Add exchange rate features
panel_with_er = add_exchange_rate_features(panel)

# Access the premium
print(f"Average ER premium: {panel_with_er['er_premium'].mean():.1f}%")
```

### `validate_for_modeling(panel, strict=False)`

Validate panel is ready for econometric modeling.

**Parameters:**
- `panel` (pd.DataFrame): Panel DataFrame to validate
- `strict` (bool): Whether to use strict academic standards (default: False)

**Returns:**
- `dict`: Validation results with valid flag, errors, and warnings

### `save_prepared_data(panel, output_dir)`

Save prepared panel data and metadata.

**Parameters:**
- `panel` (pd.DataFrame): Prepared panel DataFrame
- `output_dir` (Path): Output directory for saving files

## Usage Example

```python
from yemen_market.features.data_preparation import (
    generate_data_quality_report,
    winsorize_prices,
    add_spatial_features,
    add_exchange_rate_features,
    validate_for_modeling
)

# Load panel data
panel = pd.read_parquet("data/processed/panel.parquet")

# Generate quality report
quality = generate_data_quality_report(panel)
print(f"Price coverage: {quality['price_coverage']}")

# Winsorize prices
panel = winsorize_prices(panel, limits=(0.01, 0.01))

# Add spatial features (NEW)
panel = add_spatial_features(panel, k=3)

# Add exchange rate features (NEW)
panel = add_exchange_rate_features(panel)

# Validate for modeling
validation = validate_for_modeling(panel, strict=False)
if validation['valid']:
    print("Panel is ready for modeling!")
```

## Notes

- Spatial features require market coordinates (latitude, longitude)
- Exchange rate features can derive rates from price/usd_price ratio if explicit rates unavailable
- Winsorization is performed by commodity to account for different price scales
- Validation has both strict (academic) and relaxed (policy) modes