# PanelBuilder API Reference

The `PanelBuilder` class builds integrated panel datasets for econometric analysis by combining price, conflict, and spatial data.

## Class: PanelBuilder

```python
from yemen_market.data import PanelBuilder

builder = PanelBuilder(
    start_date=None,
    end_date=None,
    commodities=None,
    use_smart_panel=True
)
```

### Parameters

- **start_date** (str, optional): Start date for panel. Defaults to config.
- **end_date** (str, optional): End date for panel. Defaults to config.
- **commodities** (list, optional): List of commodities to include.
- **use_smart_panel** (bool): Use smart panel data if available (default: True).

### Methods

#### create_core_balanced_panel()

Create a perfectly balanced panel by selecting core markets and commodities.

```python
balanced_panel = builder.create_core_balanced_panel(
    min_coverage_pct=85.0,
    min_markets=20,
    min_commodities=15
)
```

**Parameters:**

- **min_coverage_pct** (float): Minimum coverage percentage for selection
- **min_markets** (int): Minimum number of markets for a commodity
- **min_commodities** (int): Minimum number of commodities for a market

**Returns:**

- **balanced_panel** (DataFrame): Perfectly balanced panel (21×16×75)

#### integrate_panel_data()

Integrate conflict, control zones, and geographic data into panel.

```python
integrated_panel = builder.integrate_panel_data(panel)
```

**Parameters:**

- **panel** (DataFrame): Base panel DataFrame

**Returns:**

- **integrated_panel** (DataFrame): Panel with all additional data integrated

#### load_balanced_panel()

Load pre-created balanced panel dataset.

```python
panel = builder.load_balanced_panel(panel_type='filled')
```

**Parameters:**

- **panel_type** (str): Type of panel to load ('filled' or 'raw')

**Returns:**

- **panel** (DataFrame): Balanced panel DataFrame

#### load_integrated_panel()

Load the fully integrated balanced panel with conflict and control zone data.

```python
panel = builder.load_integrated_panel()
```

**Returns:**

- **panel** (DataFrame): Integrated balanced panel DataFrame

#### validate_balanced_panel()

Validate the balanced panel structure and quality.

```python
validation_results = builder.validate_balanced_panel(panel)
```

**Parameters:**

- **panel** (DataFrame): Panel DataFrame to validate

**Returns:**

- **validation_results** (dict): Dictionary with validation metrics

#### save_balanced_panels()

Save balanced panel in multiple formats with metadata.

```python
saved_paths = builder.save_balanced_panels(
    balanced_panel,
    output_dir=None
)
```

**Parameters:**

- **balanced_panel** (DataFrame): The balanced panel DataFrame
- **output_dir** (Path, optional): Output directory

**Returns:**

- **saved_paths** (dict): Dictionary of saved file paths

#### build_integrated_panel()

Build the main integrated panel dataset.

```python
panel = builder.build_integrated_panel(
    include_conflict=True,
    include_spatial=True,
    save_output=True
)
```

**Parameters:**

- **include_conflict** (bool): Include conflict metrics
- **include_spatial** (bool): Include spatial features
- **save_output** (bool): Save to disk

**Returns:**

- **panel** (DataFrame): Integrated panel dataset

#### build_threshold_panel()

Build panel specifically for threshold cointegration analysis.

```python
threshold_panel = builder.build_threshold_panel(
    price_pairs=None,
    save_output=True
)
```

**Parameters:**

- **price_pairs** (list, optional): Specific market pairs to analyze
- **save_output** (bool): Save to disk

**Returns:**

- **threshold_panel** (DataFrame): Panel for threshold analysis

#### build_spatial_panel()

Build panel for spatial price transmission analysis.

```python
spatial_panel = builder.build_spatial_panel(
    max_distance_km=200,
    save_output=True
)
```

**Parameters:**

- **max_distance_km** (float): Maximum distance between markets
- **save_output** (bool): Save to disk

**Returns:**

- **spatial_panel** (DataFrame): Panel for spatial analysis

#### load_component_data()

Load individual data components.

```python
prices, conflict, spatial = builder.load_component_data()
```

**Returns:**

- **prices** (DataFrame): Price data
- **conflict** (DataFrame): Conflict metrics
- **spatial** (DataFrame): Spatial relationships

### Panel Structures

#### Integrated Panel

Main panel with all features:

```python
# Identifiers
- date                  # Date of observation
- market_id            # Unique market identifier
- commodity            # Commodity name

# Price data
- price_usd            # Price in USD
- price_local          # Price in local currency
- exchange_rate        # Exchange rate

# Conflict metrics
- conflict_events      # Number of events
- conflict_intensity   # Log-transformed events
- conflict_fatalities  # Total fatalities

# Spatial features
- control_zone         # Current control zone
- is_boundary_market   # Boundary market indicator
- distance_to_boundary  # Distance to nearest boundary
```

#### Threshold Panel

Panel for pairwise market analysis:

```python
# Identifiers
- date                  # Date
- market_pair          # Market pair ID
- commodity            # Commodity

# Prices
- price_origin         # Price in origin market
- price_destination    # Price in destination market
- price_differential   # Price difference

# Threshold variables
- conflict_threshold   # High conflict indicator
- distance_km         # Distance between markets
- same_zone           # Same control zone indicator
```

### Example Usage

#### Standard Panel Workflow

```python
from yemen_market.data import PanelBuilder

# Initialize builder
builder = PanelBuilder(
    start_date='2019-01-01',
    use_smart_panel=True  # Use 88.4% coverage panel
)

# Build integrated panel
panel = builder.build_integrated_panel(
    include_conflict=True,
    include_spatial=True
)

print(f"Panel shape: {panel.shape}")
print(f"Markets: {panel['market_id'].nunique()}")
print(f"Commodities: {panel['commodity'].nunique()}")

# Build threshold analysis panel
threshold = builder.build_threshold_panel()
print(f"Market pairs: {threshold['market_pair'].nunique()}")

# Build spatial panel
spatial = builder.build_spatial_panel(max_distance_km=200)
print(f"Spatial observations: {len(spatial)}")
```

#### Balanced Panel Workflow (Recommended for Econometrics)

```python
from yemen_market.data import PanelBuilder

# Initialize builder
builder = PanelBuilder()

# Create perfectly balanced panel
balanced_panel = builder.create_core_balanced_panel(
    min_coverage_pct=85.0,
    min_markets=20,
    min_commodities=15
)
print(f"Balanced panel: {balanced_panel.shape}")  # (25200, 15)

# Integrate conflict and control zone data
integrated_panel = builder.integrate_panel_data(balanced_panel)
print(f"Integrated panel: {integrated_panel.shape}")  # (25200, 44)

# Validate the panel
validation = builder.validate_balanced_panel(integrated_panel)
print(f"Is balanced: {validation['is_balanced']}")
print(f"Conflict coverage: {validation['conflict_coverage']}")

# Save the panel
saved_paths = builder.save_balanced_panels(integrated_panel)

# Or load pre-created panel
panel = builder.load_integrated_panel()
```

### Data Integration

The builder handles:

1. **Temporal alignment**: Ensures all data is properly aligned by date
2. **Missing data**: Handles structural missing data appropriately
3. **Panel balance**: Creates balanced panels where needed
4. **Feature creation**: Adds derived features for analysis

### Output Files

- **integrated_panel.parquet**: Main analysis panel
- **threshold_coint_panel.parquet**: Threshold analysis panel
- **spatial_panel.parquet**: Spatial analysis panel
- **price_transmission_panel.parquet**: Price transmission panel
- **panel_metadata.json**: Panel construction metadata

### Quality Metrics

- Panel balance statistics
- Missing data patterns
- Coverage by commodity and market
- Temporal consistency checks
