# Yemen Market Integration v2 - Architecture Proposal

## Executive Summary

This proposal outlines a complete architectural redesign of the Yemen Market Integration system, addressing fundamental limitations in v1 while preserving its scientific rigor and econometric excellence. The v2 architecture embraces modern software engineering principles to create a maintainable, scalable, and extensible system.

## Motivation for v2

### Current Limitations in v1

1. **Monolithic Coupling**: The three-tier model is tightly integrated, making modifications difficult
2. **Testing Challenges**: Large files (1900+ lines) with mixed responsibilities are hard to test
3. **Limited Extensibility**: Adding new models or data sources requires core code changes
4. **Performance Bottlenecks**: Sequential processing with limited parallelization
5. **Configuration Sprawl**: Settings scattered across multiple locations
6. **Rigid Data Pipeline**: Fixed processing flow with limited flexibility

### v2 Vision

A modern, cloud-native architecture that:
- Separates concerns clearly using Domain-Driven Design
- Enables parallel and distributed processing
- Supports plugin-based extensibility
- Provides comprehensive observability
- Maintains backward compatibility
- Improves developer experience

## Architecture Overview

### Core Principles

1. **Hexagonal Architecture** (Ports & Adapters)
   - Core domain logic independent of infrastructure
   - Clear boundaries between layers
   - Dependency inversion throughout

2. **Domain-Driven Design**
   - Bounded contexts for different domains
   - Rich domain models
   - Ubiquitous language

3. **Event-Driven Architecture**
   - Loose coupling through events
   - Async processing by default
   - Event sourcing for audit trails

4. **CQRS Pattern**
   - Separate read and write models
   - Optimized query performance
   - Flexible reporting

## Proposed Structure

```
yemen-market-integration-v2/
│
├── src/
│   ├── core/                      # Domain Layer (No external dependencies)
│   │   ├── domain/
│   │   │   ├── market/           # Market bounded context
│   │   │   │   ├── entities/
│   │   │   │   │   ├── market.py
│   │   │   │   │   └── price_observation.py
│   │   │   │   ├── value_objects/
│   │   │   │   │   ├── market_id.py
│   │   │   │   │   ├── coordinates.py
│   │   │   │   │   └── price.py
│   │   │   │   ├── repositories/
│   │   │   │   │   └── market_repository.py
│   │   │   │   └── services/
│   │   │   │       └── price_transmission_service.py
│   │   │   │
│   │   │   ├── conflict/         # Conflict bounded context
│   │   │   │   ├── entities/
│   │   │   │   │   └── conflict_event.py
│   │   │   │   ├── value_objects/
│   │   │   │   │   └── intensity.py
│   │   │   │   └── services/
│   │   │   │       └── conflict_analysis_service.py
│   │   │   │
│   │   │   ├── geography/        # Geographic bounded context
│   │   │   │   ├── entities/
│   │   │   │   │   └── geographic_zone.py
│   │   │   │   └── services/
│   │   │   │       └── spatial_analysis_service.py
│   │   │   │
│   │   │   └── shared/
│   │   │       ├── events/
│   │   │       │   ├── base_event.py
│   │   │       │   └── domain_events.py
│   │   │       └── exceptions/
│   │   │           └── domain_exceptions.py
│   │   │
│   │   └── models/               # Econometric models as first-class citizens
│   │       ├── interfaces/
│   │       │   ├── model.py
│   │       │   └── estimator.py
│   │       ├── panel/
│   │       │   ├── pooled_panel.py
│   │       │   └── fixed_effects.py
│   │       └── time_series/
│   │           ├── vecm.py
│   │           └── threshold_vecm.py
│   │
│   ├── application/              # Application Layer (Use Cases)
│   │   ├── commands/
│   │   │   ├── analyze_market_integration.py
│   │   │   ├── calculate_price_transmission.py
│   │   │   └── run_three_tier_analysis.py
│   │   ├── queries/
│   │   │   ├── get_market_prices.py
│   │   │   ├── get_conflict_metrics.py
│   │   │   └── get_analysis_results.py
│   │   ├── services/
│   │   │   ├── analysis_orchestrator.py
│   │   │   ├── data_preparation_service.py
│   │   │   └── reporting_service.py
│   │   └── dto/
│   │       └── data_transfer_objects.py
│   │
│   ├── infrastructure/           # Infrastructure Layer
│   │   ├── persistence/
│   │   │   ├── repositories/
│   │   │   │   ├── postgres/
│   │   │   │   │   ├── market_repository.py
│   │   │   │   │   └── price_repository.py
│   │   │   │   └── mongodb/
│   │   │   │       └── analysis_results_repository.py
│   │   │   └── migrations/
│   │   │
│   │   ├── external_services/
│   │   │   ├── hdx_client.py
│   │   │   ├── wfp_client.py
│   │   │   └── acled_client.py
│   │   │
│   │   ├── caching/
│   │   │   ├── redis_cache.py
│   │   │   └── memory_cache.py
│   │   │
│   │   ├── messaging/
│   │   │   ├── event_bus.py
│   │   │   └── rabbitmq_adapter.py
│   │   │
│   │   └── observability/
│   │       ├── logging/
│   │       ├── metrics/
│   │       └── tracing/
│   │
│   ├── interfaces/               # Interface Layer (Entry Points)
│   │   ├── api/
│   │   │   ├── rest/
│   │   │   │   ├── app.py      # FastAPI application
│   │   │   │   ├── routes/
│   │   │   │   └── middleware/
│   │   │   └── graphql/
│   │   │       └── schema.py
│   │   │
│   │   ├── cli/
│   │   │   ├── app.py          # Typer CLI application
│   │   │   └── commands/
│   │   │
│   │   └── notebooks/           # Jupyter integration
│   │       └── kernel.py
│   │
│   └── shared/                   # Cross-cutting concerns
│       ├── container.py          # Dependency injection container
│       ├── config/
│       │   ├── settings.py       # Pydantic settings
│       │   └── environments/
│       ├── utils/
│       └── constants/
│
├── plugins/                      # Plugin System
│   ├── models/                   # Pluggable models
│   │   ├── __plugin_interface.py
│   │   └── custom_vecm/
│   ├── data_sources/            # Pluggable data sources
│   │   ├── __plugin_interface.py
│   │   └── world_bank_adapter/
│   └── outputs/                 # Pluggable outputs
│       ├── __plugin_interface.py
│       └── latex_report/
│
├── tests/
│   ├── unit/                    # Unit tests by layer
│   │   ├── core/
│   │   ├── application/
│   │   └── infrastructure/
│   ├── integration/             # Integration tests
│   ├── e2e/                     # End-to-end tests
│   └── performance/             # Performance benchmarks
│
├── deployment/
│   ├── docker/
│   │   ├── Dockerfile
│   │   └── docker-compose.yml
│   ├── kubernetes/
│   │   ├── deployments/
│   │   └── services/
│   └── terraform/               # Infrastructure as Code
│
├── docs/
│   ├── architecture/           # ADRs and design docs
│   ├── api/                    # API documentation
│   └── user_guide/             # User documentation
│
└── tools/
    ├── migration/              # v1 to v2 migration tools
    └── development/            # Dev tools and scripts
```

## Key Components

### 1. Domain Layer

```python
# src/core/domain/market/entities/market.py
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

from ..value_objects import MarketId, Coordinates, MarketType
from ...shared.entities import AggregateRoot
from ...shared.events import DomainEvent

@dataclass
class Market(AggregateRoot):
    """Market aggregate root - represents a physical market location."""
    
    id: MarketId
    name: str
    coordinates: Coordinates
    market_type: MarketType
    governorate: str
    district: str
    active_since: datetime
    active_until: Optional[datetime] = None
    
    def __post_init__(self):
        super().__init__()
        if self.active_since > datetime.now():
            self.add_event(MarketCreatedEvent(market_id=self.id))
    
    def deactivate(self, reason: str) -> None:
        """Deactivate market with reason."""
        if self.active_until is not None:
            raise ValueError("Market already deactivated")
        
        self.active_until = datetime.now()
        self.add_event(MarketDeactivatedEvent(
            market_id=self.id,
            reason=reason,
            deactivated_at=self.active_until
        ))
    
    def is_active_at(self, date: datetime) -> bool:
        """Check if market was active at given date."""
        if date < self.active_since:
            return False
        if self.active_until and date > self.active_until:
            return False
        return True
```

### 2. Application Layer

```python
# src/application/commands/analyze_market_integration.py
from dataclasses import dataclass
from typing import Dict, Any
from datetime import datetime

from ...core.domain.market.services import MarketIntegrationService
from ...core.domain.market.repositories import MarketRepository
from ..services.analysis_orchestrator import AnalysisOrchestrator

@dataclass
class AnalyzeMarketIntegrationCommand:
    """Command to analyze market integration."""
    start_date: datetime
    end_date: datetime
    market_ids: List[str]
    commodity_ids: List[str]
    analysis_config: Dict[str, Any]

class AnalyzeMarketIntegrationHandler:
    """Handler for market integration analysis command."""
    
    def __init__(
        self,
        market_repo: MarketRepository,
        integration_service: MarketIntegrationService,
        orchestrator: AnalysisOrchestrator
    ):
        self.market_repo = market_repo
        self.integration_service = integration_service
        self.orchestrator = orchestrator
    
    async def handle(self, command: AnalyzeMarketIntegrationCommand) -> str:
        """Execute market integration analysis."""
        # Validate markets exist
        markets = await self.market_repo.find_by_ids(command.market_ids)
        
        # Orchestrate analysis
        analysis_id = await self.orchestrator.start_analysis(
            markets=markets,
            start_date=command.start_date,
            end_date=command.end_date,
            config=command.analysis_config
        )
        
        return analysis_id
```

### 3. Plugin System

```python
# plugins/__plugin_interface.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Type

class ModelPlugin(ABC):
    """Base interface for model plugins."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Unique plugin name."""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Plugin version."""
        pass
    
    @abstractmethod
    def get_model_class(self) -> Type:
        """Return the model class."""
        pass
    
    @abstractmethod
    def get_default_config(self) -> Dict[str, Any]:
        """Return default configuration."""
        pass

# plugins/models/custom_vecm/plugin.py
class CustomVECMPlugin(ModelPlugin):
    """Custom VECM implementation plugin."""
    
    @property
    def name(self) -> str:
        return "custom_vecm"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    def get_model_class(self) -> Type:
        from .model import CustomVECM
        return CustomVECM
    
    def get_default_config(self) -> Dict[str, Any]:
        return {
            "max_lags": 4,
            "deterministic": "ci",
            "seasons": 12
        }
```

### 4. API Layer

```python
# src/interfaces/api/rest/app.py
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware

from .routes import markets, analysis, reports
from ...shared.container import Container

def create_app(container: Container) -> FastAPI:
    """Create FastAPI application."""
    app = FastAPI(
        title="Yemen Market Integration API",
        version="2.0.0",
        docs_url="/api/docs"
    )
    
    # Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_methods=["*"],
        allow_headers=["*"]
    )
    
    # Dependency injection
    app.state.container = container
    
    # Routes
    app.include_router(markets.router, prefix="/api/v2/markets")
    app.include_router(analysis.router, prefix="/api/v2/analysis")
    app.include_router(reports.router, prefix="/api/v2/reports")
    
    # Health check
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "version": "2.0.0"}
    
    return app
```

## Migration Strategy

### Phase 1: Foundation (Weeks 1-2)
1. Set up v2 project structure
2. Implement core domain models
3. Create basic infrastructure
4. Set up testing framework

### Phase 2: Core Features (Weeks 3-4)
1. Implement data ingestion pipeline
2. Create analysis commands and queries
3. Build REST API
4. Develop plugin system

### Phase 3: Model Migration (Weeks 5-6)
1. Wrap v1 models in adapter pattern
2. Implement new model interfaces
3. Create model comparison framework
4. Validate results parity

### Phase 4: Integration (Weeks 7-8)
1. Build v1 compatibility layer
2. Implement data migration tools
3. Create comprehensive test suite
4. Performance optimization

### Phase 5: Deployment (Weeks 9-10)
1. Containerize application
2. Set up CI/CD pipeline
3. Deploy to staging environment
4. User acceptance testing

## Key Benefits

### 1. Maintainability
- Clear separation of concerns
- Small, focused modules
- Comprehensive testing
- Self-documenting code

### 2. Extensibility
- Plugin architecture for models
- Easy to add data sources
- Flexible output formats
- API-first design

### 3. Performance
- Async processing throughout
- Intelligent caching
- Parallel execution
- Optimized queries

### 4. Operations
- Built-in observability
- Health checks and metrics
- Distributed tracing
- Centralized logging

### 5. Developer Experience
- Type safety with Pydantic
- Dependency injection
- Comprehensive documentation
- Easy local development

## Technology Stack

### Core Technologies
- **Language**: Python 3.11+
- **Framework**: FastAPI for API, Typer for CLI
- **Async**: asyncio, aiohttp
- **Validation**: Pydantic v2
- **DI Container**: dependency-injector

### Data & Storage
- **Relational**: PostgreSQL 15+ with asyncpg
- **Document**: MongoDB for results
- **Cache**: Redis for caching
- **Object Storage**: S3 for large datasets

### Infrastructure
- **Container**: Docker
- **Orchestration**: Kubernetes
- **Message Queue**: RabbitMQ
- **Monitoring**: Prometheus + Grafana
- **Tracing**: OpenTelemetry + Jaeger

### Development
- **Testing**: pytest-asyncio
- **Linting**: ruff
- **Type Checking**: mypy
- **Documentation**: mkdocs

## Success Metrics

1. **Code Quality**
   - 100% type coverage
   - >95% test coverage
   - <5 cyclomatic complexity
   - No files >300 lines

2. **Performance**
   - 10x faster analysis execution
   - <100ms API response time
   - Support for 1M+ observations
   - Horizontal scalability

3. **Developer Productivity**
   - <5 min setup time
   - <1 min test execution
   - Self-documenting APIs
   - Plugin development <1 day

## Risk Mitigation

1. **Technical Risks**
   - Maintain v1 compatibility layer
   - Extensive integration testing
   - Performance benchmarking
   - Gradual rollout

2. **Operational Risks**
   - Comprehensive monitoring
   - Automated rollback
   - Feature flags
   - Canary deployments

3. **Adoption Risks**
   - Maintain familiar interfaces
   - Comprehensive documentation
   - Training materials
   - Migration tools

## Conclusion

Yemen Market Integration v2 represents a fundamental architectural improvement while preserving the scientific rigor of v1. The new architecture provides a solid foundation for future enhancements, better performance, and improved developer experience. The modular, plugin-based design ensures the system can evolve with changing requirements while maintaining stability and reliability.