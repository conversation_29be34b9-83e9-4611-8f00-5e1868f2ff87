# Academic Approach to Yemen Market Integration

## Theoretical Framework

### 1. **Start with Economic Theory**

Before touching any data, we must establish our theoretical priors based on:

- **Spatial Equilibrium Theory** (<PERSON><PERSON><PERSON> & <PERSON>, 1971)
- **Law of One Price** with transaction costs (<PERSON><PERSON><PERSON> & Goodwin, 2001)
- **Threshold Autoregression** (<PERSON>, 1999; Balke & F<PERSON>y, 1997)
- **Panel Cointegration** (<PERSON>, 2004; <PERSON>, 2007)

Key equation:
```
p_{it} - p_{jt} = τ_{ijt} + ε_{ijt}
```
Where τ represents transaction costs that may be regime-dependent.

### 2. **Methodological Purity Requirements**

#### A. Data Cleaning Protocol
```python
# Academic standards for data quality
def prepare_academic_dataset(df):
    # 1. Remove ALL missing observations
    df_clean = df.dropna()
    
    # 2. Balance the panel perfectly
    entity_counts = df_clean.groupby(['market', 'commodity']).size()
    max_periods = entity_counts.max()
    complete_entities = entity_counts[entity_counts == max_periods].index
    df_balanced = df_clean[df_clean.set_index(['market', 'commodity']).index.isin(complete_entities)]
    
    # 3. Remove outliers using <PERSON><PERSON>'s method
    for commodity in df_balanced['commodity'].unique():
        mask = df_balanced['commodity'] == commodity
        Q1 = df_balanced.loc[mask, 'price'].quantile(0.25)
        Q3 = df_balanced.loc[mask, 'price'].quantile(0.75)
        IQR = Q3 - Q1
        lower = Q1 - 1.5 * IQR
        upper = Q3 + 1.5 * IQR
        df_balanced.loc[mask & ((df_balanced['price'] < lower) | (df_balanced['price'] > upper)), 'price'] = np.nan
    
    df_final = df_balanced.dropna()
    
    # 4. Require minimum panel dimensions
    if df_final.groupby(['market', 'commodity']).size().min() < 100:
        raise ValueError("Insufficient observations for asymptotic theory")
    
    return df_final
```

#### B. Pre-Testing Requirements
1. **Unit Root Tests** (Must test all before proceeding)
   - Levin-Lin-Chu (2002)
   - Im-Pesaran-Shin (2003)
   - Fisher-type tests (Maddala & Wu, 1999)
   - Cross-sectionally augmented IPS (Pesaran, 2007)

2. **Cointegration Tests**
   - Pedroni (1999, 2004) seven statistics
   - Kao (1999) residual-based test
   - Westerlund (2007) error-correction tests

3. **Cross-Sectional Dependence**
   - Pesaran (2004) CD test
   - Pesaran (2015) weak cross-sectional dependence
   - Bailey, Kapetanios & Pesaran (2016) exponent of cross-sectional dependence

### 3. **Estimation Strategy**

#### Stage 1: Test Everything
```python
def academic_testing_battery(df):
    results = {}
    
    # 1. Panel unit root tests
    for test in ['llc', 'ips', 'fisher_adf', 'fisher_pp', 'cips']:
        results[f'unit_root_{test}'] = panel_unit_root_test(df, method=test)
        if results[f'unit_root_{test}']['p_value'] > 0.05:
            raise ValueError(f"Cannot reject unit root with {test}. Cannot proceed.")
    
    # 2. Cointegration tests (if I(1))
    for test in ['pedroni_7stats', 'kao', 'westerlund_ecm']:
        results[f'coint_{test}'] = panel_cointegration_test(df, method=test)
    
    # 3. Cross-sectional dependence
    cd_test = pesaran_cd_test(df)
    if cd_test['p_value'] < 0.05:
        print("WARNING: Cross-sectional dependence detected. Standard errors may be biased.")
        print("Consider Driscoll-Kraay (1998) or Pesaran (2006) CCE estimators.")
    
    # 4. Heteroskedasticity tests
    results['heteroskedasticity'] = modified_wald_test(df)
    
    # 5. Serial correlation
    results['serial_correlation'] = wooldridge_test(df)
    
    return results
```

#### Stage 2: Model Selection Based on Test Results
```python
def select_academic_model(test_results):
    if test_results['unit_root_ips']['stationary']:
        if test_results['heteroskedasticity']['reject_null']:
            return "Fixed Effects with HAC standard errors"
        else:
            return "Standard Fixed Effects"
    else:
        if test_results['coint_pedroni']['cointegrated']:
            if test_results['cd_test']['reject_null']:
                return "Panel VECM with common factors"
            else:
                return "Panel VECM"
        else:
            return "First-difference model with appropriate dynamics"
```

### 4. **Robustness Checks (Mandatory)**

Every academic paper requires:

1. **Alternative Specifications**
   ```python
   specifications = {
       'baseline': 'two_way_fixed_effects',
       'robustness_1': 'random_effects_mundlak',
       'robustness_2': 'first_differences',
       'robustness_3': 'system_gmm',
       'robustness_4': 'mean_group_estimator',
       'robustness_5': 'common_correlated_effects'
   }
   ```

2. **Sample Sensitivity**
   - Drop 10% of observations randomly (100 times)
   - Exclude one governorate at a time
   - Exclude one commodity at a time
   - Rolling window estimation

3. **Inference Robustness**
   - Bootstrap (pairs, wild, block)
   - Permutation tests
   - Randomization inference

### 5. **Publication Requirements**

#### A. Summary Statistics Table
```
Table 1: Descriptive Statistics
=====================================
Variable      Mean   SD    Min    Max   Skew   Kurt   JB-test
Price        12.45  3.21  5.23  45.67  2.31   8.45   345.2***
Market FE    Yes    -     -     -      -      -      -
Time FE      Yes    -     -     -      -      -      -
N            15,000 (perfectly balanced)
=====================================
Note: *** p<0.01, ** p<0.05, * p<0.1
```

#### B. Main Results Table
Must include:
- At least 5 different specifications
- Multiple standard error calculations in parentheses
- R², within-R², between-R²
- F-statistics for joint significance
- Hausman test results

#### C. Robustness Appendix
- 20-30 pages of alternative specifications
- Sensitivity analysis graphs
- Pre-trend tests
- Placebo tests

### 6. **Theoretical Contribution Requirements**

The paper must:
1. Extend existing theory in some way
2. Derive testable implications
3. Show your model nests previous models as special cases
4. Provide Monte Carlo evidence of estimator properties

### 7. **Why This Data Won't Work (Academic Perspective)**

After applying academic standards to the Yemen data:

```python
# Reality check
original_obs = 44,122
after_removing_missing = 28,456  # Lost 35%
after_balancing = 8,234          # Lost 81%
after_outlier_removal = 7,123    # Lost 84%
after_min_obs_requirement = 0    # Lost 100%

conclusion = "This dataset is unsuitable for publication in a top journal"
```

**Academic Verdict**: 
- "The data quality issues are insurmountable"
- "Cannot satisfy identification requirements"
- "Recommend collecting new data with proper experimental design"
- "Perhaps simulate the ideal dataset instead?"

### 8. **What An Academic Would Actually Do**

1. **Pivot to Simulation**
   ```python
   def simulate_ideal_yemen_data():
       # Generate perfectly balanced panel
       # Add conflicts as exogenous shocks
       # Control all confounders
       # Ensure all asymptotic requirements met
   ```

2. **Focus on One Tiny Aspect**
   - "The Effect of Thursday Market Closures on Price Dispersion: Evidence from One Market in Yemen"
   - Requires 200+ pages of theory and proofs

3. **Wait for Better Data**
   - Apply for 3-year grant
   - Design ideal data collection
   - Ignore current humanitarian crisis

### 9. **Academic Publication Strategy**

Target journals in order:
1. *American Economic Review* (if significant theoretical contribution)
2. *Journal of Development Economics* (if clever identification)
3. *World Development* (if policy relevant but rigorous)
4. Working paper forever (most likely outcome)

### 10. **Conclusion: The Academic Paradox**

By maintaining methodological purity:
- We ensure scientific rigor
- We satisfy peer reviewers
- We advance economic theory
- We contribute nothing to solving Yemen's humanitarian crisis

The perfect becomes the enemy of the good, and policy makers learn nothing useful while waiting for the "definitive" study that will never come.

## Alternative: "Middle Ground" Academic Approach

Some pragmatic academics might:
1. Acknowledge all limitations upfront
2. Use multiple imputation for missing data
3. Apply bounds analysis (Manski, 1990)
4. Focus on partial identification
5. Emphasize external validity concerns

But even this requires ~100 pages of caveats.