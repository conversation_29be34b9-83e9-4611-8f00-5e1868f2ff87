# Three-Tier Methodology Migration Guide

This guide helps you migrate from the old dual-track approach to the new three-tier methodology.

## Overview

The three-tier methodology replaces the old track1/track2 approach with a unified framework:

- **Old**: `track1_complex` (Bayesian TVP-VECM) + `track2_simple` (Threshold VECM)
- **New**: Integrated three-tier analysis with cross-validation

## Quick Start

### 1. Update Imports

Replace old imports:

```python
# OLD
from yemen_market.models.track2_simple import ThresholdVECM
from yemen_market.models.track1_complex import TVP_VECM

# NEW
from yemen_market.models.three_tier.integration import ThreeTierAnalysis
from yemen_market.models.three_tier.migration import ModelMigrationHelper
```

### 2. Run Complete Analysis

```python
# NEW: Run all three tiers
analysis = ThreeTierAnalysis(config)
results = analysis.run_full_analysis(data, conflict_data)
```

### 3. Migrate Existing Code

Use the migration helper:

```python
# Migrate configuration
migrator = ModelMigrationHelper()
new_config = migrator.migrate_configuration(old_config)

# Migrate results
new_results = migrator.migrate_results(old_results, 'threshold_vecm')
```

## Detailed Migration Steps

### Step 1: Data Preparation

The new methodology expects specific column names:

- `governorate` (not `market`)
- `usd_price` (not `price_usd`)
- `date`, `commodity` (unchanged)

```python
# Rename columns if needed
df = df.rename(columns={
    'market': 'governorate',
    'price_usd': 'usd_price'
})
```

### Step 2: Configuration Migration

Old configuration structure:

```python
old_config = {
    'track1_config': {
        'tvp_vecm': {'n_factors': 3, 'lags': 2}
    },
    'track2_config': {
        'threshold_vecm': {'max_lags': 4}
    }
}
```

New configuration structure:

```python
new_config = {
    'tier1_config': {
        'fixed_effects': ['entity', 'time'],
        'cluster_var': 'entity'
    },
    'tier2_config': {
        'min_observations': 50,
        'test_thresholds': True
    },
    'tier3_config': {
        'n_factors': 3,
        'conflict_validation': True
    }
}
```

### Step 3: Model Mapping

| Old Model | New Equivalent | Tier |
|-----------|----------------|------|
| `track2_simple.ThresholdVECM` | `tier2_commodity.threshold_vecm` | Tier 2 |
| `track1_complex.TVP_VECM` | `tier3_validation.DynamicFactorModel` | Tier 3 |
| `track1_complex.SpatialNetwork` | `tier3_validation.PCAMarketIntegration` | Tier 3 |
| `worldbank_threshold_vecm` | `tier2_commodity.threshold_vecm` | Tier 2 |

### Step 4: Results Access

Old results access:

```python
# OLD
threshold = model.threshold_value
coefficients = results.params
```

New results access:

```python
# NEW
# Tier 1 results
tier1_results = results['tier1']
r_squared = tier1_results.comparison_metrics.r_squared

# Tier 2 results (per commodity)
wheat_results = results['tier2']['wheat']
threshold = wheat_results.get('threshold_value')

# Tier 3 results
factor_results = results['tier3']['static_factors']
variance_explained = factor_results.tier_specific['variance_explained']
```

## Script Migration Examples

### Example 1: Simple Threshold Model

**Old:**

```python
from yemen_market.models.track2_simple import ThresholdVECM

model = ThresholdVECM(n_lags=2)
model.fit(price_matrix, threshold_variable=conflict)
threshold = model.threshold_value
```

**New:**

```python
from yemen_market.models.three_tier.tier2_commodity import CommodityExtractor

extractor = CommodityExtractor({'test_thresholds': True})
results = extractor.analyze_commodity(data, 'wheat')
threshold = results['results'].tier_specific.get('threshold_value')
```

### Example 2: Full Pipeline

**Old:**

```python
# Run track1 and track2 separately
tvp_model = TVP_VECM()
tvp_results = tvp_model.fit(data)

threshold_model = ThresholdVECM()
threshold_results = threshold_model.fit(data)
```

**New:**

```python
# Run all tiers together
analysis = ThreeTierAnalysis(config)
all_results = analysis.run_full_analysis(data)

# Access individual tier results
tier1 = all_results['tier1']
tier2 = all_results['tier2']
tier3 = all_results['tier3']
```

## Notebook Migration

For Jupyter notebooks:

1. Update imports at the top
2. Replace model instantiation
3. Update results access patterns
4. Use new visualization methods

Example notebook cell:

```python
# Cell 1: Setup
from yemen_market.models.three_tier.integration import ThreeTierAnalysis

# Cell 2: Run analysis
analysis = ThreeTierAnalysis()
results = analysis.run_full_analysis(panel_data)

# Cell 3: Visualize
commodity_comparison = analysis.get_commodity_comparison()
commodity_comparison.plot(kind='bar')
```

## Common Issues and Solutions

### Issue 1: Missing Columns

**Error**: "Missing required columns: {'governorate'}"
**Solution**: Rename columns as shown in Step 1

### Issue 2: Import Errors

**Error**: "No module named 'track1_complex'"
**Solution**: Update imports to use `three_tier` modules

### Issue 3: Attribute Errors

**Error**: "AttributeError: 'ResultsContainer' object has no attribute 'params'"
**Solution**: Use new results access pattern (see Step 4)

## Validation

After migration, validate your results:

```python
# Run comparison
from yemen_market.models.three_tier.migration import compare_methodologies

comparison = compare_methodologies(data, old_config)
# Review comparison report
```

## Next Steps

1. **Test thoroughly**: Run both old and new methods on same data
2. **Compare results**: Ensure consistency where expected
3. **Update documentation**: Reference three-tier methodology
4. **Archive old code**: Keep for reference but mark as deprecated

## Support

- See `docs/models/yemen_panel_methodology.md` for methodology details
- Check `tests/unit/models/three_tier/` for usage examples
- Review `reports/three_tier_analysis_new/` for sample outputs

## Migrating Diagnostic Code

### Legacy Diagnostic Modules

The following diagnostic modules have been deprecated and archived:

```python
# DEPRECATED - These modules have been moved to archived/v1_deprecated/diagnostics/
# Do not use these imports
from yemen_market.diagnostics import WorldBankDiagnosticSuite
from yemen_market.diagnostics import DiagnosticTestBattery
```

### New Diagnostic Framework

The new three-tier models include integrated diagnostics:

```python
# NEW - Diagnostics run automatically
model = PooledPanelModel(config)
model.fit(data)  # Diagnostics included!

# Access diagnostic results
diagnostics = model.results.get_diagnostics()
if diagnostics['wooldridge_test']['passed'] == False:
    warning("Serial correlation detected - using robust SEs")
```

### Migration Steps for Diagnostics

1. **Remove legacy imports**:

   ```python
   # Remove these
   from yemen_market.diagnostics.worldbank_diagnostics import run_world_bank_diagnostics
   from yemen_market.diagnostics.test_battery import DiagnosticTestBattery
   ```

2. **Enable automatic diagnostics**:

   ```python
   config = {
       'run_diagnostics': True,  # Enable diagnostics
       'diagnostic_config': {
           'critical_tests': ['wooldridge', 'pesaran_cd'],
           'fail_on_critical': False  # Continue even if tests fail
       }
   }
   ```

3. **Access diagnostic results**:

   ```python
   # After model fitting
   results = model.results
   
   # Get all diagnostics
   all_diagnostics = results.get_diagnostics()
   
   # Check specific test
   if 'wooldridge_test' in all_diagnostics:
       test_result = all_diagnostics['wooldridge_test']
       print(f"Serial correlation test p-value: {test_result['p_value']}")
   ```

### Diagnostic Migration Status

✅ **Migration Complete** (May 29, 2025)

- Legacy modules have been archived to `archived/v1_deprecated/diagnostics/`
- All functionality has been integrated into `three_tier.diagnostics`
- Backward compatibility is maintained through deprecation warnings
- All scripts and notebooks have been updated to use the new framework

Remember: The new methodology provides better handling of 3D panel data and includes cross-validation between tiers for more robust results.
