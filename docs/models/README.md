# Econometric Models Overview

## Introduction

The Yemen Market Integration project employs a **three-tier modeling approach** to analyze price transmission and market integration under conflict. This strategy specifically addresses the challenge of multi-dimensional panel data (market × commodity × time) while maintaining World Bank publication standards.

## Three-Tier Approach

### Why Three Tiers?

1. **3D Panel Structure**: Standard packages expect 2D panels, but our data has market × commodity × time dimensions
2. **Robustness**: Multiple methodologies provide cross-validation
3. **Policy Relevance**: Clear, interpretable results for decision-makers
4. **Academic Rigor**: Meets publication standards while being practical

### Tier Comparison

| Aspect | Tier 1 (Primary) | Tier 2 (Secondary) | Tier 3 (Validation) |
|--------|------------------|--------------------|--------------------|
| **Methodology** | Pooled Panel Regression | Commodity-Specific VECM | Factor Analysis |
| **Data Structure** | Full 3D panel | 2D panels per commodity | Wide matrix |
| **Key Feature** | Multi-way fixed effects | Threshold dynamics | Latent patterns |
| **Estimation** | linearmodels.PanelOLS | statsmodels VECM | sklearn PCA |
| **Computation** | 1-5 minutes | 5-15 minutes | <1 minute |
| **Interpretation** | Policy-friendly averages | Commodity-specific insights | Pattern validation |

## Model Selection Guide

### Tier 1: Pooled Panel Regression (PRIMARY ANALYSIS)

- **Research Questions**:
  - What is the average effect of conflict on market integration?
  - How do prices respond to conflict across all markets and commodities?
  - What is the magnitude of market fragmentation?
  
- **Data Requirements**:
  - Full panel dataset with market × commodity × time
  - Conflict intensity measures
  - Control variables (exchange rates, seasonality)
  
- **Implementation**:

  ```python
  from linearmodels import PanelOLS
  
  # Create entity as market-commodity pairs
  df['entity'] = df['market'] + '_' + df['commodity']
  panel = df.set_index(['entity', 'date'])
  
  # Run pooled regression with multi-way FE
  model = PanelOLS(
      panel['log_price'],
      panel[['conflict_intensity', 'controls']],
      entity_effects=True,
      time_effects=True
  )
  results = model.fit(cov_type='clustered', cluster_entity=True)
  ```

### Tier 2: Commodity-Specific Analysis (SUPPORTING EVIDENCE)

- **Research Questions**:
  - How does conflict affect specific commodities differently?
  - What are commodity-specific integration thresholds?
  - Do import vs local goods respond differently?
  
- **Commodities to Analyze**:
  - Wheat (imported staple)
  - Rice (imported staple)
  - Sugar (imported)
  - Fuel (critical import)
  
- **Implementation**:

  ```python
  # Extract commodity-specific 2D panel
  wheat_panel = df[df['commodity'] == 'Wheat'].copy()
  wheat_panel = wheat_panel.pivot(
      index='date',
      columns='market',
      values='price_usd'
  )
  
  # Run threshold VECM
  from yemen_market.models.three_tier.tier2_commodity import ThresholdVECM
  model = ThresholdVECM()
  model.fit(wheat_panel, conflict_data)
  ```

### Tier 3: Factor Analysis (VALIDATION)

- **Research Questions**:
  - What common patterns drive price movements?
  - Does a "conflict factor" emerge from the data?
  - How much variation is explained by common vs idiosyncratic factors?
  
- **Implementation**:

  ```python
  from sklearn.decomposition import PCA
  from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor
  
  # Create wide matrix
  price_matrix = df.pivot_table(
      index='date',
      columns=['market', 'commodity'],
      values='price_usd'
  )
  
  # Extract factors
  pca = PCA(n_components=5)
  factors = pca.fit_transform(price_matrix.fillna(method='ffill'))
  ```

## Implementation Workflow

### 1. Data Preparation

```python
# Load integrated panel data
import pandas as pd
panel_data = pd.read_parquet('data/processed/panels/integrated_panel.parquet')

# Create multi-index structure
panel_data['entity'] = panel_data['market'] + '_' + panel_data['commodity']
panel_data['log_price'] = np.log(panel_data['price_usd'])

# Check panel balance
print(f"Markets: {panel_data['market'].nunique()}")
print(f"Commodities: {panel_data['commodity'].nunique()}")
print(f"Time periods: {panel_data['date'].nunique()}")
print(f"Total observations: {len(panel_data):,}")
```

### 2. Tier 1: Pooled Panel Estimation

```python
from linearmodels import PanelOLS
from yemen_market.utils.logging import info, timer

with timer("Tier 1: Pooled panel regression"):
    # Set up panel structure
    panel = panel_data.set_index(['entity', 'date'])
    
    # Define regression
    y = panel['log_price']
    X = panel[['conflict_intensity', 'log_exchange_rate', 'rainfall_anomaly']]
    
    # Add commodity dummies if using separate FE
    commodity_dummies = pd.get_dummies(panel_data['commodity'], drop_first=True)
    X = pd.concat([X, commodity_dummies], axis=1)
    
    # Estimate with multi-way FE
    model = PanelOLS(y, X, entity_effects=True, time_effects=True)
    results = model.fit(cov_type='clustered', cluster_entity=True)
    
    info(f"Conflict coefficient: {results.params['conflict_intensity']:.4f}")
    info(f"Standard error: {results.std_errors['conflict_intensity']:.4f}")
```

### 3. Tier 2: Commodity-Specific Analysis

```python
# Priority commodities
commodities = ['Wheat', 'Rice', 'Sugar', 'Fuel']
commodity_results = {}

for commodity in commodities:
    with timer(f"Tier 2: {commodity} analysis"):
        # Extract 2D panel
        comm_data = panel_data[panel_data['commodity'] == commodity]
        
        # Create price matrix
        price_matrix = comm_data.pivot(
            index='date',
            columns='market',
            values='price_usd'
        )
        
        # Get conflict data
        conflict_matrix = comm_data.pivot(
            index='date',
            columns='market', 
            values='conflict_intensity'
        )
        
        # Run threshold VECM
        from yemen_market.models.track2_simple import SimpleThresholdVECM
        model = SimpleThresholdVECM(
            threshold_variable=conflict_matrix,
            n_lags=2
        )
        model.fit(price_matrix)
        
        commodity_results[commodity] = {
            'threshold': model.threshold_value,
            'low_regime_alpha': model.low_regime_results.alpha,
            'high_regime_alpha': model.high_regime_results.alpha
        }
```

### 4. Tier 3: Factor Validation

```python
with timer("Tier 3: Factor analysis"):
    # Create wide price matrix
    price_wide = panel_data.pivot_table(
        index='date',
        columns=['market', 'commodity'],
        values='log_price'
    )
    
    # Handle missing data
    price_filled = price_wide.fillna(method='ffill').fillna(method='bfill')
    
    # Dynamic factor model
    dfm = DynamicFactor(
        price_filled,
        k_factors=2,
        factor_order=1
    )
    dfm_results = dfm.fit()
    
    # Extract factors
    common_factor = dfm_results.factors.filtered[0]
    conflict_factor = dfm_results.factors.filtered[1]
    
    # Correlate with conflict
    conflict_avg = panel_data.groupby('date')['conflict_intensity'].mean()
    correlation = conflict_factor.corr(conflict_avg)
    info(f"Conflict factor correlation: {correlation:.3f}")
```

### 5. Robustness Checks

```python
# Driscoll-Kraay standard errors for spatial correlation
from linearmodels import PanelOLS

# Re-estimate with DK errors
model_dk = PanelOLS(y, X, entity_effects=True, time_effects=True)
results_dk = model_dk.fit(cov_type='kernel')  # Driscoll-Kraay

# Compare standard errors
print(f"Clustered SE: {results.std_errors['conflict_intensity']:.4f}")
print(f"Driscoll-Kraay SE: {results_dk.std_errors['conflict_intensity']:.4f}")
```

## Key Findings Interpretation

### Tier 1: Pooled Results

- **Positive conflict coefficient**: Markets become more fragmented
- **Magnitude interpretation**: 10% increase in conflict → X% price increase
- **Fixed effects**: Control for time-invariant market characteristics

### Tier 2: Commodity Patterns

- **Import vs Local**: Imported goods show stronger conflict sensitivity
- **Threshold levels**: Critical conflict intensity varies by commodity
- **Speed of adjustment**: How quickly markets re-integrate post-conflict

### Tier 3: Factor Insights

- **Common factor**: National price trends (inflation, seasonality)
- **Second factor**: Often correlates with conflict geography
- **Variance explained**: Indicates degree of market integration

## Policy Applications

### Market Integration Assessment

```python
# Calculate integration metric from Tier 1
integration_score = 1 / (1 + results.params['conflict_intensity'])
print(f"Market integration score: {integration_score:.2f}")

# Simulate peace scenario
peace_scenario = panel_data.copy()
peace_scenario['conflict_intensity'] = 0
peace_prediction = results.predict(peace_scenario)
```

### Commodity-Specific Interventions

```python
# Identify most affected commodities from Tier 2
impact_ranking = pd.DataFrame(commodity_results).T
impact_ranking['impact'] = (
    impact_ranking['high_regime_alpha'] - 
    impact_ranking['low_regime_alpha']
)
priority_commodities = impact_ranking.nlargest(3, 'impact')
```

## Common Issues and Solutions

### Panel Structure Issues

**Problem**: "Index contains duplicate entries, cannot reshape"

**Solution**: Our three-tier approach handles this:

- Tier 1: Uses entity as market-commodity pairs
- Tier 2: Extracts clean 2D panels per commodity
- Tier 3: Creates wide matrix with careful indexing

### Convergence Problems

**Problem**: Model fails to converge

**Solutions**:

- Check for multicollinearity in regressors
- Standardize variables before estimation
- Increase iterations or adjust optimization settings

### Missing Data

**Problem**: Unbalanced panel with many gaps

**Solutions**:

- Use methods robust to missing data
- Consider multiple imputation for critical gaps
- Report coverage statistics by market-commodity

## References

### Methodology Papers

1. **Panel Methods**: Wooldridge (2010), Baltagi (2021)
2. **Multi-dimensional Panels**: Matyas & Sevestre (2008)
3. **Conflict Economics**: Blattman & Miguel (2010)

### Yemen-Specific Studies

1. World Bank (2022). *Yemen Economic Monitor*
2. Tandon & Vishwanath (2020). "The Evolution of Poor Food Access"

## See Also

- [Yemen Panel Methodology](./yemen_panel_methodology.md) - Complete technical specification
- [API Reference](../api/models/base.md)
- [Diagnostic Framework](../api/diagnostics/test_battery.md)
- [Implementation Guide](../guides/modeling_guide.md)
