#!/usr/bin/env python3
"""End-to-end pipeline test for Yemen market integration project.

This script tests the full data processing pipeline to ensure all components
work together correctly after the consolidation changes.
"""

import sys
from pathlib import Path
import time
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from yemen_market.utils.logging import (
    info, warning, error, bind, timer, progress, log_data_shape
)
from yemen_market.config.settings import RAW_DATA_DIR, PROCESSED_DATA_DIR

# Set up logging
bind(module=__name__)


def check_raw_data():
    """Check if all required raw data exists."""
    info("=" * 60)
    info("Step 1: Checking Raw Data")
    info("=" * 60)
    
    required_files = {
        "WFP Data": RAW_DATA_DIR / "hdx/wfp-food-prices-for-yemen/wfp_food_prices_202505.csv",
        "ACAPS Control Zones": list(RAW_DATA_DIR.glob("acaps/*.zip")),
        "ACLED Conflict Data": RAW_DATA_DIR / "acled/acled_yemen_events_2019-01-01_to_2024-12-31.csv",
        "Yemen Pcodes": RAW_DATA_DIR / "hdx/cod-ab-yem/yem_admin_pcodes-02122024.xlsx",
    }
    
    all_good = True
    for name, path in required_files.items():
        if isinstance(path, list):
            if path:
                info(f"✅ {name}: Found {len(path)} files")
            else:
                error(f"❌ {name}: No files found")
                all_good = False
        else:
            if path.exists():
                info(f"✅ {name}: {path.name}")
            else:
                error(f"❌ {name}: Missing")
                all_good = False
    
    return all_good


def test_wfp_processing():
    """Test WFP data processing."""
    info("\n" + "=" * 60)
    info("Step 2: Testing WFP Processing")
    info("=" * 60)
    
    try:
        from yemen_market.data.wfp_processor import WFPProcessor
        
        with timer("wfp_processing"):
            processor = WFPProcessor(min_market_coverage=0.5)
            
            # Test enhanced processing
            commodity_df, exchange_df = processor.process_price_data()
            
            info(f"✅ Processed {len(commodity_df)} commodity records")
            info(f"✅ Processed {len(exchange_df)} exchange rate records")
            
            # Test smart panel creation
            smart_panel = processor.create_smart_panels(commodity_df)
            coverage = smart_panel['price_usd'].notna().sum() / len(smart_panel) * 100
            
            info(f"✅ Created smart panel: {len(smart_panel)} observations")
            info(f"✅ Price coverage: {coverage:.1f}%")
            
            # Check if we achieved the expected 88%+ coverage
            assert coverage > 85, f"Coverage {coverage:.1f}% is below expected 85%"
            
        return True
        
    except Exception as e:
        error(f"❌ WFP processing failed: {e}")
        return False


def test_acaps_processing():
    """Test ACAPS control zone processing."""
    info("\n" + "=" * 60)
    info("Step 3: Testing ACAPS Processing")
    info("=" * 60)
    
    try:
        from yemen_market.data.acaps_processor import ACAPSProcessor
        
        with timer("acaps_processing"):
            processor = ACAPSProcessor()
            
            # Get available files
            acaps_files = list(RAW_DATA_DIR.glob("acaps/*.zip"))
            if not acaps_files:
                warning("No ACAPS files found to process")
                return True
            
            # Process latest file
            latest_file = sorted(acaps_files)[-1]
            info(f"Processing: {latest_file.name}")
            
            control_zones_gdf = processor.process_control_zones(str(latest_file))
            
            if control_zones_gdf is not None:
                info(f"✅ Processed {len(control_zones_gdf)} control zones")
                zones = control_zones_gdf['controlling_group'].value_counts()
                for zone, count in zones.items():
                    info(f"   {zone}: {count} areas")
            else:
                warning("⚠️  No control zones extracted")
                
        return True
        
    except Exception as e:
        error(f"❌ ACAPS processing failed: {e}")
        return False


def test_acled_processing():
    """Test ACLED conflict data processing."""
    info("\n" + "=" * 60)
    info("Step 4: Testing ACLED Processing")
    info("=" * 60)
    
    try:
        from yemen_market.data.acled_processor import ACLEDProcessor
        
        with timer("acled_processing"):
            processor = ACLEDProcessor()
            
            # Load and process data
            acled_file = RAW_DATA_DIR / "acled/acled_yemen_events_2019-01-01_to_2024-12-31.csv"
            if not acled_file.exists():
                warning("ACLED file not found")
                return True
                
            conflict_metrics = processor.process_acled_data()
            
            info(f"✅ Processed conflict metrics: {len(conflict_metrics)} market-months")
            info(f"✅ Metrics include: {list(conflict_metrics.columns)}")
            
        return True
        
    except Exception as e:
        error(f"❌ ACLED processing failed: {e}")
        return False


def test_spatial_joins():
    """Test spatial join operations."""
    info("\n" + "=" * 60)
    info("Step 5: Testing Spatial Joins")
    info("=" * 60)
    
    try:
        from yemen_market.data.spatial_joins import SpatialJoiner
        
        with timer("spatial_joins"):
            joiner = SpatialJoiner()
            
            # Test market mapping
            market_zones = joiner.map_markets_to_zones()
            
            if market_zones is not None:
                info(f"✅ Mapped {len(market_zones)} markets to control zones")
                zone_counts = market_zones['control_zone'].value_counts()
                for zone, count in zone_counts.items():
                    info(f"   {zone}: {count} markets")
            else:
                warning("⚠️  No markets mapped")
                
        return True
        
    except Exception as e:
        error(f"❌ Spatial joins failed: {e}")
        return False


def test_panel_building():
    """Test panel dataset building."""
    info("\n" + "=" * 60)
    info("Step 6: Testing Panel Building")
    info("=" * 60)
    
    try:
        from yemen_market.data.panel_builder import PanelBuilder
        
        with timer("panel_building"):
            builder = PanelBuilder()
            
            # Build integrated panel
            panel_df = builder.build_integrated_panel()
            
            if panel_df is not None:
                info(f"✅ Built integrated panel: {len(panel_df)} observations")
                info(f"✅ Markets: {panel_df['market_id'].nunique()}")
                info(f"✅ Commodities: {panel_df['commodity'].nunique()}")
                
                # Check conflict integration
                conflict_cols = [col for col in panel_df.columns if 'conflict' in col.lower()]
                if conflict_cols:
                    coverage = panel_df[conflict_cols[0]].notna().sum() / len(panel_df) * 100
                    info(f"✅ Conflict coverage: {coverage:.1f}%")
            else:
                error("❌ Panel building returned None")
                return False
                
        return True
        
    except Exception as e:
        error(f"❌ Panel building failed: {e}")
        return False


def test_feature_engineering():
    """Test feature engineering module."""
    info("\n" + "=" * 60)
    info("Step 7: Testing Feature Engineering")
    info("=" * 60)
    
    try:
        from yemen_market.features.feature_engineering import FeatureEngineer
        import pandas as pd
        
        # Create sample data
        sample_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=24, freq='M'),
            'market_id': ['Market_A'] * 24,
            'commodity': ['Wheat'] * 24,
            'price_usd': [1.0 + i * 0.1 for i in range(24)],
            'conflict_events': [i % 10 for i in range(24)],
            'lat': [14.0] * 24,
            'lon': [44.0] * 24
        })
        
        with timer("feature_engineering"):
            engineer = FeatureEngineer()
            
            # Test each feature type
            features = engineer.create_all_features(sample_data)
            
            info(f"✅ Created {len(features.columns) - len(sample_data.columns)} new features")
            
            # Check specific feature categories
            temporal_features = [col for col in features.columns if 'lag' in col or 'ma' in col]
            info(f"✅ Temporal features: {len(temporal_features)}")
            
            interaction_features = [col for col in features.columns if 'interaction' in col]
            info(f"✅ Interaction features: {len(interaction_features)}")
            
            threshold_features = [col for col in features.columns if 'threshold' in col]
            info(f"✅ Threshold features: {len(threshold_features)}")
            
        return True
        
    except Exception as e:
        error(f"❌ Feature engineering failed: {e}")
        return False


def check_output_files():
    """Check if all expected output files exist."""
    info("\n" + "=" * 60)
    info("Step 8: Checking Output Files")
    info("=" * 60)
    
    expected_files = {
        "WFP commodity prices": PROCESSED_DATA_DIR / "wfp_commodity_prices.parquet",
        "WFP smart panel": PROCESSED_DATA_DIR / "wfp_smart_panel.parquet",
        "WFP exchange rates": PROCESSED_DATA_DIR / "wfp_exchange_rates.parquet",
        "Control zones": PROCESSED_DATA_DIR / "control_zones/control_zones_monthly.parquet",
        "Spatial joins": PROCESSED_DATA_DIR / "spatial/market_zones_temporal.parquet",
        "Integrated panel": PROCESSED_DATA_DIR / "panels/integrated_panel.parquet",
        "Conflict metrics": PROCESSED_DATA_DIR / "conflict/conflict_metrics.parquet",
    }
    
    all_good = True
    for name, path in expected_files.items():
        if path.exists():
            size_mb = path.stat().st_size / (1024 * 1024)
            info(f"✅ {name}: {size_mb:.2f} MB")
        else:
            warning(f"⚠️  {name}: Not found")
            all_good = False
    
    return all_good


def main():
    """Run full pipeline test."""
    start_time = time.time()
    
    info("=" * 60)
    info("Yemen Market Integration - Full Pipeline Test")
    info(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    info("=" * 60)
    
    # Track results
    results = {
        "Raw Data Check": check_raw_data(),
        "WFP Processing": test_wfp_processing(),
        "ACAPS Processing": test_acaps_processing(),
        "ACLED Processing": test_acled_processing(),
        "Spatial Joins": test_spatial_joins(),
        "Panel Building": test_panel_building(),
        "Feature Engineering": test_feature_engineering(),
        "Output Files": check_output_files(),
    }
    
    # Summary
    info("\n" + "=" * 60)
    info("Pipeline Test Summary")
    info("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        info(f"{test_name}: {status}")
    
    elapsed = time.time() - start_time
    info(f"\nTotal time: {elapsed:.1f} seconds")
    info(f"Overall result: {passed}/{total} tests passed")
    
    if passed == total:
        info("\n🎉 All pipeline tests passed successfully!")
        return 0
    else:
        error(f"\n⚠️  {total - passed} tests failed")
        return 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        info("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        error(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)