# Integration Tests

This directory contains integration tests that verify multiple components work together correctly.

## Test Files

- `test_diagnostic_integration.py` - Tests the diagnostic framework integration with three-tier models
- `test_diagnostic_basic.py` - Basic tests for diagnostic framework components
- `test_full_pipeline.py` - End-to-end test of the complete data processing pipeline
- `test_hdx_client.py` - Integration tests for HDX data client
- `test_models_quick.py` - Quick integration test for econometric models
- `test_tier1_integration.py` - Integration tests for Tier 1 pooled panel models
- `test_tier2_integration.py` - Integration tests for Tier 2 commodity-specific models

## Running Integration Tests

From the project root:

```bash
# Run all integration tests
pytest tests/integration/

# Run specific test
pytest tests/integration/test_diagnostic_integration.py

# Run with verbose output
pytest -v tests/integration/
```

## Note

These tests were moved from the `scripts/` directory to follow proper testing conventions.
All import paths have been updated to reflect the new location.