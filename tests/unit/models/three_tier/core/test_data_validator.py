"""Comprehensive tests for DataValidator."""

import numpy as np
import pandas as pd
import pytest

from yemen_market.models.three_tier.core.data_validator import (
    DataValidator, ValidationConfig, ValidationResult
)


class TestDataValidator:
    """Test data validation for three-tier analysis."""

    @pytest.fixture
    def sample_data(self):
        """Create sample market data for validation."""
        np.random.seed(42)

        dates = pd.date_range('2020-01-01', periods=100, freq='W')
        markets = ['Sana\'a', 'Aden', 'Taiz']
        commodities = ['wheat', 'rice']

        data = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': np.random.uniform(80, 120),
                        'volume': np.random.uniform(800, 1200),
                        'conflict_events': np.random.poisson(2),
                        'govt_controlled': np.random.choice([0, 1])
                    })

        return pd.DataFrame(data)

    @pytest.fixture
    def validator(self):
        """Create validator with test-friendly config."""
        # Use a config that allows 3 markets and shorter periods for testing
        config = ValidationConfig(min_entities=3, min_periods=5)
        return DataValidator(config)

    def test_validate_columns(self, validator, sample_data):
        """Test column validation."""
        result = validator.validate_columns(sample_data)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0
        assert 'All required columns present' in result.warnings

        # Test missing column
        bad_data = sample_data.drop('price', axis=1)
        result = validator.validate_columns(bad_data)

        assert not result.is_valid
        assert 'Missing required column: price' in result.errors

    def test_validate_data_types(self, validator, sample_data):
        """Test data type validation."""
        result = validator.validate_data_types(sample_data)

        assert result.is_valid
        assert len(result.errors) == 0

        # Test wrong data type
        bad_data = sample_data.copy()
        bad_data['price'] = bad_data['price'].astype(str)

        result = validator.validate_data_types(bad_data)
        assert not result.is_valid
        assert any('price' in error for error in result.errors)

    def test_validate_date_format(self, validator, sample_data):
        """Test date format validation."""
        result = validator.validate_date_format(sample_data)

        assert result.is_valid
        assert sample_data['date'].dtype == 'datetime64[ns]'

        # Test invalid dates
        bad_data = sample_data.copy()
        # Use proper pandas assignment to avoid FutureWarning
        bad_data = bad_data.astype({'date': 'object'})
        bad_data.iloc[0, bad_data.columns.get_loc('date')] = 'invalid_date'

        result = validator.validate_date_format(bad_data)
        assert not result.is_valid

    def test_validate_price_ranges(self, validator, sample_data):
        """Test price range validation."""
        result = validator.validate_price_ranges(sample_data)

        assert result.is_valid

        # Test outlier prices
        bad_data = sample_data.copy()
        bad_data.loc[0, 'price'] = -10  # Negative price
        bad_data.loc[1, 'price'] = 10000  # Extreme outlier

        result = validator.validate_price_ranges(bad_data)
        assert not result.is_valid
        assert any('Negative price' in error for error in result.errors)
        assert any('outlier' in warning for warning in result.warnings)

    def test_validate_panel_structure(self, validator, sample_data):
        """Test panel structure validation."""
        result = validator.validate_panel_structure(sample_data)

        assert result.is_valid
        assert 'n_markets' in result.info
        assert 'n_commodities' in result.info
        assert 'n_periods' in result.info
        assert 'is_balanced' in result.info

        # Test duplicate observations
        bad_data = pd.concat([sample_data, sample_data.iloc[[0]]])
        result = validator.validate_panel_structure(bad_data)

        assert not result.is_valid
        assert any('Duplicate' in error for error in result.errors)

    def test_validate_missing_data(self, validator, sample_data):
        """Test missing data validation."""
        # Add some missing values
        data_with_missing = sample_data.copy()
        data_with_missing.loc[0:5, 'price'] = np.nan

        result = validator.validate_missing_data(data_with_missing)

        assert result.is_valid  # Should pass with warnings
        assert len(result.warnings) > 0
        assert any('Missing values' in warning for warning in result.warnings)

        # Test excessive missing data - make sure we exceed the 30% threshold
        # Since validation is based on total cells, we need to set missing values across multiple columns
        total_cells = data_with_missing.shape[0] * data_with_missing.shape[1]
        missing_cells_needed = int(total_cells * 0.35)  # 35% missing to exceed 30% threshold

        # Set missing values across multiple columns to reach the threshold
        rows_to_affect = min(len(data_with_missing), missing_cells_needed // 3)  # Spread across 3 columns
        data_with_missing.loc[0:rows_to_affect, ['price', 'volume', 'conflict_events']] = np.nan

        result = validator.validate_missing_data(data_with_missing)

        assert not result.is_valid
        assert any('Excessive missing' in error for error in result.errors)

    def test_validate_market_consistency(self, validator, sample_data):
        """Test market name consistency."""
        result = validator.validate_market_consistency(sample_data)

        assert result.is_valid

        # Test inconsistent naming
        bad_data = sample_data.copy()
        bad_data.loc[0, 'market'] = 'Sana\'a '  # Extra space
        bad_data.loc[1, 'market'] = 'ADEN'  # Different case

        result = validator.validate_market_consistency(bad_data)
        assert len(result.warnings) > 0
        assert 'Potential duplicate markets' in str(result.warnings)

    def test_validate_time_series_continuity(self, validator, sample_data):
        """Test time series continuity validation."""
        result = validator.validate_time_continuity(sample_data)

        assert result.is_valid

        # Test with significant gaps - remove multiple consecutive dates to create a large gap
        dates_to_remove = pd.to_datetime(['2020-02-01', '2020-02-08', '2020-02-15', '2020-02-22', '2020-03-01'])
        bad_data = sample_data[~sample_data['date'].isin(dates_to_remove)].copy()
        result = validator.validate_time_continuity(bad_data)

        assert len(result.warnings) > 0
        assert any('gap' in warning.lower() or 'irregular' in warning.lower() for warning in result.warnings)

    def test_validate_relationships(self, validator, sample_data):
        """Test logical relationship validation."""
        # Add related variables
        sample_data['total_value'] = sample_data['price'] * sample_data['volume']

        result = validator.validate_relationships(sample_data)
        assert result.is_valid

        # Test invalid relationship
        bad_data = sample_data.copy()
        bad_data.loc[0, 'total_value'] = 0  # Should be price * volume

        result = validator.validate_relationships(bad_data)
        assert len(result.warnings) > 0

    def test_validate_all(self, validator, sample_data):
        """Test comprehensive validation."""
        result = validator.validate_all(sample_data)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert 'validation_timestamp' in result.info
        assert 'n_checks_passed' in result.info
        assert 'n_checks_failed' in result.info

    def test_validation_report(self, validator, sample_data):
        """Test validation report generation."""
        result = validator.validate_all(sample_data)
        report = result.generate_report()

        assert isinstance(report, str)
        assert 'Validation Report' in report
        assert 'Valid: Yes' in report or 'Valid: No' in report
        assert 'Errors:' in report
        assert 'Warnings:' in report
        assert 'Information:' in report

    def test_fix_common_issues(self, validator):
        """Test automatic fixing of common issues."""
        # Create data with fixable issues
        data = pd.DataFrame({
            'date': ['2020-01-01', '2020-01-08', '2020-01-15'],
            'market': ['Sana\'a ', 'ADEN', 'Taiz'],  # Inconsistent
            'commodity': ['wheat', 'wheat', 'wheat'],
            'price': [100, -5, 102],  # Negative price
            'volume': [1000, 1100, 1200]
        })

        fixed_data = validator.fix_common_issues(data)

        # Check fixes applied
        assert all(fixed_data['market'] == ['Sana\'a', 'Aden', 'Taiz'])
        # Check that non-negative prices are still >= 0, and negative price was replaced with NaN
        non_nan_prices = fixed_data['price'].dropna()
        assert all(non_nan_prices >= 0)  # All non-NaN prices should be >= 0
        assert pd.isna(fixed_data.loc[1, 'price'])  # Negative price should be NaN

    def test_custom_validation_rules(self):
        """Test custom validation rules."""
        # Create validator with custom rules
        config = ValidationConfig(
            min_price=50,
            max_price=200,
            required_columns=['date', 'market', 'commodity', 'price', 'custom_field']
        )
        validator = DataValidator(config)

        data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=5),
            'market': 'Sana\'a',
            'commodity': 'wheat',
            'price': [45, 100, 150, 190, 210],  # Some outside range
            'custom_field': 'value'
        })

        result = validator.validate_all(data)

        # Should have warnings for prices outside range
        assert len(result.warnings) > 0
        assert any('outside expected range' in warning for warning in result.warnings)

    def test_validation_result_methods(self):
        """Test ValidationResult methods."""
        result = ValidationResult()

        # Add errors and warnings
        result.add_error('Test error 1')
        result.add_error('Test error 2')
        result.add_warning('Test warning 1')
        result.add_info('n_observations', 1000)

        # Check state
        assert not result.is_valid
        assert len(result.errors) == 2
        assert len(result.warnings) == 1
        assert result.info['n_observations'] == 1000

        # Test summary
        summary = result.summary()
        assert summary['is_valid'] == False
        assert summary['n_errors'] == 2
        assert summary['n_warnings'] == 1

    def test_export_validation_results(self, validator, sample_data, tmp_path):
        """Test exporting validation results."""
        result = validator.validate_all(sample_data)

        # Export to file
        export_path = tmp_path / 'validation_results.json'
        result.export(export_path)

        assert export_path.exists()

        # Load and verify
        import json
        with open(export_path) as f:
            loaded = json.load(f)

        assert loaded['is_valid'] == result.is_valid
        assert loaded['errors'] == result.errors
        assert loaded['warnings'] == result.warnings

    def test_batch_validation(self, validator):
        """Test validation of multiple datasets."""
        # Create multiple datasets
        datasets = {}
        for commodity in ['wheat', 'rice', 'sugar']:
            data = pd.DataFrame({
                'date': pd.date_range('2020-01-01', periods=10),
                'market': 'Sana\'a',
                'commodity': commodity,
                'price': np.random.uniform(80, 120, 10),
                'volume': np.random.uniform(800, 1200, 10)
            })
            datasets[commodity] = data

        # Validate all
        results = validator.validate_batch(datasets)

        assert len(results) == 3
        assert all(commodity in results for commodity in ['wheat', 'rice', 'sugar'])
        assert all(isinstance(r, ValidationResult) for r in results.values())

    def test_validation_with_schema(self):
        """Test validation against a predefined schema."""
        schema = {
            'date': {'type': 'datetime', 'required': True},
            'market': {'type': 'string', 'required': True, 'values': ['Sana\'a', 'Aden', 'Taiz']},
            'commodity': {'type': 'string', 'required': True},
            'price': {'type': 'numeric', 'required': True, 'min': 0, 'max': 1000},
            'volume': {'type': 'numeric', 'required': False, 'min': 0}
        }

        config = ValidationConfig(schema=schema)
        validator = DataValidator(config)

        # Test valid data
        valid_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=3),
            'market': ['Sana\'a', 'Aden', 'Taiz'],
            'commodity': 'wheat',
            'price': [100, 110, 105],
            'volume': [1000, 1100, 1050]
        })

        result = validator.validate_against_schema(valid_data)
        assert result.is_valid

        # Test invalid data
        invalid_data = valid_data.copy()
        invalid_data.loc[0, 'market'] = 'Invalid City'

        result = validator.validate_against_schema(invalid_data)
        assert not result.is_valid
        assert any('not in allowed values' in error for error in result.errors)