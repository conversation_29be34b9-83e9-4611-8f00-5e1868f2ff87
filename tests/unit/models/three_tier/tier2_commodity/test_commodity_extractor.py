"""Unit tests for the CommodityExtractor class."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

from yemen_market.models.three_tier.tier2_commodity import (
    CommodityExtractor, CommodityExtractorConfig
)
from yemen_market.utils.logging import get_logger

# Set up test logger
logger = get_logger(__name__)

# Sample test data
def create_test_data():
    """Create a sample 3D panel dataset for testing."""
    np.random.seed(42)
    
    # Create date range
    dates = pd.date_range('2020-01-01', '2022-12-31', freq='ME')  # ME for month end
    markets = ['Market1', 'Market2', 'Market3', 'Market4']
    commodities = ['Wheat', 'Rice', 'Sugar']
    
    # Generate data
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                # Add some missing values (10% probability)
                if np.random.random() > 0.1:
                    data.append({
                        'market': market,
                        'commodity': commodity,
                        'date': date,
                        'price': np.random.lognormal(mean=3, sigma=0.5)
                    })
    
    return pd.DataFrame(data)


def test_commodity_extractor_init():
    """Test initialization of CommodityExtractor with default config."""
    extractor = CommodityExtractor()
    
    assert extractor.config.min_markets == 3
    assert extractor.config.min_periods == 50
    assert extractor.config.min_observations == 100
    assert extractor.config.max_missing_pct == 0.5
    assert extractor.config.required_columns == ['market', 'commodity', 'date', 'price']
    assert len(extractor.extracted_commodities) == 0
    assert len(extractor.coverage_stats) == 0


def test_commodity_extractor_init_custom_config():
    """Test initialization with custom config."""
    config = CommodityExtractorConfig(
        min_markets=5,
        min_periods=60,
        min_observations=150,
        max_missing_pct=0.3,
        required_columns=['market', 'product', 'date', 'value']
    )
    
    extractor = CommodityExtractor(config=config)
    
    assert extractor.config.min_markets == 5
    assert extractor.config.min_periods == 60
    assert extractor.config.min_observations == 150
    assert extractor.config.max_missing_pct == 0.3
    assert extractor.config.required_columns == ['market', 'product', 'date', 'value']


def test_validate_commodity_data_success():
    """Test successful validation of commodity data."""
    # Create valid test data - need at least 100 observations
    dates = pd.date_range('2020-01-01', '2021-12-31', freq='W')  # Weekly to get more observations
    data = []
    for i, date in enumerate(dates):
        for market in ['Market1', 'Market2', 'Market3', 'Market4']:  # 4 markets
            data.append({
                'market': market,
                'commodity': 'Wheat',
                'date': date,
                'price': 100 + i + np.random.normal(0, 5)
            })
    
    df = pd.DataFrame(data)
    extractor = CommodityExtractor()
    
    # Test validation
    is_valid, messages = extractor.validate_commodity_data('Wheat', df)
    
    assert is_valid is True
    assert len(messages) == 0


def test_validate_commodity_data_insufficient_markets():
    """Test validation with insufficient markets."""
    # Create data with only 2 markets
    dates = pd.date_range('2020-01-01', '2021-12-31', freq='W')
    data = []
    for i, date in enumerate(dates):
        for market in ['Market1', 'Market2']:  # Only 2 markets
            data.append({
                'market': market,
                'commodity': 'Wheat',
                'date': date,
                'price': 100 + i + np.random.normal(0, 5)
            })
    
    df = pd.DataFrame(data)
    extractor = CommodityExtractor(config=CommodityExtractorConfig(min_markets=3))
    
    # Test validation
    is_valid, messages = extractor.validate_commodity_data('Wheat', df)
    
    assert is_valid is False
    assert any("Insufficient markets: 2 < 3 minimum" in msg for msg in messages)


def test_extract_all_commodities():
    """Test extraction of all valid commodities."""
    # Create test data with 3 commodities
    df = create_test_data()
    
    # Initialize extractor with lenient settings for testing
    config = CommodityExtractorConfig(
        min_markets=2,
        min_periods=10,
        min_observations=20,
        max_missing_pct=0.2
    )
    extractor = CommodityExtractor(config=config)
    
    # Extract commodities
    result = extractor.extract_all_commodities(df)
    
    # Check results
    assert len(result) == 3  # Should extract all 3 commodities
    assert set(result.keys()) == {'Wheat', 'Rice', 'Sugar'}
    
    # Check that each commodity has the expected structure
    for commodity, df_comm in result.items():
        # Check required columns are present (might have additional columns like 'index')
        required_cols = {'market', 'commodity', 'date', 'price'}
        assert required_cols.issubset(set(df_comm.columns))
        assert len(df_comm) >= 20  # At least min_observations
        assert df_comm['commodity'].nunique() == 1  # Only one commodity per df
        assert df_comm['market'].nunique() >= 2  # At least min_markets


def test_prepare_for_vecm():
    """Test preparation of data for VECM analysis."""
    # Create test data
    dates = pd.date_range('2020-01-01', '2020-12-31', freq='ME')
    data = []
    for i, date in enumerate(dates):
        for market in ['Market1', 'Market2', 'Market3']:
            data.append({
                'market': market,
                'commodity': 'Wheat',
                'date': date,
                'price': 100 + i + np.random.normal(0, 5)
            })
    
    df = pd.DataFrame(data)
    extractor = CommodityExtractor()
    
    # Prepare for VECM
    wide_df, metadata = extractor.prepare_for_vecm('Wheat', df)
    
    # Check results
    assert isinstance(wide_df, pd.DataFrame)
    assert wide_df.shape == (len(dates), 3)  # 3 markets × n_dates
    assert set(wide_df.columns) == {'Market1', 'Market2', 'Market3'}
    assert wide_df.index.equals(pd.DatetimeIndex(dates))
    
    # Check metadata
    assert metadata['commodity'] == 'Wheat'
    assert metadata['n_markets'] == 3
    assert metadata['n_observations'] == len(dates)
    assert 'price_stats' in metadata
    assert 'mean' in metadata['price_stats']
    assert 'std' in metadata['price_stats']


def test_validate_input_data_missing_columns():
    """Test validation of input data with missing required columns."""
    # Create test data with missing 'price' column
    df = pd.DataFrame({
        'market': ['M1', 'M2'],
        'commodity': ['Wheat', 'Wheat'],
        'date': ['2020-01-01', '2020-01-02']
    })
    
    extractor = CommodityExtractor()
    
    # Should raise ValueError due to missing 'price' column
    with pytest.raises(ValueError) as excinfo:
        extractor._validate_input_data(df)
    
    assert "missing required columns: price" in str(excinfo.value)


def test_extract_all_commodities_with_invalid_data():
    """Test extraction with some invalid commodities."""
    # Create test data with one valid and one invalid commodity
    dates = pd.date_range('2020-01-01', '2020-12-31', freq='W')
    
    # Valid commodity data
    data = []
    for date in dates:
        for market in ['Market1', 'Market2', 'Market3']:
            data.append({
                'market': market,
                'commodity': 'Wheat',
                'date': date,
                'price': 100 + np.random.normal(0, 5)
            })
    
    # Invalid commodity (only 1 market)
    for date in dates:
        data.append({
            'market': 'Market1',  # Only 1 market
            'commodity': 'Rice',
            'date': date,
            'price': 150 + np.random.normal(0, 5)
        })
    
    df = pd.DataFrame(data)
    
    # Initialize extractor with strict settings
    config = CommodityExtractorConfig(
        min_markets=2,  # Rice will fail with only 1 market
        min_periods=10,
        min_observations=20,
        max_missing_pct=0.2
    )
    extractor = CommodityExtractor(config=config)
    
    # Extract commodities
    result = extractor.extract_all_commodities(df)
    
    # Check that only the valid commodity was extracted
    assert len(result) == 1
    assert 'Wheat' in result
    assert 'Rice' not in result
