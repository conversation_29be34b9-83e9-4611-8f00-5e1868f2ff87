"""Test suite for factor models in Tier 3 validation.

Tests static and dynamic factor models for market integration analysis.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from yemen_market.models.three_tier.tier3_validation.factor_models import (
    StaticFactorModel, DynamicFactorModel
)
from yemen_market.models.three_tier.core.base_model import TierType


@pytest.fixture
def sample_data():
    """Create sample 3D panel data."""
    np.random.seed(42)
    
    # Generate dates
    dates = pd.date_range(start='2020-01-01', periods=100, freq='W')
    
    # Markets and commodities
    markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah']
    commodities = ['wheat', 'rice', 'sugar']
    
    # Create data
    data = []
    for date in dates:
        for market in markets:
            for commodity in commodities:
                # Generate correlated prices
                base_price = 100 + np.random.randn() * 10
                market_effect = np.random.randn() * 5
                commodity_effect = np.random.randn() * 3
                
                data.append({
                    'date': date,
                    'governorate': market,
                    'commodity': commodity,
                    'usd_price': base_price + market_effect + commodity_effect
                })
    
    return pd.DataFrame(data)


class TestStaticFactorModel:
    """Test static factor model implementation."""
    
    @pytest.fixture
    def model(self):
        """Create model instance."""
        config = {
            'n_factors': 3,
            'standardize': True,
            'min_variance_explained': 0.8
        }
        return StaticFactorModel(config)
    
    def test_initialization(self, model):
        """Test model initialization."""
        assert model.tier == TierType.TIER3_VALIDATION
        assert model.n_factors == 3
        assert model.standardize is True
        assert model.min_variance_explained == 0.8
        assert not model.is_fitted
    
    def test_validate_data_valid(self, model, sample_data):
        """Test data validation with valid data."""
        assert model.validate_data(sample_data) is True
    
    def test_validate_data_missing_columns(self, model):
        """Test data validation with missing columns."""
        invalid_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'price': np.random.randn(10)
        })
        assert model.validate_data(invalid_data) is False
    
    def test_validate_data_too_many_missing(self, model, sample_data):
        """Test data validation with too many missing values."""
        # Set most values to NaN
        sample_data.loc[sample_data.index[:int(len(sample_data)*0.6)], 'usd_price'] = np.nan
        
        with patch('yemen_market.models.three_tier.core.panel_data_handler.PanelDataHandler.create_wide_matrix') as mock_wide:
            # Mock wide matrix with many NaNs
            wide_matrix = pd.DataFrame(np.nan, index=range(100), columns=range(10))
            mock_wide.return_value = wide_matrix
            
            assert model.validate_data(sample_data) is False
    
    def test_fit_success(self, model, sample_data):
        """Test successful model fitting."""
        model.fit(sample_data)
        
        assert model.is_fitted
        assert model.factor_scores is not None
        assert model.loadings is not None
        assert model.results is not None
        
        # Check factor scores shape
        n_periods = sample_data['date'].nunique()
        assert model.factor_scores.shape[0] == n_periods
        assert model.factor_scores.shape[1] == model.n_factors
        
        # Check loadings shape
        n_series = len(sample_data['governorate'].unique()) * len(sample_data['commodity'].unique())
        assert model.loadings.shape[0] <= n_series  # May be less due to missing data handling
        assert model.loadings.shape[1] == model.n_factors
    
    def test_fit_auto_select_factors(self, sample_data):
        """Test automatic factor selection."""
        model = StaticFactorModel({'n_factors': None, 'min_variance_explained': 0.7})
        model.fit(sample_data)
        
        assert model.is_fitted
        assert model.n_factors is not None
        assert model.n_factors >= 1
        assert model.n_factors <= model.max_factors
    
    def test_predict_training_data(self, model, sample_data):
        """Test prediction on training data."""
        model.fit(sample_data)
        
        factor_scores = model.predict()
        
        assert isinstance(factor_scores, pd.DataFrame)
        assert factor_scores.shape[1] == model.n_factors
        assert all(f'Factor_{i+1}' in factor_scores.columns for i in range(model.n_factors))
    
    def test_predict_new_data(self, model, sample_data):
        """Test prediction on new data."""
        # Fit on first 80 periods
        train_data = sample_data[sample_data['date'] < sample_data['date'].unique()[80]]
        model.fit(train_data)
        
        # Predict on last 20 periods
        test_data = sample_data[sample_data['date'] >= sample_data['date'].unique()[80]]
        factor_scores = model.predict(test_data)
        
        assert isinstance(factor_scores, pd.DataFrame)
        assert len(factor_scores) == test_data['date'].nunique()
    
    def test_get_factor_contributions(self, model, sample_data):
        """Test getting factor contributions for specific entity."""
        model.fit(sample_data)
        
        # Get contributions for a specific market-commodity pair
        entity = "Sana'a_wheat"
        if entity in model.loadings.index:
            contributions = model.get_factor_contributions(entity)
            
            assert isinstance(contributions, pd.Series)
            assert len(contributions) == model.n_factors
            assert contributions.name == entity
    
    def test_interpret_loadings(self, model, sample_data):
        """Test loading interpretation logic."""
        model.fit(sample_data)
        
        results = model.results
        assert 'factor_interpretations' in results.tier_specific
        
        # Check if any interpretations were generated
        interpretations = results.tier_specific['factor_interpretations']
        assert isinstance(interpretations, list)
    
    def test_results_structure(self, model, sample_data):
        """Test structure of results container."""
        model.fit(sample_data)
        
        results = model.results
        
        # Check metadata
        assert results.metadata['n_observations'] > 0
        assert results.metadata['n_series'] > 0
        assert results.metadata['n_factors'] == model.n_factors
        
        # Check tier-specific results
        assert 'variance_explained' in results.tier_specific
        assert 'cumulative_variance' in results.tier_specific
        assert 'factor_scores' in results.tier_specific
        assert 'loadings' in results.tier_specific
        
        # Check coefficients (variance explained)
        assert len(results.coefficients) == model.n_factors
        assert all(f'Factor_{i+1}_variance' in results.coefficients for i in range(model.n_factors))


class TestDynamicFactorModel:
    """Test dynamic factor model implementation."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample panel data with temporal dynamics."""
        np.random.seed(42)
        
        # Generate longer time series for dynamics
        dates = pd.date_range(start='2019-01-01', periods=200, freq='W')
        
        # Markets and commodities
        markets = ['Sana\'a', 'Aden', 'Taiz']
        commodities = ['wheat', 'rice']
        
        # Create data with AR structure
        data = []
        prev_prices = {}
        
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    key = f"{market}_{commodity}"
                    
                    # AR(1) structure with common factor
                    common_factor = np.sin(dates.get_loc(date) / 50) * 20
                    
                    if key in prev_prices:
                        price = 0.8 * prev_prices[key] + 0.2 * (100 + common_factor) + np.random.randn() * 5
                    else:
                        price = 100 + common_factor + np.random.randn() * 5
                    
                    prev_prices[key] = price
                    
                    data.append({
                        'date': date,
                        'governorate': market,
                        'commodity': commodity,
                        'usd_price': price
                    })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def model(self):
        """Create dynamic factor model instance."""
        config = {
            'n_factors': 2,
            'ar_lags': 1,
            'allow_structural_breaks': True
        }
        return DynamicFactorModel(config)
    
    def test_initialization(self, model):
        """Test model initialization."""
        assert model.tier == TierType.TIER3_VALIDATION
        assert model.n_factors == 2
        assert model.ar_lags == 1
        assert model.allow_structural_breaks is True
        assert not model.is_fitted
    
    def test_validate_data(self, model, sample_data):
        """Test data validation for dynamic model."""
        assert model.validate_data(sample_data) is True
    
    def test_validate_data_insufficient_periods(self, model):
        """Test validation with too few time periods."""
        short_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=30, freq='W'),
            'governorate': 'Sana\'a',
            'commodity': 'wheat',
            'usd_price': np.random.randn(30) + 100
        })
        
        # Should still validate but with warning
        assert model.validate_data(short_data) is True
    
    @patch('statsmodels.tsa.statespace.dynamic_factor.DynamicFactor')
    def test_fit_success(self, mock_dfm_class, model, sample_data):
        """Test successful dynamic factor model fitting."""
        # Mock the state space model
        mock_dfm = Mock()
        mock_results = Mock()
        mock_results.filtered_state = np.random.randn(5, 100)  # 5 states, 100 periods
        mock_results.smoothed_state = np.random.randn(5, 100)
        mock_results.llf = -1000.0
        mock_results.nobs_effective = 90
        mock_results.coefficients_of_determination = np.array([0.7, 0.6])
        mock_results.transition = np.eye(2)
        mock_results.mle_retvals = {'converged': True}
        
        mock_dfm.fit.return_value = mock_results
        mock_dfm_class.return_value = mock_dfm
        
        model.fit(sample_data)
        
        assert model.is_fitted
        assert model.static_model is not None
        assert model.filtered_states is not None
        assert model.smoothed_states is not None
        assert model.results is not None
    
    @patch('statsmodels.tsa.statespace.dynamic_factor.DynamicFactor')
    def test_structural_breaks(self, mock_dfm_class, model, sample_data):
        """Test structural break detection."""
        # Setup mock
        mock_dfm = Mock()
        mock_results = Mock()
        
        # Create states with a clear break
        n_periods = 100
        states = np.zeros((2, n_periods))
        states[0, :50] = np.random.randn(50) + 1
        states[0, 50:] = np.random.randn(50) - 1  # Mean shift
        states[1, :] = np.random.randn(n_periods)
        
        mock_results.filtered_state = states
        mock_results.smoothed_state = states
        mock_results.llf = -1000.0
        mock_results.nobs_effective = 90
        mock_results.coefficients_of_determination = np.array([0.7, 0.6])
        mock_results.transition = np.eye(2)
        mock_results.mle_retvals = {'converged': True}
        
        mock_dfm.fit.return_value = mock_results
        mock_dfm_class.return_value = mock_dfm
        
        model.fit(sample_data)
        
        # Model should detect structural breaks
        assert model.is_fitted
    
    def test_predict(self, model, sample_data):
        """Test prediction method."""
        # Use static model mock for simplicity
        model.static_model = Mock()
        model.static_model.fit.return_value = None
        model.static_model.loadings = pd.DataFrame(
            np.random.randn(10, 2),
            columns=['Factor_1', 'Factor_2']
        )
        
        model.smoothed_states = np.random.randn(2, 100)
        model.n_factors = 2
        model.is_fitted = True
        
        predictions = model.predict()
        
        assert isinstance(predictions, pd.DataFrame)
        assert predictions.shape[1] == 2
        assert 'DynamicFactor_1' in predictions.columns
        assert 'DynamicFactor_2' in predictions.columns
    
    def test_get_time_varying_loadings(self, model):
        """Test getting time-varying loadings."""
        # Setup minimal state for testing
        model.static_model = Mock()
        model.static_model.loadings = pd.DataFrame(
            np.random.randn(10, 2),
            columns=['Factor_1', 'Factor_2']
        )
        model.is_fitted = True
        
        loadings = model.get_time_varying_loadings(period=50)
        
        assert isinstance(loadings, pd.DataFrame)
        assert loadings.shape == model.static_model.loadings.shape


class TestFactorModelsIntegration:
    """Test integration between static and dynamic factor models."""
    
    def test_static_dynamic_consistency(self, sample_data):
        """Test that static and dynamic models give consistent results."""
        # Fit static model
        static_model = StaticFactorModel({'n_factors': 2})
        static_model.fit(sample_data)
        
        # Fit dynamic model
        dynamic_model = DynamicFactorModel({'n_factors': 2})
        
        # Mock the dynamic factor to avoid statsmodels dependency in tests
        with patch('statsmodels.tsa.statespace.dynamic_factor.DynamicFactor'):
            dynamic_model.static_model = static_model  # Use same static initialization
            dynamic_model.is_fitted = True
            dynamic_model.results = Mock()
            
            # Both should identify similar number of important factors
            assert static_model.n_factors == dynamic_model.n_factors
    
    def test_missing_data_handling(self):
        """Test that both models handle missing data appropriately."""
        # Create data with missing values
        dates = pd.date_range('2020-01-01', periods=50, freq='W')
        markets = ['Sana\'a', 'Aden']
        commodities = ['wheat', 'rice']
        
        # Create proper panel data
        data_list = []
        for date in dates:
            for market in markets:
                for commodity in commodities:
                    data_list.append({
                        'date': date,
                        'governorate': market,
                        'commodity': commodity,
                        'usd_price': np.random.randn() + 100
                    })
        
        data = pd.DataFrame(data_list)
        
        # Introduce missing values
        data.loc[data.index[:50], 'usd_price'] = np.nan
        
        # Static model should handle this
        static_model = StaticFactorModel({'n_factors': 2})
        static_model.fit(data)
        
        assert static_model.is_fitted
        assert static_model.results is not None