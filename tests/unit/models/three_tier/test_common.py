"""Tests for common utilities and classes for three-tier analysis."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime
import json

from yemen_market.models.three_tier.common import ModelMetadata, ResultsContainer
from yemen_market.utils.logging import bind

# Set module context for logging (if necessary for tests)
# bind(module=__name__) # Usually not needed directly in test files unless testing logging behavior

class TestModelMetadata:
    def test_initialization_defaults(self):
        """Test ModelMetadata initialization with default values."""
        metadata = ModelMetadata(model_type="TestModel", tier=1)
        assert metadata.model_type == "TestModel"
        assert metadata.tier == 1
        assert metadata.commodity is None
        assert isinstance(metadata.estimation_date, datetime)
        assert metadata.sample_period is None
        assert metadata.n_observations is None
        assert metadata.convergence is True
        assert metadata.notes == ""

    def test_initialization_with_values(self):
        """Test ModelMetadata initialization with provided values."""
        estimation_time = datetime.now()
        metadata = ModelMetadata(
            model_type="AnotherTestModel",
            tier=2,
            commodity="Wheat",
            estimation_date=estimation_time,
            sample_period="2020-2023",
            n_observations=100,
            convergence=False,
            notes="Test notes"
        )
        assert metadata.model_type == "AnotherTestModel"
        assert metadata.tier == 2
        assert metadata.commodity == "Wheat"
        assert metadata.estimation_date == estimation_time
        assert metadata.sample_period == "2020-2023"
        assert metadata.n_observations == 100
        assert metadata.convergence is False
        assert metadata.notes == "Test notes"

# Placeholder for ResultsContainer tests - to be expanded
class TestResultsContainer:
    @pytest.fixture
    def basic_metadata(self):
        return ModelMetadata(model_type="TestContainerModel", tier=0)

    def test_initialization_with_metadata(self, basic_metadata):
        """Test ResultsContainer initialization with metadata."""
        container = ResultsContainer(metadata=basic_metadata)
        assert container.metadata == basic_metadata
        assert container.results == {}
        assert container.diagnostics == {}
        assert container.predictions is None
        assert container.residuals is None
        assert container.fitted_values is None
        assert container.parameters == {}
        assert container.standard_errors == {}
        assert container.statistics == {}

    def test_initialization_without_metadata(self):
        """Test ResultsContainer initialization without metadata (uses default)."""
        container = ResultsContainer()
        assert isinstance(container.metadata, ModelMetadata)
        assert container.metadata.model_type == "unknown"
        assert container.metadata.tier == 0


    def test_add_and_get_result(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        container.add_result("my_key", "my_value")
        assert container.get_result("my_key") == "my_value"
        assert container.get_result("non_existent_key") is None
        assert container.get_result("non_existent_key", default="default_val") == "default_val"

    def test_add_and_get_diagnostic(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        container.add_diagnostic("diag_key", {"detail": "issue"})
        assert container.get_diagnostic("diag_key") == {"detail": "issue"}
        assert container.get_diagnostic("non_existent_diag") is None
        assert container.get_diagnostic("non_existent_diag", default=[]) == []

    def test_set_predictions(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        preds_df = pd.DataFrame({"pred": [1, 2, 3]})
        container.set_predictions(preds_df)
        pd.testing.assert_frame_equal(container.predictions, preds_df)

    def test_set_residuals(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        residuals_series = pd.Series([0.1, -0.2, 0.05])
        container.set_residuals(residuals_series)
        pd.testing.assert_series_equal(container.residuals, residuals_series)

    def test_set_fitted_values(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        fitted_series = pd.Series([0.9, 2.2, 2.95])
        container.set_fitted_values(fitted_series)
        pd.testing.assert_series_equal(container.fitted_values, fitted_series)

    def test_add_and_get_parameter(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        container.add_parameter("alpha", 0.5, std_error=0.01)
        assert container.get_parameter("alpha") == 0.5
        assert container.standard_errors["alpha"] == 0.01
        assert container.get_parameter("beta") is None
        assert container.get_parameter("beta", default=1.0) == 1.0
        container.add_parameter("gamma", 0.7) # Test without std_error
        assert container.get_parameter("gamma") == 0.7
        assert "gamma" not in container.standard_errors

    def test_add_and_get_statistic(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        container.add_statistic("r_squared", 0.95)
        assert container.get_statistic("r_squared") == 0.95
        assert container.get_statistic("adj_r_squared") is None
        assert container.get_statistic("adj_r_squared", default=0.9) == 0.9

    def test_to_dict(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        container.add_result("res_key", "res_val")
        container.add_parameter("param_key", 1.23, 0.01)
        container.set_predictions(pd.DataFrame({"a": [1]}))
        
        container_dict = container.to_dict()
        
        assert isinstance(container_dict, dict)
        assert "metadata" in container_dict
        assert container_dict["metadata"]["model_type"] == "TestContainerModel"
        assert "results" in container_dict
        assert container_dict["results"]["res_key"] == "res_val"
        assert "parameters" in container_dict
        assert container_dict["parameters"]["param_key"] == 1.23
        assert "standard_errors" in container_dict
        assert container_dict["standard_errors"]["param_key"] == 0.01
        assert "predictions" in container_dict
        assert container_dict["predictions"]["data"][0][0] == 1 # Check data
        assert container_dict["predictions"]["columns"][0] == 'a' # Check column name

    def test_to_json(self, basic_metadata):
        container = ResultsContainer(metadata=basic_metadata)
        container.add_result("json_res", {"nested": "data"})
        container.set_residuals(pd.Series([1.0, 2.0]))

        json_str = container.to_json()
        assert isinstance(json_str, str)
        data = json.loads(json_str)
        assert data["metadata"]["model_type"] == "TestContainerModel"
        assert data["results"]["json_res"] == {"nested": "data"}
        # Residuals are converted to list in JSON
        assert data["residuals"]["data"] == [1.0, 2.0]

    def test_from_dict(self, basic_metadata):
        original_container = ResultsContainer(metadata=basic_metadata)
        original_container.add_result("orig_res", "orig_val")
        original_container.add_parameter("orig_param", 3.14, 0.02)
        original_container.set_fitted_values(pd.Series([10.0, 11.0]))

        container_dict = original_container.to_dict()
        new_container = ResultsContainer.from_dict(container_dict)

        assert new_container.metadata.model_type == basic_metadata.model_type
        assert new_container.get_result("orig_res") == "orig_val"
        assert new_container.get_parameter("orig_param") == 3.14
        assert new_container.standard_errors["orig_param"] == 0.02
        pd.testing.assert_series_equal(new_container.fitted_values, original_container.fitted_values)

    def test_from_json(self, basic_metadata):
        original_container = ResultsContainer(metadata=basic_metadata)
        original_container.add_statistic("f_stat", 123.45)
        original_container.set_predictions(pd.DataFrame({"p1": [5,6], "p2": [7,8]}))

        json_str = original_container.to_json()
        new_container = ResultsContainer.from_json(json_str)

        assert new_container.metadata.tier == basic_metadata.tier
        assert new_container.get_statistic("f_stat") == 123.45
        pd.testing.assert_frame_equal(new_container.predictions, original_container.predictions)

    def test_serialization_deserialization_full_cycle(self, basic_metadata):
        """Test a more complete serialization-deserialization cycle."""
        container = ResultsContainer(metadata=basic_metadata)
        container.add_result("key1", "value1")
        container.add_diagnostic("diag1", {"code": 100, "message": "OK"})
        container.set_predictions(pd.DataFrame({"colA": [1, 2], "colB": [3, 4]}))
        container.set_residuals(pd.Series([0.1, -0.1]))
        container.set_fitted_values(pd.Series([0.9, 2.1]))
        container.add_parameter("beta0", 1.0, 0.1)
        container.add_parameter("beta1", 2.5, 0.25)
        container.add_statistic("aic", 50.0)
        container.add_statistic("bic", 55.0)

        # To dict and from dict
        dict_representation = container.to_dict()
        rehydrated_from_dict = ResultsContainer.from_dict(dict_representation)

        assert rehydrated_from_dict.metadata.model_type == container.metadata.model_type
        assert rehydrated_from_dict.get_result("key1") == "value1"
        assert rehydrated_from_dict.get_diagnostic("diag1") == {"code": 100, "message": "OK"}
        pd.testing.assert_frame_equal(rehydrated_from_dict.predictions, container.predictions)
        pd.testing.assert_series_equal(rehydrated_from_dict.residuals, container.residuals)
        pd.testing.assert_series_equal(rehydrated_from_dict.fitted_values, container.fitted_values)
        assert rehydrated_from_dict.get_parameter("beta0") == 1.0
        assert rehydrated_from_dict.standard_errors["beta0"] == 0.1
        assert rehydrated_from_dict.get_statistic("aic") == 50.0

        # To json and from json
        json_representation = container.to_json()
        rehydrated_from_json = ResultsContainer.from_json(json_representation)

        assert rehydrated_from_json.metadata.model_type == container.metadata.model_type
        assert rehydrated_from_json.get_result("key1") == "value1"
        # Note: JSON conversion might change types e.g. tuples to lists, check accordingly
        assert rehydrated_from_json.get_diagnostic("diag1") == {"code": 100, "message": "OK"}
        pd.testing.assert_frame_equal(rehydrated_from_json.predictions, container.predictions)
        pd.testing.assert_series_equal(rehydrated_from_json.residuals, container.residuals)
        pd.testing.assert_series_equal(rehydrated_from_json.fitted_values, container.fitted_values)
        assert rehydrated_from_json.get_parameter("beta1") == 2.5
        assert rehydrated_from_json.standard_errors["beta1"] == 0.25
        assert rehydrated_from_json.get_statistic("bic") == 55.0
