import pytest
import pandas as pd
import numpy as np
from yemen_market.models.three_tier.tier1_pooled.fixed_effects import within_transform

@pytest.fixture
def sample_panel_data_for_transform():
    """Creates a sample DataFrame for testing within_transform."""
    data = {
        'id': ['A', 'A', 'A', 'B', 'B', 'B', 'C', 'C', 'C'],
        'time': [1, 2, 3, 1, 2, 3, 1, 2, 3],
        'x1': [10, 12, 14, 20, 22, 24, 30, 32, 34], # Means: A=12, B=22, C=32
        'x2': [1, 2, 3, 4, 5, 6, 7, 8, 9],       # Means: A=2, B=5, C=8
        'other_col': [100, 100, 100, 200, 200, 200, 300, 300, 300],
        'string_col': ['cat', 'dog', 'cat', 'dog', 'cat', 'dog', 'cat', 'dog', 'cat']
    }
    return pd.DataFrame(data)

def test_within_transform_basic(sample_panel_data_for_transform):
    """Test basic within transformation (demeaning)."""
    df_original = sample_panel_data_for_transform.copy()
    entity_col = 'id'
    time_col = 'time' # Not strictly used by within_transform but good to define
    cols_to_transform = ['x1', 'x2']

    df_transformed = within_transform(
        df_original.copy(), # Pass a copy to test non-inplace behavior
        group_cols=entity_col, 
        transform_cols=cols_to_transform # Changed from cols_to_transform to transform_cols
    )

    # 1. Check if transformed columns have means close to zero within each group
    for col in cols_to_transform:
        transformed_means = df_transformed.groupby(entity_col)[f"{col}_within"].mean()
        assert np.allclose(transformed_means, 0), f"Mean of transformed {col} is not zero for all groups."

    # 2. Check if other columns are unchanged
    pd.testing.assert_series_equal(
        df_original['other_col'], 
        df_transformed['other_col'], 
        check_dtype=False, # Allow for int vs float if no transformation applied
        obj=f"'other_col' was modified"
    )
    pd.testing.assert_series_equal(
        df_original['string_col'], 
        df_transformed['string_col'], 
        obj=f"'string_col' was modified"
    )
    pd.testing.assert_series_equal(
        df_original[time_col], 
        df_transformed[time_col], 
        check_dtype=False,
        obj=f"'{time_col}' was modified"
    )

    # 3. Check if original DataFrame is not modified (default inplace=False)
    pd.testing.assert_frame_equal(sample_panel_data_for_transform, df_original, obj="Original DataFrame was modified")

    # 4. Check that the transformed columns are different from original
    for col in cols_to_transform:
        assert not df_original[col].equals(df_transformed[f"{col}_within"]), f"Transformed column {col}_within should be different from original {col}."

    # 5. Check output DataFrame structure
    assert df_transformed.shape == (df_original.shape[0], df_original.shape[1] + len(cols_to_transform))
    assert all(col in df_transformed.columns for col in df_original.columns)
