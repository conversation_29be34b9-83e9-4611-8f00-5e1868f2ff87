"""Comprehensive tests for model migration utilities."""

import numpy as np
import pandas as pd
import pytest
from unittest.mock import Mock, patch, MagicMock
import pickle
import json
from pathlib import Path

from yemen_market.models.three_tier.migration.model_migration import (
    ModelMigration, MigrationConfig, VersionManager
)


class TestModelMigration:
    """Test model migration and versioning functionality."""
    
    @pytest.fixture
    def sample_model_v1(self):
        """Create a mock v1 model."""
        model = Mock()
        model.version = '1.0.0'
        model.config = {'param1': 0.5, 'param2': 100}
        model.state = {'fitted': True, 'n_obs': 1000}
        return model
    
    @pytest.fixture
    def sample_model_v2(self):
        """Create a mock v2 model."""
        model = Mock()
        model.version = '2.0.0'
        model.config = {'param1': 0.5, 'param2': 100, 'new_param': 'default'}
        model.state = {'fitted': True, 'n_obs': 1000, 'new_feature': True}
        return model
    
    @pytest.fixture
    def migration_config(self):
        """Create migration configuration."""
        return MigrationConfig(
            source_version='1.0.0',
            target_version='2.0.0',
            backup_enabled=True
        )
    
    @pytest.fixture
    def migrator(self, migration_config):
        """Create migrator instance."""
        return ModelMigration(migration_config)
    
    def test_version_comparison(self, migrator):
        """Test version comparison utilities."""
        assert migrator.compare_versions('1.0.0', '2.0.0') == -1
        assert migrator.compare_versions('2.0.0', '1.0.0') == 1
        assert migrator.compare_versions('1.0.0', '1.0.0') == 0
        
        # Test with patch versions
        assert migrator.compare_versions('1.0.1', '1.0.0') == 1
        assert migrator.compare_versions('1.2.0', '1.1.9') == 1
    
    def test_migration_needed(self, migrator, sample_model_v1):
        """Test checking if migration is needed."""
        assert migrator.is_migration_needed(sample_model_v1) == True
        
        # Same version - no migration needed
        sample_model_v1.version = '2.0.0'
        assert migrator.is_migration_needed(sample_model_v1) == False
    
    def test_migrate_config(self, migrator):
        """Test configuration migration."""
        old_config = {
            'param1': 0.5,
            'param2': 100,
            'deprecated_param': 'old_value'
        }
        
        # Define migration rules
        rules = {
            'add': {'new_param': 'default_value'},
            'remove': ['deprecated_param'],
            'rename': {'param2': 'parameter2'}
        }
        
        new_config = migrator.migrate_config(old_config, rules)
        
        assert 'new_param' in new_config
        assert new_config['new_param'] == 'default_value'
        assert 'deprecated_param' not in new_config
        assert 'param2' not in new_config
        assert 'parameter2' in new_config
        assert new_config['parameter2'] == 100
    
    def test_migrate_state(self, migrator):
        """Test state dictionary migration."""
        old_state = {
            'weights': np.array([1.0, 2.0, 3.0]),
            'bias': 0.5,
            'old_feature': 'value'
        }
        
        # Define state migration
        def state_transform(state):
            new_state = state.copy()
            # Transform weights format
            new_state['weights'] = {'values': state['weights'], 'shape': state['weights'].shape}
            # Remove old feature
            del new_state['old_feature']
            # Add new feature
            new_state['version_info'] = '2.0.0'
            return new_state
        
        new_state = migrator.migrate_state(old_state, state_transform)
        
        assert isinstance(new_state['weights'], dict)
        assert np.array_equal(new_state['weights']['values'], np.array([1.0, 2.0, 3.0]))
        assert 'old_feature' not in new_state
        assert new_state['version_info'] == '2.0.0'
    
    def test_backup_creation(self, migrator, sample_model_v1, tmp_path):
        """Test backup creation before migration."""
        backup_path = migrator.create_backup(sample_model_v1, tmp_path)
        
        assert backup_path.exists()
        assert 'backup' in backup_path.name
        assert '1.0.0' in backup_path.name
        
        # Load backup and verify
        with open(backup_path, 'rb') as f:
            backed_up = pickle.load(f)
        
        assert backed_up.version == sample_model_v1.version
        assert backed_up.config == sample_model_v1.config
    
    def test_full_migration(self, migrator, sample_model_v1):
        """Test complete model migration."""
        # Define migration steps
        migration_steps = {
            '1.0.0->2.0.0': {
                'config': {
                    'add': {'new_param': 'default'},
                    'remove': [],
                    'rename': {}
                },
                'state_transform': lambda s: {**s, 'new_feature': True}
            }
        }
        
        migrated = migrator.migrate(sample_model_v1, migration_steps)
        
        assert migrated.version == '2.0.0'
        assert 'new_param' in migrated.config
        assert migrated.state['new_feature'] == True
    
    def test_migration_validation(self, migrator):
        """Test migration validation."""
        # Valid migration
        is_valid, errors = migrator.validate_migration(
            source_version='1.0.0',
            target_version='2.0.0',
            model_type='PooledPanel'
        )
        assert is_valid
        assert len(errors) == 0
        
        # Invalid migration - downgrade
        is_valid, errors = migrator.validate_migration(
            source_version='2.0.0',
            target_version='1.0.0',
            model_type='PooledPanel'
        )
        assert not is_valid
        assert 'Downgrade not supported' in errors[0]
    
    def test_batch_migration(self, migrator, tmp_path):
        """Test migrating multiple models."""
        # Create multiple models
        models = {}
        for i in range(3):
            model = Mock()
            model.version = '1.0.0'
            model.config = {'id': i}
            model.state = {'data': f'model_{i}'}
            models[f'model_{i}'] = model
        
        # Migrate all
        migration_rules = {
            '1.0.0->2.0.0': {
                'config': {'add': {'version': 2}},
                'state_transform': lambda s: s
            }
        }
        
        migrated = migrator.migrate_batch(models, migration_rules)
        
        assert len(migrated) == 3
        for name, model in migrated.items():
            assert model.version == '2.0.0'
            assert model.config['version'] == 2
    
    def test_migration_rollback(self, migrator, sample_model_v1, tmp_path):
        """Test rolling back a failed migration."""
        # Create backup
        backup_path = migrator.create_backup(sample_model_v1, tmp_path)
        
        # Simulate failed migration
        try:
            # This should fail
            raise Exception("Migration failed")
        except:
            # Rollback
            restored = migrator.rollback(backup_path)
            
            assert restored.version == sample_model_v1.version
            assert restored.config == sample_model_v1.config
    
    def test_version_manager(self):
        """Test version management utilities."""
        vm = VersionManager()
        
        # Register versions
        vm.register_version('1.0.0', 'Initial release')
        vm.register_version('1.1.0', 'Added feature X')
        vm.register_version('2.0.0', 'Major refactor')
        
        # Get version info
        info = vm.get_version_info('1.1.0')
        assert info['description'] == 'Added feature X'
        
        # Get migration path
        path = vm.get_migration_path('1.0.0', '2.0.0')
        assert path == ['1.0.0', '1.1.0', '2.0.0']
    
    def test_compatibility_matrix(self):
        """Test model compatibility checking."""
        matrix = {
            ('1.0.0', '1.1.0'): True,
            ('1.1.0', '2.0.0'): True,
            ('1.0.0', '2.0.0'): False  # Requires intermediate step
        }
        
        migrator = ModelMigration()
        
        # Check compatibility
        assert migrator.check_compatibility('1.0.0', '1.1.0', matrix) == True
        assert migrator.check_compatibility('1.0.0', '2.0.0', matrix) == False
    
    def test_migration_history(self, migrator, tmp_path):
        """Test migration history tracking."""
        history_file = tmp_path / 'migration_history.json'
        
        # Record migration
        migrator.record_migration(
            model_id='model_1',
            source_version='1.0.0',
            target_version='2.0.0',
            timestamp='2024-01-01T00:00:00',
            success=True,
            history_file=history_file
        )
        
        # Load history
        history = migrator.load_history(history_file)
        
        assert len(history) == 1
        assert history[0]['model_id'] == 'model_1'
        assert history[0]['success'] == True
    
    def test_migration_dry_run(self, migrator, sample_model_v1):
        """Test dry run mode for migrations."""
        migration_steps = {
            '1.0.0->2.0.0': {
                'config': {'add': {'new_param': 'default'}},
                'state_transform': lambda s: s
            }
        }
        
        # Dry run - should not modify original
        report = migrator.migrate(
            sample_model_v1,
            migration_steps,
            dry_run=True
        )
        
        assert isinstance(report, dict)
        assert report['would_migrate'] == True
        assert report['changes']['config']['added'] == ['new_param']
        assert sample_model_v1.version == '1.0.0'  # Unchanged
    
    def test_custom_migration_handlers(self, migrator):
        """Test registering custom migration handlers."""
        # Register handler for specific model type
        def custom_handler(model, config):
            # Custom migration logic
            model.special_attribute = 'migrated'
            return model
        
        migrator.register_handler('SpecialModel', custom_handler)
        
        # Test with special model
        special_model = Mock()
        special_model.model_type = 'SpecialModel'
        special_model.version = '1.0.0'
        
        migrated = migrator.apply_handlers(special_model)
        assert migrated.special_attribute == 'migrated'
    
    def test_migration_exceptions(self, migrator):
        """Test exception handling during migration."""
        # Model with incompatible structure
        bad_model = Mock()
        bad_model.version = None  # No version
        
        with pytest.raises(ValueError, match="Model has no version"):
            migrator.is_migration_needed(bad_model)
        
        # Invalid migration rules
        with pytest.raises(ValueError, match="Invalid migration rules"):
            migrator.migrate(Mock(), {})
    
    def test_export_import_migration_rules(self, migrator, tmp_path):
        """Test exporting and importing migration rules."""
        rules = {
            '1.0.0->2.0.0': {
                'config': {'add': {'param': 'value'}},
                'description': 'Test migration'
            }
        }
        
        # Export
        export_path = tmp_path / 'rules.json'
        migrator.export_rules(rules, export_path)
        
        # Import
        imported = migrator.import_rules(export_path)
        
        assert imported == rules