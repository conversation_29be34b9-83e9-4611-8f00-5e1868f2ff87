"""Test suite for three-tier analysis orchestrator.

Tests the main runner that coordinates all three tiers of analysis.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import json
import tempfile

from yemen_market.models.three_tier.integration.three_tier_runner import ThreeTierAnalysis
from .test_helpers import (
    create_sample_data_with_both_naming_conventions,
    create_conflict_data_with_both_naming_conventions
)


class TestThreeTierAnalysis:
    """Test the three-tier analysis orchestrator."""
    
    @pytest.fixture
    def sample_data(self):
        """Create comprehensive sample data for all tiers."""
        np.random.seed(42)
        
        # Generate longer time series for better testing
        dates = pd.date_range('2019-01-01', periods=150, freq='W')
        markets = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Ibb']
        commodities = ['wheat', 'rice', 'sugar', 'oil']
        
        return create_sample_data_with_both_naming_conventions(dates, markets, commodities)
    
    @pytest.fixture
    def conflict_data(self):
        """Create sample conflict data."""
        # Major conflicts
        major_dates = pd.date_range('2019-03-01', '2020-06-01', freq='3M')
        # Minor conflicts
        minor_dates = pd.date_range('2019-01-15', '2020-08-01', freq='M')
        
        return create_conflict_data_with_both_naming_conventions(major_dates, minor_dates)
    
    @pytest.fixture
    def analysis(self, tmp_path):
        """Create analysis instance with temporary output directory."""
        from yemen_market.models.three_tier.tier1_pooled import PooledPanelConfig
        from yemen_market.models.three_tier.tier2_commodity import CommodityExtractorConfig
        
        config = {
            'tier1_config': PooledPanelConfig(
                entity_effects=True,
                time_effects=True,
                dependent_var='usd_price'
            ),
            'tier2_config': CommodityExtractorConfig(
                min_observations=50,
                min_markets=2,
                min_periods=20
            ),
            'tier3_config': {'n_factors': 3},
            'output_dir': str(tmp_path / 'three_tier_results'),
            'run_parallel': False
        }
        return ThreeTierAnalysis(config)
    
    def test_initialization(self, analysis, tmp_path):
        """Test three-tier analysis initialization."""
        assert analysis.tier1_config.entity_effects is True
        assert analysis.tier1_config.time_effects is True
        assert analysis.tier2_config.min_observations == 50
        assert analysis.tier3_config == {'n_factors': 3}
        assert not analysis.run_parallel
        assert analysis.output_dir.exists()
        
        # Check component initialization
        assert analysis.panel_handler is not None
        assert analysis.tier1_model is None  # Not fitted yet
        assert analysis.tier2_models == {}
        assert analysis.tier3_models == {}
    
    def test_run_full_analysis_success(self, analysis, sample_data, conflict_data):
        """Test successful full analysis run without mocks - real integration test."""
        # Run analysis with real implementations
        results = analysis.run_full_analysis(sample_data, conflict_data)
        
        # Check structure
        assert 'tier1' in results
        assert 'tier2' in results
        assert 'tier3' in results
        assert 'summary' in results
        assert 'cross_validation' in results
        
        # Check tier 1 results
        assert results['tier1'] is not None
        assert hasattr(results['tier1'], 'metadata')
        assert hasattr(results['tier1'], 'results')
        assert hasattr(results['tier1'], 'statistics')
        
        # Check tier 2 results
        assert len(results['tier2']) > 0
        for commodity, commodity_results in results['tier2'].items():
            assert commodity_results is not None
            if isinstance(commodity_results, dict) and 'results' in commodity_results:
                assert hasattr(commodity_results['results'], 'metadata')
                assert hasattr(commodity_results['results'], 'statistics')
        
        # Check tier 3 results
        assert 'static_factors' in results['tier3']
        assert 'pca_integration' in results['tier3']
        
        # Check summary
        assert 'overview' in results['summary']
        assert 'key_findings' in results['summary']
        
        # Check outputs were saved
        assert analysis.output_dir.exists()
        assert (analysis.output_dir / 'analysis_summary.json').exists()
    
    def test_run_tier1_error_handling(self, analysis, sample_data):
        """Test Tier 1 error handling."""
        # Set data before running tier1
        analysis.data = sample_data
        
        with patch.object(analysis, 'tier1_model', None):
            with patch('yemen_market.models.three_tier.integration.three_tier_runner.PooledPanelModel') as mock_pooled:
                mock_pooled.side_effect = Exception("Tier 1 failed")
                
                # Should not crash entire analysis
                analysis._run_tier1()
                
                assert 'error' in analysis.results['tier1']
                assert 'Tier 1 failed' in analysis.results['tier1']['error']
    
    def test_run_tier2_commodity_analysis(self, analysis, sample_data):
        """Test Tier 2 commodity-specific analysis."""
        analysis.data = sample_data
        
        with patch('yemen_market.models.three_tier.integration.three_tier_runner.CommodityExtractor') as mock_extractor:
            mock_commodity_extractor = Mock()
            mock_results = {
                'results': Mock(
                    get=lambda x: Mock(r_squared=0.7),
                    comparison_metrics=Mock(r_squared=0.7)
                ),
                'model': Mock()
            }
            mock_commodity_extractor.analyze_commodity.return_value = mock_results
            mock_extractor.return_value = mock_commodity_extractor
            
            analysis._run_tier2()
            
            # Should analyze all commodities
            commodities = sample_data['commodity'].unique()
            assert mock_commodity_extractor.analyze_commodity.call_count == len(commodities)
            
            # Check results storage
            for commodity in commodities:
                assert commodity in analysis.results['tier2']
    
    def test_run_tier3_validation(self, analysis, sample_data, conflict_data):
        """Test Tier 3 validation analysis."""
        analysis.data = sample_data
        analysis.conflict_data = conflict_data
        
        with patch('yemen_market.models.three_tier.integration.three_tier_runner.StaticFactorModel') as mock_static:
            with patch('yemen_market.models.three_tier.integration.three_tier_runner.PCAMarketIntegration') as mock_pca:
                with patch('yemen_market.models.three_tier.integration.three_tier_runner.ConflictIntegrationValidator') as mock_conflict:
                    # Setup mocks
                    mock_static_instance = Mock()
                    mock_static_instance.get_results.return_value = Mock()
                    mock_static.return_value = mock_static_instance
                    
                    mock_pca_instance = Mock()
                    mock_pca_instance.generate_integration_report.return_value = Mock()
                    mock_pca_instance.rolling_pca_analysis.return_value = pd.DataFrame({'pc1_variance': [0.7]})
                    mock_pca.return_value = mock_pca_instance
                    
                    mock_conflict_instance = Mock()
                    mock_conflict_instance.get_results.return_value = Mock()
                    mock_conflict.return_value = mock_conflict_instance
                    
                    analysis._run_tier3()
                    
                    # Check all components were run
                    assert 'static_factors' in analysis.results['tier3']
                    assert 'pca_integration' in analysis.results['tier3']
                    assert 'conflict_validation' in analysis.results['tier3']
                    
                    # Verify conflict validator received integration scores
                    mock_conflict_instance.fit.assert_called_once()
                    call_args = mock_conflict_instance.fit.call_args
                    assert 'integration_scores' in call_args[1]
    
    def test_cross_tier_validation(self, analysis):
        """Test cross-tier validation logic."""
        # Setup mock results
        analysis.results['tier1'] = Mock(
            coefficients={'var1': 0.5, 'var2': -0.3},
            comparison_metrics=Mock(r_squared=0.7)
        )
        
        analysis.results['tier2'] = {
            'wheat': {'coefficients': {'var1': 0.6, 'var2': -0.25}, 'r_squared': 0.75},
            'rice': {'coefficients': {'var1': 0.4, 'var2': -0.35}, 'r_squared': 0.65}
        }
        
        # Create proper loadings DataFrame with string index
        loadings_data = pd.DataFrame(
            np.random.randn(10, 3),
            index=[f'market_{i}_wheat' for i in range(3)] +
                  [f'market_{i}_rice' for i in range(3)] + 
                  [f'market_{i}_other' for i in range(4)],
            columns=['Factor_1', 'Factor_2', 'Factor_3']
        )
        
        analysis.results['tier3'] = {
            'static_factors': Mock(
                tier_specific={'loadings': loadings_data}
            ),
            'pca_integration': Mock(
                tier_specific={'overall_integration': {'pc1_variance_explained': 0.6}}
            )
        }
        
        analysis._cross_tier_validation()
        
        assert 'cross_validation' in analysis.results
        assert 'tier1_vs_tier2' in analysis.results['cross_validation']
        assert 'tier2_vs_tier3' in analysis.results['cross_validation']
        assert 'integration_consistency' in analysis.results['cross_validation']
    
    def test_generate_summary(self, analysis, sample_data):
        """Test summary generation."""
        analysis.data = sample_data
        # Create mock results with proper attributes
        tier1_mock = Mock()
        tier1_mock.metadata = Mock(warnings=['High autocorrelation detected'])
        
        tier3_pca_mock = Mock()
        tier3_pca_mock.tier_specific = {'overall_integration': {'integration_level': 'Moderate'}}
        
        tier3_conflict_mock = Mock()
        tier3_conflict_mock.tier_specific = {'event_study': {'pct_events_reducing_integration': 75.0}}
        
        analysis.results = {
            'tier1': tier1_mock,
            'tier2': {
                'wheat': {'integration_level': 'High'},
                'rice': {'integration_level': 'Low'}
            },
            'tier3': {
                'pca_integration': tier3_pca_mock,
                'conflict_validation': tier3_conflict_mock
            }
        }
        
        analysis._generate_summary()
        
        summary = analysis.results['summary']
        assert 'overview' in summary
        assert 'key_findings' in summary
        assert 'recommendations' in summary
        
        # Check overview
        assert summary['overview']['n_markets'] == sample_data['market'].nunique()
        assert summary['overview']['n_commodities'] == sample_data['commodity'].nunique()
        
        # Check findings
        assert any('High autocorrelation' in f for f in summary['key_findings'])
        assert any('75.0%' in f for f in summary['key_findings'])
    
    def test_save_results(self, analysis, tmp_path):
        """Test result saving functionality."""
        # Create mock results with proper attributes
        tier1_mock = Mock()
        tier1_mock.to_dict = lambda: {'test': 'tier1_data'}
        tier1_mock.save = Mock()
        tier1_mock.comparison_metrics = Mock(r_squared=0.75)
        
        static_factors_mock = Mock()
        static_factors_mock.to_dict = lambda: {'test': 'factor_data'}
        static_factors_mock.save = Mock()
        
        analysis.results = {
            'tier1': tier1_mock,
            'tier2': {
                'wheat': {'test': 'wheat_data'},
                'rice': {'test': 'rice_data'}
            },
            'tier3': {
                'static_factors': static_factors_mock
            },
            'cross_validation': {'test': 'validation_data'},
            'summary': {
                'overview': {
                    'n_markets': 5,
                    'n_commodities': 2,
                    'n_observations': 100,
                    'date_range': ('2020-01-01', '2020-12-31'),
                    'tiers_completed': 3
                },
                'key_findings': ['Finding 1'],
                'recommendations': []
            }
        }
        
        analysis._save_results()
        
        # Check directory structure
        assert (analysis.output_dir / 'tier1').exists()
        assert (analysis.output_dir / 'tier2').exists()
        assert (analysis.output_dir / 'tier3').exists()
        
        # Check summary files
        assert (analysis.output_dir / 'analysis_summary.json').exists()
        assert (analysis.output_dir / 'three_tier_analysis_report.md').exists()
        
        # Verify summary content
        with open(analysis.output_dir / 'analysis_summary.json') as f:
            summary = json.load(f)
            assert summary['overview']['n_markets'] == 5
    
    def test_get_commodity_comparison(self, analysis):
        """Test commodity comparison table generation."""
        analysis.results['tier2'] = {
            'wheat': {
                'integration_level': 'High',
                'n_observations': 500,
                'r_squared': 0.75,
                'threshold_value': 1.5
            },
            'rice': {
                'integration_level': 'Low',
                'n_observations': 480,
                'r_squared': 0.45
            },
            'sugar': {'error': 'Insufficient data'}
        }
        
        comparison = analysis.get_commodity_comparison()
        
        assert isinstance(comparison, pd.DataFrame)
        assert len(comparison) == 2  # Only successful commodities
        assert 'commodity' in comparison.columns
        assert 'integration_level' in comparison.columns
        assert 'has_threshold' in comparison.columns
        
        # Check values
        wheat_row = comparison[comparison['commodity'] == 'wheat'].iloc[0]
        assert wheat_row['has_threshold'] == True  # Use == instead of is for numpy booleans
        assert wheat_row['threshold_value'] == 1.5
    
    def test_invalid_data_handling(self, analysis):
        """Test handling of invalid input data."""
        invalid_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'price': np.random.randn(10)  # Missing required columns
        })
        
        with pytest.raises(ValueError, match="Invalid panel data structure"):
            analysis.run_full_analysis(invalid_data)
    
    def test_parallel_configuration(self):
        """Test parallel execution configuration."""
        config = {'run_parallel': True}
        analysis = ThreeTierAnalysis(config)
        
        assert analysis.run_parallel is True
        # Note: Actual parallel execution would require more complex mocking


class TestThreeTierIntegration:
    """Test integration aspects of three-tier analysis."""
    
    def test_data_flow_between_tiers(self, tmp_path):
        """Test that data flows correctly between tiers."""
        analysis = ThreeTierAnalysis({'output_dir': str(tmp_path)})
        
        # Create minimal valid data
        data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=100, freq='W'),
            'market': ['Sana\'a'] * 50 + ['Aden'] * 50,
            'commodity': ['wheat'] * 100,
            'price': np.random.randn(100) + 100
        })
        
        with patch('yemen_market.models.three_tier.integration.three_tier_runner.PooledPanelModel'):
            with patch('yemen_market.models.three_tier.integration.three_tier_runner.CommodityExtractor'):
                with patch('yemen_market.models.three_tier.integration.three_tier_runner.StaticFactorModel'):
                    # Run analysis
                    analysis.data = data
                    analysis.conflict_data = None  # Initialize conflict_data
                    
                    # Each tier should have access to the same base data
                    analysis._run_tier1()
                    assert analysis.data is data
                    
                    analysis._run_tier2()
                    assert analysis.data is data
                    
                    analysis._run_tier3()
                    assert analysis.data is data
    
    def test_error_propagation(self, tmp_path):
        """Test that errors in one tier don't crash others."""
        analysis = ThreeTierAnalysis({'output_dir': str(tmp_path)})
        
        data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10),
            'market': 'Sana\'a',
            'governorate': 'Sana\'a',  # Add governorate for compatibility
            'commodity': 'wheat',
            'price': [100] * 10,
            'usd_price': [100] * 10  # Add usd_price for compatibility
        })
        
        # Make Tier 1 fail
        with patch('yemen_market.models.three_tier.integration.three_tier_runner.PooledPanelModel') as mock_tier1:
            mock_tier1.side_effect = Exception("Tier 1 error")
            
            # Other tiers should still run
            with patch('yemen_market.models.three_tier.integration.three_tier_runner.CommodityExtractor') as mock_tier2:
                with patch('yemen_market.models.three_tier.integration.three_tier_runner.StaticFactorModel') as mock_tier3:
                    # Set up mocks to not fail
                    mock_extractor = Mock()
                    mock_extractor.analyze_commodity.return_value = {
                        'results': Mock(get=lambda x: Mock()),
                        'model': Mock()
                    }
                    mock_tier2.return_value = mock_extractor
                    
                    mock_static = Mock()
                    mock_static.get_results.return_value = Mock(tier_specific={})
                    mock_tier3.return_value = mock_static
                    
                    # Also set up PCA mock to avoid issues
                    with patch('yemen_market.models.three_tier.integration.three_tier_runner.PCAMarketIntegration') as mock_pca:
                        mock_pca_instance = Mock()
                        mock_pca_instance.generate_integration_report.return_value = Mock(tier_specific={})
                        mock_pca.return_value = mock_pca_instance
                    
                        # Should complete without crashing
                        results = analysis.run_full_analysis(data)
                        
                        # Check tier1 has error
                        assert isinstance(results['tier1'], dict)
                        assert 'error' in results['tier1']
                        
                        # Other tiers should still have attempted
                        assert results['tier2'] is not None
                        assert results['tier3'] is not None