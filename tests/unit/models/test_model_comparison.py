"""
Comprehensive tests for ModelComparison class.

Tests all methods and functionality with World Bank macroeconomist methodological rigor.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import matplotlib.pyplot as plt

from yemen_market.models.model_comparison import (
    ModelComparison,
    run_model_comparison,
    ComparisonSuite
)
from yemen_market.models.three_tier.core.base_model import BaseThreeTierModel
from yemen_market.models.three_tier.core.results_container import ResultsContainer


class MockModel(BaseThreeTierModel):
    """Mock model for testing."""

    def __init__(self, name: str = "mock", is_fitted: bool = True, **kwargs):
        super().__init__()
        self.name = name
        self.is_fitted = is_fitted  # Override the base class attribute
        self.mock_results = kwargs.get('results', {})
        self.mock_ic = kwargs.get('ic', {'AIC': 100, 'BIC': 110})
        self.mock_diagnostics = kwargs.get('diagnostics', {'ljung_box': 0.05})
        self.mock_coefficients = kwargs.get('coefficients', {'alpha': 0.5})

        # Add vecm_results attribute for compatibility
        self.vecm_results = Mock()
        self.vecm_results.alpha = np.array([0.5])  # Default alpha value
        self.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])

    def fit(self, data: pd.DataFrame, **kwargs) -> ResultsContainer:
        self.is_fitted = True
        return ResultsContainer(
            commodity='test',
            model_type=self.name,
            results=self.mock_results
        )

    def predict(self, steps: int = 1, **kwargs) -> pd.DataFrame:
        """Mock prediction."""
        dates = pd.date_range('2023-01-01', periods=steps, freq='M')
        return pd.DataFrame({
            'prediction': np.random.normal(100, 10, steps)
        }, index=dates)

    def get_information_criteria(self) -> dict:
        """Mock information criteria."""
        return self.mock_ic.copy()

    def get_diagnostics(self) -> dict:
        """Mock diagnostics."""
        return self.mock_diagnostics.copy()

    def get_coefficients(self, param_name: str = 'alpha') -> dict:
        """Mock coefficients."""
        return {param_name: self.mock_coefficients.get(param_name, 0.5)}

    def forecast_evaluation(self, test_data: pd.DataFrame, metrics: list) -> dict:
        """Mock forecast evaluation."""
        return {metric: np.random.uniform(0.1, 1.0) for metric in metrics}

    def validate_data(self, data: pd.DataFrame) -> tuple[bool, list[str]]:
        """Mock data validation."""
        if data is None or data.empty:
            return False, ['Data is empty or None']
        return True, []


class TestModelComparison:
    """Test ModelComparison class."""

    @pytest.fixture
    def mock_models(self):
        """Create mock models for testing."""
        model1 = MockModel(
            name="Model1",
            ic={'AIC': 100, 'BIC': 110},
            diagnostics={'ljung_box': 0.05, 'arch_test': 0.03},
            coefficients={'alpha': 0.5, 'beta': 0.3}
        )

        model2 = MockModel(
            name="Model2",
            ic={'AIC': 95, 'BIC': 105},
            diagnostics={'ljung_box': 0.02, 'arch_test': 0.01},
            coefficients={'alpha': 0.6, 'beta': 0.4}
        )

        model3 = MockModel(
            name="Model3",
            ic={'AIC': 105, 'BIC': 115},
            diagnostics={'ljung_box': 0.08, 'arch_test': 0.06},
            coefficients={'alpha': 0.4, 'beta': 0.2}
        )

        return {'Model1': model1, 'Model2': model2, 'Model3': model3}

    @pytest.fixture
    def comparison(self, mock_models):
        """Create ModelComparison instance."""
        return ModelComparison(mock_models)

    @pytest.fixture
    def empty_comparison(self):
        """Create empty ModelComparison instance."""
        return ModelComparison()

    @pytest.fixture
    def sample_test_data(self):
        """Create sample test data."""
        dates = pd.date_range('2023-01-01', periods=12, freq='M')
        return pd.DataFrame({
            'price': np.random.normal(100, 10, 12),
            'market': ['Market1'] * 12
        }, index=dates)

    def test_initialization_empty(self, empty_comparison):
        """Test initialization with no models."""
        assert len(empty_comparison.models) == 0
        assert isinstance(empty_comparison.models, dict)

    def test_initialization_with_models(self, comparison, mock_models):
        """Test initialization with models."""
        assert len(comparison.models) == 3
        assert set(comparison.models.keys()) == set(mock_models.keys())

    def test_add_model_success(self, empty_comparison):
        """Test adding a fitted model."""
        model = MockModel(name="TestModel", is_fitted=True)

        empty_comparison.add_model("TestModel", model)

        assert "TestModel" in empty_comparison.models
        assert empty_comparison.models["TestModel"] == model

    def test_add_model_unfitted_raises_error(self, empty_comparison):
        """Test adding unfitted model raises ValueError."""
        model = MockModel(name="UnfittedModel", is_fitted=False)

        with pytest.raises(ValueError, match="must be fitted before comparison"):
            empty_comparison.add_model("UnfittedModel", model)

    def test_compare_information_criteria(self, comparison):
        """Test information criteria comparison."""
        ic_df = comparison.compare_information_criteria()

        # Check structure
        assert isinstance(ic_df, pd.DataFrame)
        assert ic_df.index.name == 'Model'
        assert 'AIC' in ic_df.columns
        assert 'BIC' in ic_df.columns

        # Check values
        assert len(ic_df) == 3
        assert ic_df.loc['Model1', 'AIC'] == 100
        assert ic_df.loc['Model2', 'AIC'] == 95
        assert ic_df.loc['Model3', 'AIC'] == 105

        # Check best model identification
        assert ic_df.loc['Model2', 'AIC_best'] == True
        assert ic_df.loc['Model2', 'BIC_best'] == True

    def test_compare_information_criteria_empty_models(self, empty_comparison):
        """Test IC comparison with no models."""
        ic_df = empty_comparison.compare_information_criteria()

        assert isinstance(ic_df, pd.DataFrame)
        assert len(ic_df) == 0

    def test_compare_coefficients(self, comparison):
        """Test coefficient comparison."""
        coef_comparison = comparison.compare_coefficients('alpha')

        # Check structure
        assert isinstance(coef_comparison, dict)
        assert 'Model1' in coef_comparison
        assert 'Model2' in coef_comparison
        assert 'Model3' in coef_comparison

        # Check values
        assert coef_comparison['Model1']['alpha'] == 0.5
        assert coef_comparison['Model2']['alpha'] == 0.6
        assert coef_comparison['Model3']['alpha'] == 0.4

    def test_compare_coefficients_missing_param(self, comparison):
        """Test coefficient comparison with missing parameter."""
        coef_comparison = comparison.compare_coefficients('gamma')

        # Should return default values
        for model_name in comparison.models.keys():
            assert coef_comparison[model_name]['gamma'] == 0.5

    def test_compare_diagnostics(self, comparison):
        """Test diagnostic comparison."""
        diag_df = comparison.compare_diagnostics()

        # Check structure
        assert isinstance(diag_df, pd.DataFrame)
        assert diag_df.index.name == 'Model'
        assert 'ljung_box' in diag_df.columns
        assert 'arch_test' in diag_df.columns

        # Check values
        assert len(diag_df) == 3
        assert diag_df.loc['Model1', 'ljung_box'] == 0.05
        assert diag_df.loc['Model2', 'ljung_box'] == 0.02
        assert diag_df.loc['Model3', 'ljung_box'] == 0.08

    def test_compare_forecasts(self, comparison, sample_test_data):
        """Test forecast comparison."""
        forecast_results = comparison.compare_forecasts(steps=6, test_data=sample_test_data)

        # Check structure
        assert isinstance(forecast_results, dict)
        assert 'forecasts' in forecast_results
        assert 'metrics' in forecast_results

        # Check forecasts
        forecasts = forecast_results['forecasts']
        assert len(forecasts) == 3
        for model_name in comparison.models.keys():
            assert model_name in forecasts
            assert isinstance(forecasts[model_name], pd.DataFrame)
            assert len(forecasts[model_name]) == 6

        # Check metrics
        metrics = forecast_results['metrics']
        assert isinstance(metrics, pd.DataFrame)
        assert len(metrics) == 3
        assert 'rmse' in metrics.columns
        assert 'mae' in metrics.columns
        assert 'mape' in metrics.columns

    def test_compare_forecasts_no_test_data(self, comparison):
        """Test forecast comparison without test data."""
        forecast_results = comparison.compare_forecasts(steps=3)

        assert 'forecasts' in forecast_results
        assert 'metrics' in forecast_results
        assert forecast_results['metrics'] is None

        # Should still have forecasts
        forecasts = forecast_results['forecasts']
        assert len(forecasts) == 3

    def test_assess_dual_track_consistency(self, comparison):
        """Test dual-track consistency assessment."""
        # Mock VECM results for track models
        track1_model = comparison.models['Model1']
        track2_model = comparison.models['Model2']

        # Add mock VECM results
        track1_model.vecm_results = Mock()
        track2_model.vecm_results = Mock()
        track2_model.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])

        # Rename models to trigger track detection
        comparison.models = {
            'track1_bayesian': track1_model,
            'track2_threshold': track2_model
        }

        consistency = comparison.assess_dual_track_consistency()

        # Check structure
        assert isinstance(consistency, dict)
        # Note: Some keys may be missing if models don't have required attributes
        if 'overall' in consistency:
            assert consistency['overall'] in [
                "Strong agreement - use Track 2 for policy",
                "Moderate agreement - investigate differences",
                "Weak agreement - check model specifications"
            ]

    @patch('matplotlib.pyplot.show')
    @patch('matplotlib.pyplot.savefig')
    def test_plot_comparison(self, mock_savefig, mock_show, comparison):
        """Test comparison plotting."""
        with tempfile.TemporaryDirectory() as tmpdir:
            save_path = Path(tmpdir) / "comparison.png"

            comparison.plot_comparison(save_path=str(save_path))

            # Check savefig was called
            mock_savefig.assert_called_once()

    def test_plot_comparison_insufficient_models(self, empty_comparison):
        """Test plotting with insufficient models."""
        # Should handle gracefully with warning
        empty_comparison.plot_comparison()
        # No exception should be raised

    def test_plot_comparison_no_save_path(self, comparison):
        """Test plotting without saving."""
        with patch('matplotlib.pyplot.show'):
            comparison.plot_comparison()
        # Should complete without error

    def test_generate_comparison_table(self, comparison):
        """Test comparison table generation."""
        table = comparison.generate_comparison_table()

        assert isinstance(table, pd.DataFrame)
        assert len(table) == 3
        assert 'Model' in table.columns or table.index.name == 'Model'

    def test_save_results(self, comparison):
        """Test saving comparison results."""
        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)

            comparison.save_results(output_dir)

            # Check files were created
            assert (output_dir / "information_criteria.csv").exists()
            assert (output_dir / "diagnostics.csv").exists()
            assert (output_dir / "comparison_plot.png").exists()

    def test_private_calculate_parameter_correlation(self, comparison):
        """Test parameter correlation calculation."""
        coef1 = {'alpha': 0.5, 'beta': 0.3}
        coef2 = {'alpha': 0.6, 'beta': 0.4}

        correlation = comparison._calculate_parameter_correlation(coef1, coef2)

        assert isinstance(correlation, float)
        assert -1 <= correlation <= 1

    def test_private_calculate_regime_agreement(self, comparison):
        """Test regime agreement calculation."""
        model1 = Mock()
        model1.vecm_results = Mock()
        model1.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])

        model2 = Mock()
        model2.vecm_results = Mock()
        model2.vecm_results.regime_assignment = np.array([0, 1, 1, 1, 0])

        agreement = comparison._calculate_regime_agreement(model1, model2)

        assert isinstance(agreement, (float, type(np.nan)))
        # Allow for NaN in case of missing data
        if not np.isnan(agreement):
            assert 0 <= agreement <= 1

    def test_error_handling_forecast_failure(self, comparison):
        """Test handling of forecast failures."""
        # Mock a model that raises exception during prediction
        failing_model = Mock()
        failing_model.predict.side_effect = Exception("Forecast failed")
        comparison.models['FailingModel'] = failing_model

        # Should handle gracefully
        forecast_results = comparison.compare_forecasts(steps=3)

        # Should still return results for other models
        assert 'forecasts' in forecast_results
        assert len(forecast_results['forecasts']) >= 2  # Original models should work


class TestRunModelComparison:
    """Test run_model_comparison convenience function."""

    @pytest.fixture
    def mock_models(self):
        """Create mock models for testing."""
        return {
            'Model1': MockModel(name="Model1"),
            'Model2': MockModel(name="Model2")
        }

    def test_run_model_comparison_basic(self, mock_models):
        """Test basic model comparison run."""
        comparison = run_model_comparison(mock_models)

        assert isinstance(comparison, ModelComparison)
        assert len(comparison.models) == 2

    def test_run_model_comparison_with_test_data(self, mock_models):
        """Test model comparison with test data."""
        test_data = pd.DataFrame({
            'price': np.random.normal(100, 10, 12),
            'market': ['Market1'] * 12
        })

        comparison = run_model_comparison(mock_models, test_data=test_data)

        assert isinstance(comparison, ModelComparison)

    def test_run_model_comparison_with_output_dir(self, mock_models):
        """Test model comparison with output directory."""
        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)

            comparison = run_model_comparison(
                mock_models,
                output_dir=output_dir
            )

            assert isinstance(comparison, ModelComparison)
            # Check some output files were created
            assert any(output_dir.iterdir())


class TestComparisonSuite:
    """Test ComparisonSuite class."""

    @pytest.fixture
    def mock_models(self):
        """Create mock models for testing."""
        return {
            'Model1': MockModel(name="Model1"),
            'Model2': MockModel(name="Model2"),
            'Model3': MockModel(name="Model3")
        }

    @pytest.fixture
    def comparison_suite(self, mock_models):
        """Create ComparisonSuite instance."""
        suite = ComparisonSuite()
        suite.add_comparison("Test Comparison", mock_models)
        return suite

    def test_comparison_suite_initialization(self):
        """Test ComparisonSuite initialization."""
        suite = ComparisonSuite()
        assert len(suite.comparisons) == 0

    def test_add_comparison(self, mock_models):
        """Test adding comparison to suite."""
        suite = ComparisonSuite()
        suite.add_comparison("Test", mock_models)

        assert len(suite.comparisons) == 1
        assert isinstance(suite.comparisons[0], ModelComparison)

    def test_generate_report(self, comparison_suite):
        """Test report generation."""
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = Path(tmpdir) / "report.txt"

            report = comparison_suite.generate_report(output_path)

            assert isinstance(report, str)
            assert "Model Comparison Report" in report
            assert output_path.exists()

    def test_generate_report_no_output_path(self, comparison_suite):
        """Test report generation without output path."""
        report = comparison_suite.generate_report()

        assert isinstance(report, str)
        assert "Model Comparison Report" in report


class TestEdgeCasesAndErrorHandling:
    """Test edge cases and error handling."""

    def test_empty_models_dict(self):
        """Test behavior with empty models dictionary."""
        comparison = ModelComparison({})

        # All methods should handle empty models gracefully
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 0

        diag_df = comparison.compare_diagnostics()
        assert len(diag_df) == 0

        coef_comp = comparison.compare_coefficients()
        assert len(coef_comp) == 0

    def test_single_model_comparison(self):
        """Test comparison with single model."""
        model = MockModel(name="SingleModel")
        comparison = ModelComparison({'SingleModel': model})

        # Should work with single model
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 1

        # Plotting should warn about insufficient models
        comparison.plot_comparison()

    def test_models_with_missing_methods(self):
        """Test handling of models missing expected methods."""
        incomplete_model = Mock()
        incomplete_model.is_fitted = True
        incomplete_model.get_information_criteria.side_effect = AttributeError("Method not found")

        comparison = ModelComparison({'IncompleteModel': incomplete_model})

        # Should handle missing methods gracefully
        ic_df = comparison.compare_information_criteria()
        # Should return empty or handle error appropriately

    def test_models_with_different_ic_keys(self):
        """Test models returning different IC keys."""
        model1 = MockModel(name="Model1", ic={'AIC': 100, 'BIC': 110})
        model2 = MockModel(name="Model2", ic={'AIC': 95, 'HQIC': 105})  # Different keys

        comparison = ModelComparison({'Model1': model1, 'Model2': model2})

        ic_df = comparison.compare_information_criteria()

        # Should handle different keys gracefully
        assert len(ic_df) == 2
        assert 'AIC' in ic_df.columns

    def test_forecast_with_different_prediction_formats(self):
        """Test forecasting with models returning different formats."""
        model1 = MockModel(name="Model1")

        # Model that returns different format
        model2 = Mock()
        model2.is_fitted = True
        model2.predict.return_value = pd.Series([1, 2, 3])  # Series instead of DataFrame

        comparison = ModelComparison({'Model1': model1, 'Model2': model2})

        # Should handle different return formats
        forecast_results = comparison.compare_forecasts(steps=3)
        assert 'forecasts' in forecast_results

    def test_coefficient_comparison_with_complex_structures(self):
        """Test coefficient comparison with complex coefficient structures."""
        # Model with nested coefficient structure
        complex_model = MockModel(
            name="ComplexModel",
            coefficients={'alpha': {'value': 0.5, 'std_err': 0.1}}
        )

        simple_model = MockModel(
            name="SimpleModel",
            coefficients={'alpha': 0.6}
        )

        comparison = ModelComparison({
            'ComplexModel': complex_model,
            'SimpleModel': simple_model
        })

        # Should handle different coefficient structures
        coef_comp = comparison.compare_coefficients('alpha')
        assert 'ComplexModel' in coef_comp
        assert 'SimpleModel' in coef_comp

    def test_large_number_of_models(self):
        """Test performance with large number of models."""
        models = {}
        for i in range(20):
            models[f'Model_{i}'] = MockModel(  # Use underscore instead of concatenating str + int
                name=f"Model_{i}",
                ic={'AIC': 100 + i, 'BIC': 110 + i}
            )

        comparison = ModelComparison(models)

        # Should handle large number of models
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 20

        # Best model identification should work
        assert ic_df['AIC_best'].sum() == 1  # Only one best model

    def test_unicode_and_special_characters_in_names(self):
        """Test handling of unicode and special characters in model names."""
        models = {
            'Model_α': MockModel(name="Model_α"),
            'Model-β': MockModel(name="Model-β"),
            'Model (γ)': MockModel(name="Model (γ)")
        }

        comparison = ModelComparison(models)

        # Should handle special characters in names
        ic_df = comparison.compare_information_criteria()
        assert len(ic_df) == 3
        assert 'Model_α' in ic_df.index

    def test_memory_efficiency_with_large_forecasts(self):
        """Test memory efficiency with large forecast arrays."""
        # Model that returns large forecasts
        large_model = MockModel(name="LargeModel")
        large_model.predict = lambda steps=1, **kwargs: pd.DataFrame({
            'prediction': np.random.normal(100, 10, steps)
        })

        comparison = ModelComparison({'LargeModel': large_model})

        # Should handle large forecasts efficiently
        forecast_results = comparison.compare_forecasts(steps=1000)
        assert len(forecast_results['forecasts']['LargeModel']) == 1000

    def test_thread_safety_simulation(self):
        """Test thread safety by simulating concurrent access."""
        import threading
        import time

        comparison = ModelComparison()
        results = []

        def add_models_concurrently(thread_id):
            for i in range(5):
                model = MockModel(name=f"Thread{thread_id}_Model{i}")
                comparison.add_model(f"Thread{thread_id}_Model{i}", model)
                time.sleep(0.001)  # Small delay to increase chance of race conditions

        # Create multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=add_models_concurrently, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Should have all models added
        assert len(comparison.models) == 15


class TestAdvancedModelComparisonCoverage:
    """Additional tests to achieve 100% coverage of model_comparison.py."""

    @pytest.fixture
    def advanced_models(self):
        """Create models with various configurations for comprehensive testing."""
        # Model with vecm_results but no get_coefficients
        model1 = Mock()
        model1.is_fitted = True
        model1.vecm_results = Mock()
        model1.vecm_results.alpha = np.array([0.1, 0.2])
        model1.get_information_criteria = Mock(return_value={'AIC': 100, 'BIC': 110})
        model1.predict = Mock(return_value=pd.DataFrame({'prediction': [1, 2, 3]}))
        # Remove get_coefficients method to force vecm_results path
        del model1.get_coefficients

        # Model with posterior_means
        model2 = Mock()
        model2.is_fitted = True
        model2.vecm_results = Mock()
        model2.vecm_results.posterior_means = {'alpha': np.array([0.15, 0.25])}
        model2.get_information_criteria = Mock(return_value={'AIC': 95, 'BIC': 105})
        model2.predict = Mock(return_value=pd.DataFrame({'prediction': [1.1, 2.1, 3.1]}))
        # Remove get_coefficients method to force vecm_results path
        del model2.get_coefficients

        # Model with regime-specific parameters
        model3 = Mock()
        model3.is_fitted = True
        model3.vecm_results = Mock()
        model3.vecm_results.low_regime_alpha = np.array([0.05, 0.1])
        model3.vecm_results.high_regime_alpha = np.array([0.2, 0.3])
        model3.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])
        model3.get_information_criteria = Mock(return_value={'AIC': 105, 'BIC': 115})
        model3.predict = Mock(return_value=pd.DataFrame({'prediction': [0.9, 1.9, 2.9]}))
        # Remove get_coefficients method to force vecm_results path
        del model3.get_coefficients

        # Model with diagnostics attribute instead of get_diagnostics method
        model4 = Mock()
        model4.is_fitted = True
        model4.diagnostics = {
            'ljung_box': {'p_value': 0.03},
            'arch_test': 0.07,  # Direct value
            'jarque_bera': {'p_value': 0.12}
        }
        model4.get_information_criteria = Mock(return_value={'AIC': 98, 'BIC': 108})
        model4.predict = Mock(return_value=pd.DataFrame({'prediction': [1.2, 2.2, 3.2]}))
        # Remove get_diagnostics method to force diagnostics attribute path
        del model4.get_diagnostics

        return {
            'VecmModel': model1,
            'BayesianModel': model2,
            'ThresholdModel': model3,
            'DiagnosticsModel': model4
        }

    def test_compare_coefficients_vecm_results_paths(self, advanced_models):
        """Test all vecm_results coefficient extraction paths."""
        comparison = ModelComparison(advanced_models)

        # Test direct attribute access
        coef_dict = comparison.compare_coefficients('alpha')

        assert 'VecmModel' in coef_dict
        assert 'BayesianModel' in coef_dict
        assert 'ThresholdModel' in coef_dict

        # Check regime-specific parameters - should be a dict with low/high
        if 'ThresholdModel' in coef_dict:
            threshold_coefs = coef_dict['ThresholdModel']
            if isinstance(threshold_coefs, dict):
                assert 'low' in threshold_coefs
                assert 'high' in threshold_coefs

    def test_compare_diagnostics_attribute_path(self, advanced_models):
        """Test diagnostics extraction from diagnostics attribute."""
        comparison = ModelComparison(advanced_models)

        diag_df = comparison.compare_diagnostics()

        assert isinstance(diag_df, pd.DataFrame)
        assert 'DiagnosticsModel' in diag_df.index

        # Check p_value extraction
        assert diag_df.loc['DiagnosticsModel', 'ljung_box'] == 0.03
        # Check direct value
        assert diag_df.loc['DiagnosticsModel', 'arch_test'] == 0.07

        # Check failure flags
        assert 'ljung_box_fail' in diag_df.columns
        assert diag_df.loc['DiagnosticsModel', 'ljung_box_fail'] == True  # p < 0.05

    def test_compare_diagnostics_no_results(self):
        """Test diagnostics comparison when no results available."""
        model = Mock()
        model.is_fitted = True
        # No get_diagnostics method and no diagnostics attribute

        comparison = ModelComparison({'EmptyModel': model})

        diag_df = comparison.compare_diagnostics()
        assert isinstance(diag_df, pd.DataFrame)
        assert len(diag_df) == 0

    def test_assess_dual_track_consistency_full_coverage(self):
        """Test dual-track consistency with full coverage of all paths."""
        # Create track1 (Bayesian) model
        track1_model = Mock()
        track1_model.is_fitted = True
        track1_model.vecm_results = Mock()
        track1_model.get_coefficients = Mock(return_value={'alpha': 0.5})
        track1_model.predict = Mock(return_value=pd.DataFrame({'prediction': [1, 2, 3, 4, 5, 6]}))
        track1_model.forecast_evaluation = Mock(return_value={'rmse': 0.1, 'mae': 0.08, 'mape': 0.05})

        # Create track2 (Threshold) model
        track2_model = Mock()
        track2_model.is_fitted = True
        track2_model.vecm_results = Mock()
        track2_model.vecm_results.regime_assignment = np.array([0, 1, 0, 1, 0])
        track2_model.get_coefficients = Mock(return_value={'alpha': 0.6})
        track2_model.predict = Mock(return_value=pd.DataFrame({'prediction': [1.1, 2.1, 3.1, 4.1, 5.1, 6.1]}))
        track2_model.forecast_evaluation = Mock(return_value={'rmse': 0.12, 'mae': 0.09, 'mape': 0.06})

        comparison = ModelComparison({
            'track1_bayesian': track1_model,
            'track2_threshold': track2_model
        })

        # Test with test data to trigger forecast metrics
        test_data = pd.DataFrame({'price': [1, 2, 3, 4, 5, 6]})

        # Mock compare_forecasts to return metrics
        with patch.object(comparison, 'compare_forecasts') as mock_forecasts:
            mock_forecasts.return_value = {
                'forecasts': {'track1_bayesian': pd.DataFrame(), 'track2_threshold': pd.DataFrame()},
                'metrics': pd.DataFrame({
                    'track1_bayesian': {'rmse': 0.1, 'mae': 0.08},
                    'track2_threshold': {'rmse': 0.12, 'mae': 0.09}
                }).T
            }

            consistency = comparison.assess_dual_track_consistency()

        # Should have all consistency metrics
        assert 'parameter_correlation' in consistency
        assert 'regime_agreement' in consistency
        assert 'forecast_metrics' in consistency
        assert 'overall' in consistency

        # Test different correlation levels for overall assessment
        assert consistency['overall'] in [
            "Strong agreement - use Track 2 for policy",
            "Moderate agreement - investigate differences",
            "Weak agreement - check model specifications"
        ]

    def test_generate_comparison_table_empty_models(self):
        """Test comparison table generation with empty models."""
        comparison = ModelComparison({})

        table = comparison.generate_comparison_table()
        assert isinstance(table, pd.DataFrame)
        assert len(table) == 0

    def test_generate_comparison_table_merge_scenarios(self):
        """Test different merge scenarios in comparison table generation."""
        # Model with IC but no diagnostics
        model1 = Mock()
        model1.is_fitted = True
        model1.get_information_criteria = Mock(return_value={'AIC': 100, 'BIC': 110})

        # Model with diagnostics but no IC
        model2 = Mock()
        model2.is_fitted = True
        model2.get_diagnostics = Mock(return_value={'ljung_box': 0.05})
        model2.get_information_criteria = Mock(side_effect=Exception("No IC"))

        # Model with both
        model3 = Mock()
        model3.is_fitted = True
        model3.get_information_criteria = Mock(return_value={'AIC': 95, 'BIC': 105})
        model3.get_diagnostics = Mock(return_value={'ljung_box': 0.03})

        comparison = ModelComparison({
            'ICOnly': model1,
            'DiagOnly': model2,
            'Both': model3
        })

        table = comparison.generate_comparison_table()
        assert isinstance(table, pd.DataFrame)
        assert len(table) == 3

    def test_parameter_correlation_edge_cases(self):
        """Test parameter correlation calculation edge cases."""
        comparison = ModelComparison({})

        # Test no common parameters
        coef1 = {'alpha': 0.5}
        coef2 = {'beta': 0.6}
        corr = comparison._calculate_parameter_correlation(coef1, coef2)
        assert corr == 0.0

        # Test nested value structures
        coef1 = {'alpha': {'value': 0.5}, 'beta': 0.3}
        coef2 = {'alpha': {'value': 0.6}, 'beta': 0.4}
        corr = comparison._calculate_parameter_correlation(coef1, coef2)
        assert isinstance(corr, float)
        assert -1 <= corr <= 1

        # Test single parameter (should return 1.0 if equal, 0.0 if different)
        coef1 = {'alpha': 0.5}
        coef2 = {'alpha': 0.5}
        corr = comparison._calculate_parameter_correlation(coef1, coef2)
        assert corr == 1.0

        coef1 = {'alpha': 0.5}
        coef2 = {'alpha': 0.6}
        corr = comparison._calculate_parameter_correlation(coef1, coef2)
        assert corr == 0.0

    def test_get_regime_assignment_simple_cases(self):
        """Test _get_regime_assignment method with simpler cases."""
        comparison = ModelComparison({})

        # Test 1: Direct regime assignment with dates
        model1 = Mock()
        model1.vecm_results = Mock()
        model1.vecm_results.regime_assignment = np.array([0, 1, 0, 1])
        model1.data = {'dates': pd.date_range('2023-01-01', periods=4, freq='M')}

        regime = comparison._get_regime_assignment(model1)
        assert isinstance(regime, pd.Series)
        assert len(regime) == 4
        assert isinstance(regime.index, pd.DatetimeIndex)

        # Test 2: Direct regime assignment without dates
        model2 = Mock()
        model2.vecm_results = Mock()
        model2.vecm_results.regime_assignment = np.array([0, 1, 0])
        # Remove data attribute completely to test the hasattr path
        if hasattr(model2, 'data'):
            delattr(model2, 'data')

        regime = comparison._get_regime_assignment(model2)
        assert isinstance(regime, pd.Series)
        assert len(regime) == 3

        # Test 3: Conflict intensity data as Series
        model3 = Mock()
        model3.data = {
            'conflict_intensity': pd.Series([30, 60, 40, 70],
                                          index=pd.date_range('2023-01-01', periods=4, freq='M'))
        }
        model3.threshold = 50
        # Ensure vecm_results is not present
        model3.vecm_results = None

        regime = comparison._get_regime_assignment(model3)
        assert isinstance(regime, pd.Series)
        assert regime.iloc[0] == 0  # 30 < 50
        assert regime.iloc[1] == 1  # 60 > 50

        # Test 4: No regime information available
        model4 = Mock()
        # No vecm_results, no data
        model4.vecm_results = None
        model4.data = {}  # Empty dict instead of Mock to avoid 'in' operator issue

        regime = comparison._get_regime_assignment(model4)
        assert regime is None

    def test_calculate_regime_agreement_comprehensive(self):
        """Test _calculate_regime_agreement with comprehensive coverage."""
        comparison = ModelComparison({})

        # Test with datetime indices
        model1 = Mock()
        model1.vecm_results = Mock()
        dates = pd.date_range('2023-01-01', periods=5, freq='M')
        model1.vecm_results.regime_assignment = pd.Series([0, 1, 0, 1, 0], index=dates)

        model2 = Mock()
        model2.vecm_results = Mock()
        # Overlapping but different date range
        dates2 = pd.date_range('2023-02-01', periods=4, freq='M')
        model2.vecm_results.regime_assignment = pd.Series([1, 0, 1, 0], index=dates2)

        # Mock _get_regime_assignment to return our test data
        with patch.object(comparison, '_get_regime_assignment') as mock_get_regime:
            mock_get_regime.side_effect = [
                pd.Series([0, 1, 0, 1, 0], index=dates, name='regime'),
                pd.Series([1, 0, 1, 0], index=dates2, name='regime')
            ]

            agreement = comparison._calculate_regime_agreement(model1, model2)
            assert isinstance(agreement, float)
            assert 0 <= agreement <= 1

    def test_calculate_regime_agreement_edge_cases(self):
        """Test regime agreement calculation edge cases."""
        comparison = ModelComparison({})

        # Test with no overlapping dates
        model1 = Mock()
        model2 = Mock()

        with patch.object(comparison, '_get_regime_assignment') as mock_get_regime:
            dates1 = pd.date_range('2023-01-01', periods=3, freq='M')
            dates2 = pd.date_range('2024-01-01', periods=3, freq='M')

            mock_get_regime.side_effect = [
                pd.Series([0, 1, 0], index=dates1, name='regime'),
                pd.Series([1, 0, 1], index=dates2, name='regime')
            ]

            agreement = comparison._calculate_regime_agreement(model1, model2)
            assert np.isnan(agreement)

    def test_calculate_regime_agreement_with_conflict_analysis(self):
        """Test regime agreement with conflict data analysis."""
        comparison = ModelComparison({})

        model1 = Mock()
        model2 = Mock()

        # Set up model2 with conflict data and threshold
        model2.data = {
            'conflict_intensity': pd.Series([45, 55, 35, 65, 40],
                                          index=pd.date_range('2023-01-01', periods=5, freq='M'))
        }
        model2.threshold = 50

        with patch.object(comparison, '_get_regime_assignment') as mock_get_regime:
            dates = pd.date_range('2023-01-01', periods=5, freq='M')
            # Create disagreement at periods where conflict is near threshold
            regime1 = pd.Series([0, 1, 0, 1, 0], index=dates, name='regime')
            regime2 = pd.Series([1, 1, 1, 1, 1], index=dates, name='regime')  # Disagreement

            mock_get_regime.side_effect = [regime1, regime2]

            agreement = comparison._calculate_regime_agreement(model1, model2)
            assert isinstance(agreement, float)
            assert 0 <= agreement <= 1

    @patch('matplotlib.pyplot.show')
    @patch('matplotlib.pyplot.savefig')
    def test_plot_comparison_comprehensive_coverage(self, mock_savefig, mock_show):
        """Test plot_comparison with comprehensive coverage of all plotting paths."""
        # Create models with various attributes for plotting
        model1 = Mock()
        model1.is_fitted = True
        model1.vecm_results = Mock()
        model1.vecm_results.alpha = np.array([0.1, 0.2])
        model1.get_information_criteria = Mock(return_value={'AIC': 100, 'BIC': 110})
        model1.get_diagnostics = Mock(return_value={'ljung_box': 0.03, 'arch_test': 0.07})

        model2 = Mock()
        model2.is_fitted = True
        model2.vecm_results = Mock()
        model2.vecm_results.alpha = np.array([0.15, 0.25])
        model2.get_information_criteria = Mock(return_value={'AIC': 95, 'BIC': 105})
        model2.get_diagnostics = Mock(return_value={'ljung_box': 0.02, 'arch_test': 0.08})

        comparison = ModelComparison({
            'Model1': model1,
            'Model2': model2
        })

        # Mock assess_dual_track_consistency to return comprehensive results
        with patch.object(comparison, 'assess_dual_track_consistency') as mock_consistency:
            mock_consistency.return_value = {
                'parameter_correlation': 0.85,
                'regime_agreement': 0.75,
                'overall': 'Strong agreement - use Track 2 for policy'
            }

            comparison.plot_comparison()

            # Verify show was called (no save path)
            mock_show.assert_called_once()

    def test_run_model_comparison_comprehensive(self):
        """Test run_model_comparison function with comprehensive coverage."""
        models = {
            'Model1': MockModel(name="Model1"),
            'Model2': MockModel(name="Model2")
        }

        test_data = pd.DataFrame({
            'price': [100, 105, 98, 102],
            'date': pd.date_range('2023-01-01', periods=4, freq='M')
        })

        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)

            # Test with all parameters
            comparison = run_model_comparison(
                models=models,
                test_data=test_data,
                output_dir=output_dir
            )

            assert isinstance(comparison, ModelComparison)
            assert len(comparison.models) == 2

            # Check that output files were created
            assert any(output_dir.iterdir())

    def test_comparison_suite_comprehensive_coverage(self):
        """Test ComparisonSuite with comprehensive coverage."""
        suite = ComparisonSuite()

        # Add multiple comparisons
        models1 = {'Model1': MockModel(name="Model1"), 'Model2': MockModel(name="Model2")}
        models2 = {'Model3': MockModel(name="Model3"), 'Model4': MockModel(name="Model4")}

        suite.add_comparison("Comparison 1", models1)
        suite.add_comparison("Comparison 2", models2)

        assert len(suite.comparisons) == 2

        # Test report generation with output path
        with tempfile.TemporaryDirectory() as tmpdir:
            output_path = Path(tmpdir) / "suite_report.txt"

            report = suite.generate_report(output_path)

            assert isinstance(report, str)
            assert "Model Comparison Report" in report
            assert "Comparison 1" in report
            assert "Comparison 2" in report
            assert output_path.exists()

            # Read the file to verify content
            file_content = output_path.read_text()
            assert file_content == report

    def test_comparison_suite_error_handling(self):
        """Test ComparisonSuite error handling in report generation."""
        suite = ComparisonSuite()

        # Create a comparison that will fail during consistency assessment
        failing_model = Mock()
        failing_model.is_fitted = True

        models = {'FailingModel': failing_model}
        suite.add_comparison("Failing Comparison", models)

        # Generate report - should handle exceptions gracefully
        report = suite.generate_report()

        assert isinstance(report, str)
        assert "Model Comparison Report" in report
        # The comparison name is stored as key, so check for "Failing Comparison"
        assert "Failing Comparison" in report or "Comparison 1" in report

    def test_plot_comparison_detailed_coverage(self):
        """Test plot_comparison with detailed coverage of plotting paths."""
        # Create models with specific attributes for plotting
        model1 = Mock()
        model1.is_fitted = True
        model1.vecm_results = Mock()
        model1.vecm_results.alpha = np.array([0.1, 0.2])
        model1.get_information_criteria = Mock(return_value={'AIC': 100, 'BIC': 110})
        model1.get_diagnostics = Mock(return_value={'ljung_box': 0.03, 'arch_test': 0.07})

        model2 = Mock()
        model2.is_fitted = True
        model2.vecm_results = Mock()
        model2.vecm_results.alpha = np.array([0.15, 0.25])
        model2.get_information_criteria = Mock(return_value={'AIC': 95, 'BIC': 105})
        model2.get_diagnostics = Mock(return_value={'ljung_box': 0.02, 'arch_test': 0.08})

        comparison = ModelComparison({
            'Model1': model1,
            'Model2': model2
        })

        with patch('matplotlib.pyplot.subplots') as mock_subplots, \
             patch('matplotlib.pyplot.show') as mock_show, \
             patch('matplotlib.pyplot.savefig') as mock_savefig:

            # Mock the figure and axes
            mock_fig = Mock()
            mock_axes = [Mock() for _ in range(4)]
            mock_subplots.return_value = (mock_fig, np.array(mock_axes).reshape(2, 2))

            # Test without save path
            comparison.plot_comparison()
            mock_show.assert_called_once()

            # Reset mocks
            mock_show.reset_mock()
            mock_savefig.reset_mock()

            # Test with save path
            comparison.plot_comparison(save_path="/tmp/test.png")
            mock_savefig.assert_called_once()

    def test_save_results_comprehensive(self):
        """Test save_results method with comprehensive coverage."""
        model1 = MockModel(name="Model1")
        model2 = MockModel(name="Model2")

        comparison = ModelComparison({
            'Model1': model1,
            'Model2': model2
        })

        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)

            # Mock plot_comparison to avoid matplotlib issues
            with patch.object(comparison, 'plot_comparison') as mock_plot:
                comparison.save_results(output_dir)

                # Check that files were created
                assert (output_dir / "information_criteria.csv").exists()
                assert (output_dir / "diagnostics.csv").exists()

                # Check that plot_comparison was called
                mock_plot.assert_called_once_with(str(output_dir / "comparison_plot.png"))

    def test_run_model_comparison_all_paths(self):
        """Test run_model_comparison function covering all execution paths."""
        models = {
            'Model1': MockModel(name="Model1"),
            'Model2': MockModel(name="Model2")
        }

        test_data = pd.DataFrame({
            'price': [100, 105, 98, 102],
            'date': pd.date_range('2023-01-01', periods=4, freq='M')
        })

        with tempfile.TemporaryDirectory() as tmpdir:
            output_dir = Path(tmpdir)

            # Mock the save_results method to avoid file I/O issues
            with patch.object(ModelComparison, 'save_results') as mock_save:
                comparison = run_model_comparison(
                    models=models,
                    test_data=test_data,
                    output_dir=output_dir
                )

                assert isinstance(comparison, ModelComparison)
                assert len(comparison.models) == 2

                # Check that save_results was called
                mock_save.assert_called_once_with(output_dir)

    def test_information_criteria_edge_cases(self):
        """Test information criteria comparison edge cases."""
        # Model that raises exception during IC retrieval
        failing_model = Mock()
        failing_model.is_fitted = True
        failing_model.get_information_criteria = Mock(side_effect=Exception("IC failed"))

        # Model with missing IC values
        partial_model = Mock()
        partial_model.is_fitted = True
        partial_model.get_information_criteria = Mock(return_value={'AIC': 100})  # Missing BIC

        comparison = ModelComparison({
            'FailingModel': failing_model,
            'PartialModel': partial_model
        })

        ic_df = comparison.compare_information_criteria()

        # Should handle failures gracefully
        assert isinstance(ic_df, pd.DataFrame)
        assert 'PartialModel' in ic_df.index

        # Check that best model identification works with partial data
        if 'AIC_best' in ic_df.columns:
            assert ic_df['AIC_best'].sum() <= 1  # At most one best model

    def test_coefficient_comparison_error_handling(self):
        """Test coefficient comparison error handling."""
        # Model that raises exception during coefficient retrieval
        failing_model = Mock()
        failing_model.is_fitted = True
        failing_model.get_coefficients = Mock(side_effect=Exception("Coef failed"))

        comparison = ModelComparison({'FailingModel': failing_model})

        # Should handle failures gracefully
        coef_dict = comparison.compare_coefficients('alpha')
        assert isinstance(coef_dict, dict)
        # May or may not contain the failing model depending on error handling
