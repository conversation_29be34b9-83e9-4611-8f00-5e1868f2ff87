"""Unit tests for ACLED conflict data processor."""

import pytest
import pandas as pd
import numpy as np
import geopandas as gpd
from shapely.geometry import Point
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from yemen_market.data.acled_processor import ACLEDProcessor


@pytest.fixture
def mock_acled_data():
    """Create mock ACLED event data."""
    dates = pd.date_range('2019-01-01', '2019-12-31', freq='D')
    n_events = 100
    
    # Sample events
    data = {
        'event_date': np.random.choice(dates, n_events),
        'latitude': np.random.uniform(12.5, 17.5, n_events),
        'longitude': np.random.uniform(42.5, 54.0, n_events),
        'event_type': np.random.choice([
            'Battles', 'Explosions/Remote violence', 
            'Violence against civilians', 'Protests'
        ], n_events),
        'fatalities': np.random.poisson(2, n_events),
        'actor1': np.random.choice(['<PERSON>uth<PERSON>', 'Government', 'AQAP'], n_events),
        'actor2': np.random.choice(['Government', 'Civilians', 'Unknown'], n_events),
    }
    
    return pd.DataFrame(data)


@pytest.fixture
def mock_market_locations():
    """Create mock market location data."""
    markets = {
        'market_id': ['Sana\'a_Main', 'Aden_Port', 'Taiz_Central'],
        'governorate': ['Sana\'a', 'Aden', 'Taiz'],
        'market_name': ['Main Market', 'Port Market', 'Central Market'],
        'lat': [15.3694, 12.7855, 13.5795],
        'lon': [44.1910, 45.0187, 44.0219]
    }
    
    df = pd.DataFrame(markets)
    geometry = [Point(lon, lat) for lon, lat in zip(df['lon'], df['lat'])]
    
    return gpd.GeoDataFrame(df, geometry=geometry, crs='EPSG:4326')


@pytest.fixture
def processor(tmp_path):
    """Create ACLEDProcessor instance with temp directories."""
    data_dir = tmp_path / "raw" / "acled"
    output_dir = tmp_path / "processed" / "conflict"
    data_dir.mkdir(parents=True)
    
    return ACLEDProcessor(
        data_dir=data_dir,
        output_dir=output_dir,
        radius_km=50.0
    )


class TestACLEDProcessor:
    """Test ACLED processor functionality."""
    
    def test_initialization(self, processor):
        """Test processor initialization."""
        assert processor.radius_km == 50.0
        assert processor.output_dir.exists()
        assert len(processor.EVENT_TYPES) == 6
        assert len(processor.KEY_ACTORS) == 6
    
    def test_load_acled_data(self, processor, mock_acled_data):
        """Test loading ACLED data."""
        # Save mock data
        acled_file = processor.data_dir / "yemen_acled_2019.csv"
        mock_acled_data.to_csv(acled_file, index=False)
        
        # Load data
        df = processor.load_acled_data()
        
        assert len(df) == len(mock_acled_data)
        assert 'event_date' in df.columns
        assert df['event_date'].dtype == 'datetime64[ns]'
    
    def test_load_acled_data_with_date_filter(self, processor, mock_acled_data):
        """Test loading ACLED data with date filtering."""
        # Save mock data
        acled_file = processor.data_dir / "yemen_acled_2019.csv"
        mock_acled_data.to_csv(acled_file, index=False)
        
        # Load with date filter
        df = processor.load_acled_data(
            start_date='2019-06-01',
            end_date='2019-08-31'
        )
        
        assert df['event_date'].min() >= pd.Timestamp('2019-06-01')
        assert df['event_date'].max() <= pd.Timestamp('2019-08-31')
    
    def test_load_acled_data_no_files(self, processor):
        """Test error when no ACLED files found."""
        with pytest.raises(FileNotFoundError):
            processor.load_acled_data()
    
    @patch('yemen_market.data.acled_processor.pd.read_parquet')
    def test_load_market_locations(self, mock_read_parquet, processor):
        """Test loading market locations."""
        # Mock WFP panel data
        mock_panel = pd.DataFrame({
            'governorate': ['Sana\'a', 'Aden', 'Taiz'],
            'market_name': ['Main', 'Port', 'Central'],
            'lat': [15.3694, 12.7855, 13.5795],
            'lon': [44.1910, 45.0187, 44.0219]
        })
        mock_read_parquet.return_value = mock_panel
        
        # Load markets
        markets = processor.load_market_locations()
        
        assert isinstance(markets, gpd.GeoDataFrame)
        assert len(markets) == 3
        assert 'market_id' in markets.columns
        assert markets.crs == 'EPSG:4326'
    
    def test_calculate_conflict_metrics(self, processor, mock_acled_data, 
                                      mock_market_locations):
        """Test conflict metric calculation."""
        # Calculate metrics
        metrics = processor.calculate_conflict_metrics(
            mock_acled_data, 
            mock_market_locations
        )
        
        # Check structure
        assert 'market_id' in metrics.columns
        assert 'year_month' in metrics.columns
        assert 'n_events' in metrics.columns
        assert 'conflict_intensity' in metrics.columns
        
        # Check derived features
        assert 'conflict_intensity_lag1' in metrics.columns
        assert 'conflict_ma3' in metrics.columns
        
        # Check values
        assert metrics['n_events'].min() >= 0
        assert metrics['conflict_intensity'].min() >= 0
    
    def test_calculate_conflict_metrics_actor_columns(self, processor, 
                                                    mock_acled_data,
                                                    mock_market_locations):
        """Test that actor-specific columns are created."""
        metrics = processor.calculate_conflict_metrics(
            mock_acled_data,
            mock_market_locations
        )
        
        # Check actor columns
        for actor in ['houthis', 'government', 'aqap']:
            assert f'n_events_{actor}' in metrics.columns
    
    def test_create_summary_stats(self, processor, mock_acled_data,
                                mock_market_locations):
        """Test summary statistics creation."""
        # Calculate metrics first
        metrics = processor.calculate_conflict_metrics(
            mock_acled_data,
            mock_market_locations
        )
        
        # Create summary
        summary = processor.create_summary_stats(metrics)
        
        assert 'statistic' in summary.columns
        assert 'value' in summary.columns
        assert len(summary) > 5
        
        # Check specific statistics
        stats = summary['statistic'].tolist()
        assert 'Total observations' in stats
        assert 'Markets covered' in stats
        assert 'Time period' in stats
    
    @patch('yemen_market.data.acled_processor.pd.read_parquet')
    def test_process_conflict_data(self, mock_read_parquet, processor, 
                                 mock_acled_data):
        """Test full processing pipeline."""
        # Save mock ACLED data
        acled_file = processor.data_dir / "yemen_acled_2019.csv"
        mock_acled_data.to_csv(acled_file, index=False)
        
        # Mock market data
        mock_panel = pd.DataFrame({
            'governorate': ['Sana\'a', 'Aden'],
            'market_name': ['Main', 'Port'],
            'lat': [15.3694, 12.7855],
            'lon': [44.1910, 45.0187]
        })
        mock_read_parquet.return_value = mock_panel
        
        # Process data
        outputs = processor.process_conflict_data()
        
        assert 'conflict_metrics' in outputs
        assert 'conflict_summary' in outputs
        assert len(outputs['conflict_metrics']) > 0
        
        # Check that files were saved
        assert (processor.output_dir / 'conflict_metrics.parquet').exists()
        assert (processor.output_dir / 'processing_metadata.json').exists()
    
    def test_save_outputs(self, processor):
        """Test saving outputs."""
        # Create mock data
        outputs = {
            'conflict_metrics': pd.DataFrame({
                'market_id': ['A', 'B'],
                'year_month': ['2024-01', '2024-01'],
                'n_events': [5, 10]
            }),
            'summary': pd.DataFrame({
                'stat': ['mean'],
                'value': [7.5]
            })
        }
        
        # Save
        processor.save_outputs(outputs)
        
        # Check files exist
        assert (processor.output_dir / 'conflict_metrics.parquet').exists()
        assert (processor.output_dir / 'conflict_metrics_sample.csv').exists()
        assert (processor.output_dir / 'processing_metadata.json').exists()
    
    def test_empty_events_handling(self, processor, mock_market_locations):
        """Test handling of empty event data."""
        # Create empty events DataFrame with required columns
        empty_events = pd.DataFrame({
            'event_date': pd.Series(dtype='datetime64[ns]'),
            'latitude': pd.Series(dtype='float64'),
            'longitude': pd.Series(dtype='float64'),
            'event_type': pd.Series(dtype='object'),
            'fatalities': pd.Series(dtype='int64'),
            'actor1': pd.Series(dtype='object'),
            'actor2': pd.Series(dtype='object')
        })
        
        # Should not raise error
        metrics = processor.calculate_conflict_metrics(
            empty_events,
            mock_market_locations
        )
        
        # Should return empty dataframe with correct columns
        assert 'n_events' in metrics.columns
        assert 'total_fatalities' in metrics.columns
        assert len(metrics) == 0  # No rows expected when no events