"""Tests for the enhanced logging utility."""

import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock, call, ANY
import sys

from yemen_market.utils.logging import LogConfig, EconometricLogger


class TestLogConfig:
    def test_initialization_defaults(self):
        """Test LogConfig initializes with default values."""
        config = LogConfig()
        assert config.log_dir == Path("logs")
        assert config.experiment_dir == Path("experiments")
        assert config.console_level == "INFO"
        assert config.file_level == "DEBUG"
        assert config.use_colors is True
        assert config.structured_format is True
        assert config.include_context is True
        assert config.rotation == "100 MB"
        assert config.retention == "30 days"
        assert config.compression == "zip"
        assert config.track_performance is True
        assert config.track_experiments is True
        assert config.enable_remote is False
        assert config.remote_url is None
        assert config.remote_api_key is None

    def test_initialization_with_custom_values(self):
        """Test LogConfig initializes with custom values."""
        custom_log_dir = Path("/tmp/custom_logs")
        custom_exp_dir = Path("/tmp/custom_exp")
        config = LogConfig(
            log_dir=custom_log_dir,
            experiment_dir=custom_exp_dir,
            console_level="DEBUG",
            file_level="WARNING",
            use_colors=False,
            structured_format=False,
            include_context=False,
            rotation="50 MB",
            retention="10 days",
            compression="gz",
            track_performance=False,
            track_experiments=False,
            enable_remote=True,
            remote_url="http://example.com",
            remote_api_key="test_key"
        )
        assert config.log_dir == custom_log_dir
        assert config.experiment_dir == custom_exp_dir
        assert config.console_level == "DEBUG"
        assert config.file_level == "WARNING"
        assert config.use_colors is False
        assert config.structured_format is False
        assert config.include_context is False
        assert config.rotation == "50 MB"
        assert config.retention == "10 days"
        assert config.compression == "gz"
        assert config.track_performance is False
        assert config.track_experiments is False
        assert config.enable_remote is True
        assert config.remote_url == "http://example.com"
        assert config.remote_api_key == "test_key"


class TestEconometricLogger:
    @pytest.fixture
    def mock_log_config(self):
        """Fixture for a mock LogConfig."""
        mock_config = MagicMock(spec=LogConfig)
        mock_config.log_dir = Path("mock_logs")
        mock_config.experiment_dir = Path("mock_experiments")
        mock_config.use_colors = True
        mock_config.console_level = "INFO"
        mock_config.file_level = "DEBUG"
        mock_config.rotation = "10 MB"
        mock_config.retention = "1 day"
        mock_config.compression = "zip"
        mock_config.structured_format = True
        mock_config.track_experiments = True # Enabled by default in LogConfig, ensure mock reflects this for handler count
        mock_config.track_performance = True # Enabled by default in LogConfig, ensure mock reflects this for handler count
        # To test scenarios where these are false, create specific configs or parametrize
        return mock_config

    def test_initialization_default_config(self):
        """Test EconometricLogger initializes with a default LogConfig."""
        with patch.object(Path, 'mkdir', autospec=True) as mock_mkdir:
            with patch('yemen_market.utils.logging.logger.remove') as mock_loguru_remove:
                with patch('yemen_market.utils.logging.logger.add') as mock_loguru_add:
                    with patch('yemen_market.utils.logging.structlog.configure') as mock_structlog_configure:

                        # Default LogConfig has track_experiments=True and track_performance=True
                        # So, expect 4 handlers: console, file, experiment, performance
                        default_config = LogConfig()
                        logger_instance = EconometricLogger(config=default_config) # Pass explicit default for clarity

                        assert isinstance(logger_instance.config, LogConfig)
                        # Check that mkdir was called for the log_dir and experiment_dir of the default_config
                        # Path.mkdir is mocked by mock_mkdir. The first argument to Path.mkdir is self (the Path instance).
                        mock_mkdir.assert_any_call(default_config.log_dir, parents=True, exist_ok=True)
                        mock_mkdir.assert_any_call(default_config.experiment_dir, parents=True, exist_ok=True)
                        assert mock_mkdir.call_count == 2 # Called for log_dir and experiment_dir

                        mock_loguru_remove.assert_called_once()
                        assert mock_loguru_add.call_count == 4
                        mock_structlog_configure.assert_called_once()

    def test_initialization_custom_config(self, mock_log_config):
        """Test EconometricLogger initializes with a custom LogConfig."""
        with patch.object(Path, 'mkdir', autospec=True) as mock_mkdir: # This will mock Path.mkdir globally
            with patch('yemen_market.utils.logging.logger.remove') as mock_loguru_remove:
                with patch('yemen_market.utils.logging.logger.add') as mock_loguru_add:
                    with patch('yemen_market.utils.logging.structlog.configure') as mock_structlog_configure:

                        # mock_log_config.log_dir and mock_log_config.experiment_dir are already Path objects from the fixture.
                        # Path.mkdir is mocked by mock_mkdir.
                        logger_instance = EconometricLogger(config=mock_log_config)
                        assert logger_instance.config == mock_log_config

                        mock_mkdir.assert_any_call(mock_log_config.log_dir, parents=True, exist_ok=True)
                        mock_mkdir.assert_any_call(mock_log_config.experiment_dir, parents=True, exist_ok=True)
                        assert mock_mkdir.call_count == 2

                        mock_loguru_remove.assert_called_once()
                        # mock_log_config has track_experiments=True and track_performance=True by default in fixture
                        assert mock_loguru_add.call_count == 4
                        mock_structlog_configure.assert_called_once()


    def test_setup_directories(self, mock_log_config):
        """Test _setup_directories creates log and experiment directories."""
        # We need to patch Path.mkdir globally for this test, as _setup_directories
        # is called from __init__.
        with patch.object(Path, 'mkdir', autospec=True) as mock_mkdir_method_on_path_class:
            # Instantiate the logger; __init__ will call _setup_directories
            logger_instance = EconometricLogger(config=mock_log_config)

            # Check that the global Path.mkdir (mock_mkdir_method_on_path_class)
            # was called with the Path instances from mock_log_config.
            # The first argument to mkdir (the 'self' of the Path instance) is part of the call.
            calls = [
                call(mock_log_config.log_dir, parents=True, exist_ok=True),
                call(mock_log_config.experiment_dir, parents=True, exist_ok=True)
            ]
            # Use assert_has_calls with any_order=True because the order of creating
            # log_dir vs experiment_dir might not be guaranteed if _setup_directories changes internally.
            mock_mkdir_method_on_path_class.assert_has_calls(calls, any_order=True)
            assert mock_mkdir_method_on_path_class.call_count == 2


    @pytest.mark.parametrize(
        "use_colors, track_experiments, track_performance, console_level, file_level, structured_format",
        [
            (True, True, True, "INFO", "DEBUG", True),
            (False, True, True, "WARNING", "ERROR", False),
            (True, False, True, "DEBUG", "INFO", True),
            (True, True, False, "CRITICAL", "WARNING", False),
            (False, False, False, "INFO", "DEBUG", True),
        ]
    )
    def test_configure_loguru_handlers_detailed(
        self,
        use_colors, track_experiments, track_performance,
        console_level, file_level, structured_format,
        tmp_path # Use pytest's tmp_path for log_dir
    ):
        """Test that Loguru handlers are added correctly based on LogConfig."""
        test_log_dir = tmp_path / "logs"
        test_exp_dir = tmp_path / "experiments"

        config = LogConfig(
            log_dir=test_log_dir,
            experiment_dir=test_exp_dir,
            use_colors=use_colors,
            track_experiments=track_experiments,
            track_performance=track_performance,
            console_level=console_level,
            file_level=file_level,
            structured_format=structured_format,
            rotation="1MB", # Smaller for test
            retention="1 day" # Shorter for test
        )

        with patch('yemen_market.utils.logging.logger.remove') as mock_loguru_remove, \
             patch('yemen_market.utils.logging.logger.add') as mock_loguru_add, \
             patch('yemen_market.utils.logging.RichHandler', autospec=True) as mock_rich_handler_class, \
             patch('yemen_market.utils.logging.console', autospec=True) as mock_rich_console_instance, \
             patch.object(Path, 'mkdir', autospec=True) as mock_path_mkdir: # Already tested, just to prevent actual creation

            logger_instance = EconometricLogger(config=config)

            mock_loguru_remove.assert_called_once()

            expected_handlers = 2  # Console + File are always there
            if track_experiments:
                expected_handlers += 1
            if track_performance:
                expected_handlers += 1
            assert mock_loguru_add.call_count == expected_handlers

            # --- Assertions for each handler ---
            actual_calls = mock_loguru_add.call_args_list

            # Console Handler
            console_handler_call = None
            if use_colors:
                for c in actual_calls:
                    # RichHandler is passed as the first positional arg (sink)
                    if hasattr(c.args[0], '__class__') and c.args[0].__class__.__name__ == 'RichHandler':
                        console_handler_call = c
                        break
                assert console_handler_call is not None, "RichConsole handler not found"
                sink_instance = console_handler_call.args[0]
                mock_rich_handler_class.assert_called_once_with(console=mock_rich_console_instance, rich_tracebacks=True)
                assert console_handler_call.kwargs['format'] == "{message}"
                assert console_handler_call.kwargs['level'] == console_level
                assert console_handler_call.kwargs['filter'] == logger_instance._console_filter
            else:
                for c in actual_calls:
                    if c.args and c.args[0] == sys.stderr:
                        console_handler_call = c
                        break
                assert console_handler_call is not None, "Standard Error console handler not found"
                assert console_handler_call.kwargs['format'] == "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
                assert console_handler_call.kwargs['level'] == console_level

            # File Handler
            file_handler_call = None
            expected_log_file_start = str(test_log_dir / "yemen_market_")
            for c in actual_calls:
                if isinstance(c.args[0], str) and c.args[0].startswith(expected_log_file_start):
                    file_handler_call = c
                    break
            assert file_handler_call is not None, "File handler not found"
            assert file_handler_call.args[0].endswith(".log")
            assert file_handler_call.kwargs['format'] == "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {extra} | {message}"
            assert file_handler_call.kwargs['level'] == file_level
            assert file_handler_call.kwargs['rotation'] == config.rotation
            assert file_handler_call.kwargs['retention'] == config.retention
            assert file_handler_call.kwargs['compression'] == config.compression
            assert file_handler_call.kwargs['serialize'] == structured_format
            assert file_handler_call.kwargs['enqueue'] is True

            # Experiment Handler
            if track_experiments:
                exp_handler_call = None
                for c in actual_calls:
                    if c.args and c.args[0] == logger_instance._experiment_sink:
                        exp_handler_call = c
                        break
                assert exp_handler_call is not None, "Experiment handler not found"
                assert exp_handler_call.kwargs['serialize'] is True
                assert callable(exp_handler_call.kwargs['filter'])
                # Test filter behavior (INFO level and above)
                filter_func = exp_handler_call.kwargs['filter']
                with patch('yemen_market.utils.logging.logger.level') as mock_loguru_level_method:
                    mock_loguru_level_method.return_value.no = 20 # Assume INFO is 20
                    assert filter_func({'level': MagicMock(no=20)}) is True
                    assert filter_func({'level': MagicMock(no=10)}) is False # Assume DEBUG (10) < INFO (20)
                    mock_loguru_level_method.assert_called_with("INFO")

            # Performance Handler
            if track_performance:
                perf_handler_call = None
                expected_perf_file = str(test_log_dir / "performance.jsonl")
                for c in actual_calls:
                    if c.args and c.args[0] == expected_perf_file:
                        perf_handler_call = c
                        break
                assert perf_handler_call is not None, "Performance handler not found"
                assert perf_handler_call.kwargs['serialize'] is True
                assert callable(perf_handler_call.kwargs['filter'])
                # Test filter behavior
                filter_func = perf_handler_call.kwargs['filter']
                assert filter_func({'extra': {'performance': True}}) is True
                assert filter_func({'extra': {}}) is False

    # Further tests will go here
