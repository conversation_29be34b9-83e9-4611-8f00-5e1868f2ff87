"""Unit tests for panel dataset builder."""

import json
from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock, patch

import numpy as np
import pandas as pd
import pytest

from yemen_market.data.panel_builder import PanelBuilder


class TestPanelBuilder:
    """Test suite for panel dataset builder."""
    
    @pytest.fixture
    def builder(self, tmp_path):
        """Create PanelBuilder instance with temp directories."""
        return PanelBuilder(
            data_dir=tmp_path / "processed",
            output_dir=tmp_path / "panels",
            commodities=['Wheat', 'Rice'],
            frequency='M'
        )
    
    @pytest.fixture
    def sample_price_data(self):
        """Create sample WFP price data."""
        dates = pd.date_range('2024-01-01', periods=6, freq='ME')
        data = []
        
        for date in dates:
            for market in ['Market_A', 'Market_B']:
                for commodity in ['Wheat', 'Rice']:
                    data.append({
                        'date': date,
                        'market_id': market,
                        'market_name': market.replace('_', ' '),
                        'governorate': 'Sana\'a' if market == 'Market_A' else 'Aden',
                        'district': 'District1' if market == 'Market_A' else 'District2',
                        'commodity': commodity,
                        'price_usd': np.random.uniform(5, 15),
                        'lat': 15.0 if market == 'Market_A' else 12.0,
                        'lon': 44.0 if market == 'Market_A' else 45.0
                    })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def sample_exchange_data(self):
        """Create sample exchange rate data."""
        dates = pd.date_range('2024-01-01', periods=6, freq='ME')
        data = []
        
        for date in dates:
            for market in ['Market_A', 'Market_B']:
                parallel_rate = 500 if market == 'Market_A' else 600
                data.append({
                    'date': date,
                    'market_id': market,
                    'official_rate': 250,
                    'parallel_rate': parallel_rate,
                    'exchange_rate': parallel_rate  # Primary rate for analysis
                })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def sample_market_zones(self):
        """Create sample market-zone mapping."""
        dates = pd.date_range('2024-01-01', periods=6, freq='ME')
        data = []
        
        for date in dates:
            data.extend([
                {
                    'market_id': 'Market_A',
                    'date': date,
                    'control_zone': 'houthi',
                    'market_governorate': 'Sana\'a',
                    'market_district': 'District1',
                    'zone_changed': False
                },
                {
                    'market_id': 'Market_B',
                    'date': date,
                    'control_zone': 'government' if date < dates[3] else 'contested',
                    'market_governorate': 'Aden', 
                    'market_district': 'District2',
                    'zone_changed': date == dates[3]
                }
            ])
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def setup_test_data(self, tmp_path, sample_price_data, 
                       sample_exchange_data, sample_market_zones):
        """Set up test data files."""
        # Create directory structure
        processed_dir = tmp_path / "processed"
        wfp_dir = processed_dir / "wfp"
        spatial_dir = processed_dir / "spatial"
        interim_dir = tmp_path / "interim"
        wfp_dir.mkdir(parents=True)
        spatial_dir.mkdir(parents=True)
        interim_dir.mkdir(parents=True)
        
        # Save test data with correct names
        # Save price data in the main processed directory as expected by PanelBuilder
        sample_price_data.to_parquet(processed_dir / "wfp_commodity_prices.parquet")
        # Save exchange rates in interim directory (first location checked)
        sample_exchange_data.to_parquet(interim_dir / "exchange_rates.parquet")
        # Save market zones in the spatial directory
        sample_market_zones.to_parquet(spatial_dir / "market_zones_temporal.parquet")
        
        return {
            'prices': sample_price_data,
            'exchange_rates': sample_exchange_data,
            'market_zones': sample_market_zones
        }
    
    def test_init(self, builder):
        """Test PanelBuilder initialization."""
        assert builder.commodities == ['Wheat', 'Rice']
        assert builder.frequency == 'M'
        assert builder.output_dir.exists()
    
    def test_load_component_data(self, builder, setup_test_data):
        """Test loading component datasets."""
        data = builder.load_component_data()
        
        assert 'prices' in data
        assert 'exchange_rates' in data
        assert 'market_zones' in data
        
        assert len(data['prices']) == len(setup_test_data['prices'])
        assert len(data['exchange_rates']) == len(setup_test_data['exchange_rates'])
    
    def test_load_component_data_missing_files(self, builder):
        """Test handling missing data files."""
        with pytest.raises(FileNotFoundError):
            builder.load_component_data()
    
    def test_create_price_panel(self, builder, setup_test_data):
        """Test creating price panel dataset."""
        data = {
            'prices': setup_test_data['prices'],
            'market_zones': setup_test_data['market_zones']
        }
        
        panel = builder.create_price_panel(data)
        
        assert not panel.empty
        assert 'control_zone' in panel.columns
        assert 'year_month' in panel.columns
        assert panel['commodity'].isin(['Wheat', 'Rice']).all()
        
        # Check merge worked
        assert panel['control_zone'].notna().sum() > 0
    
    def test_create_price_panel_no_zones(self, builder, setup_test_data):
        """Test price panel without market zones."""
        data = {'prices': setup_test_data['prices']}
        
        panel = builder.create_price_panel(data)
        
        assert not panel.empty
        assert 'control_zone' not in panel.columns
    
    def test_create_exchange_rate_panel(self, builder, setup_test_data):
        """Test creating exchange rate panel."""
        data = {
            'exchange_rates': setup_test_data['exchange_rates'],
            'market_zones': setup_test_data['market_zones']
        }
        
        panel = builder.create_exchange_rate_panel(data)
        
        assert not panel.empty
        assert 'control_zone' in panel.columns
        assert 'zone_exchange_rate' in panel.columns
        assert 'rate_differential' in panel.columns
        assert 'rate_differential_pct' in panel.columns
    
    def test_create_exchange_rate_panel_no_data(self, builder):
        """Test exchange panel with no exchange data."""
        data = {}
        panel = builder.create_exchange_rate_panel(data)
        assert panel.empty
    
    def test_add_temporal_features(self, builder, setup_test_data):
        """Test adding temporal features."""
        panel = setup_test_data['prices'].copy()
        panel['control_zone'] = 'houthi'
        # Add month column that temporal features expects
        panel['month'] = panel['date'].dt.month
        
        result = builder.add_temporal_features(panel)
        
        # Check lag features
        assert 'price_usd_lag1' in result.columns
        assert 'price_usd_lag2' in result.columns
        assert 'price_usd_lag3' in result.columns
        
        # Check differences
        assert 'price_usd_diff' in result.columns
        assert 'price_usd_pct_change' in result.columns
        
        # Check rolling stats
        assert 'price_usd_ma3' in result.columns
        assert 'price_usd_std3' in result.columns
        
        # Check other features
        assert 'time_trend' in result.columns
        assert 'quarter' in result.columns
        assert 'is_ramadan' in result.columns
        assert 'is_contested' in result.columns
    
    def test_create_balanced_panel(self, builder):
        """Test creating balanced panel."""
        # Create unbalanced data (missing some dates)
        dates = pd.date_range('2024-01-01', periods=6, freq='ME')
        data = []
        
        # Market A has all dates
        for date in dates:
            data.append({
                'date': date,
                'market_id': 'Market_A',
                'commodity': 'Wheat',
                'price_usd': 10.0
            })
        
        # Market B missing some dates
        for date in dates[::2]:  # Every other date
            data.append({
                'date': date,
                'market_id': 'Market_B',
                'commodity': 'Wheat',
                'price_usd': 11.0
            })
        
        panel = pd.DataFrame(data)
        
        # Create balanced panel
        balanced = builder.create_balanced_panel(panel, min_obs_per_entity=3)
        
        # Check structure
        assert len(balanced) > len(panel)  # Should have filled missing
        
        # Check all dates present for included entities
        market_dates = balanced.groupby('market_id')['date'].count()
        assert (market_dates >= 3).all()  # All have minimum observations
    
    def test_handle_missing_data(self, builder):
        """Test missing data handling."""
        # Create data with missing values
        panel = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=6, freq='ME'),
            'market_id': 'Market_A',
            'commodity': 'Wheat',
            'price_usd': [10, np.nan, np.nan, 12, np.nan, 14],
            'parallel_rate': [500, np.nan, 510, np.nan, np.nan, 520],
            'control_zone': ['houthi', np.nan, 'houthi', 'houthi', np.nan, 'houthi'],
            'year_month': pd.date_range('2024-01-01', periods=6, freq='ME').to_period('M')
        })
        
        # Handle missing data
        result = builder.handle_missing_data(panel)
        
        # Check interpolation worked
        assert result['price_usd'].isnull().sum() <= panel['price_usd'].isnull().sum()
        
        # Check forward fill worked
        assert result['parallel_rate'].isnull().sum() <= panel['parallel_rate'].isnull().sum()
        assert result['control_zone'].isnull().sum() == 0
    
    def test_create_model_specific_panels(self, builder):
        """Test creating model-specific panels."""
        # Create base panel with all features
        base_panel = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=6, freq='ME'),
            'market_id': 'Market_A',
            'commodity': 'Wheat',
            'price_usd': np.random.uniform(10, 15, 6),
            'price_usd_lag1': np.random.uniform(10, 15, 6),
            'price_usd_diff': np.random.uniform(-1, 1, 6),
            'parallel_rate': np.random.uniform(500, 600, 6),
            'official_rate': 250,
            'control_zone': 'houthi',
            'is_contested': False,
            'time_trend': range(1, 7),
            'lat': 15.0,
            'lon': 44.0,
            'year_month': pd.date_range('2024-01-01', periods=6, freq='ME').to_period('M')
        })
        
        panels = builder.create_model_specific_panels(base_panel)
        
        assert 'price_transmission' in panels
        assert 'exchange_passthrough' in panels
        assert 'threshold_coint' in panels
        assert 'spatial' in panels
        
        # Check each panel has appropriate columns
        assert 'price_usd' in panels['price_transmission'].columns
        assert 'parallel_rate' in panels['exchange_passthrough'].columns
        assert 'time_trend' in panels['threshold_coint'].columns
        assert 'lat' in panels['spatial'].columns
    
    def test_save_panels(self, builder, tmp_path):
        """Test saving panel datasets."""
        # Create test panels
        panels = {
            'main': pd.DataFrame({
                'date': pd.date_range('2024-01-01', periods=3, freq='ME'),
                'market_id': 'Market_A',
                'price_usd': [10, 11, 12]
            }),
            'spatial': pd.DataFrame({
                'date': pd.date_range('2024-01-01', periods=3, freq='ME'),
                'market_id': 'Market_A',
                'lat': 15.0,
                'lon': 44.0
            })
        }
        
        # Save panels
        saved = builder.save_panels(panels)
        
        assert 'main_parquet' in saved
        assert 'main_csv' in saved
        assert 'spatial_parquet' in saved
        assert 'metadata' in saved
        
        # Check files exist
        assert all(path.exists() for path in saved.values())
        
        # Check metadata
        with open(saved['metadata']) as f:
            metadata = json.load(f)
        
        assert 'panels' in metadata
        assert metadata['panels'] == ['main', 'spatial']
        assert metadata['frequency'] == 'M'
    
    def test_save_panels_empty(self, builder):
        """Test saving empty panels."""
        panels = {'empty': pd.DataFrame()}
        
        saved = builder.save_panels(panels)
        
        # Should only save metadata
        assert 'metadata' in saved
        assert 'empty_parquet' not in saved
    
    def test_generate_panel_summary(self, builder):
        """Test generating panel summary."""
        panels = {
            'main': pd.DataFrame({
                'date': pd.date_range('2024-01-01', periods=6, freq='ME'),
                'market_id': ['Market_A'] * 3 + ['Market_B'] * 3,
                'commodity': 'Wheat',
                'price_usd': [10, 11, np.nan, 12, 13, 14],
                'control_zone': ['houthi', 'houthi', 'government', 
                               'government', 'contested', 'contested']
            }),
            'empty': pd.DataFrame()
        }
        
        summary = builder.generate_panel_summary(panels)
        
        assert not summary.empty
        assert len(summary) == 1  # Only non-empty panel
        
        # Check summary contents
        main_summary = summary[summary['panel'] == 'main'].iloc[0]
        assert main_summary['n_observations'] == 6
        assert main_summary['n_markets'] == 2
        assert main_summary['n_commodities'] == 1
        assert main_summary['missing_price_pct'] > 0
        assert main_summary['n_control_zones'] == 3
    
    def test_integration_workflow(self, builder, setup_test_data):
        """Test complete panel building workflow."""
        # Load data
        data = builder.load_component_data()
        assert len(data) > 0
        
        # Create price panel
        price_panel = builder.create_price_panel(data)
        assert not price_panel.empty
        
        # Create exchange panel
        exchange_panel = builder.create_exchange_rate_panel(data)
        assert not exchange_panel.empty
        
        # Add temporal features
        price_panel = builder.add_temporal_features(price_panel)
        assert 'price_usd_lag1' in price_panel.columns
        
        # Create balanced panel
        balanced = builder.create_balanced_panel(price_panel, min_obs_per_entity=3)
        assert not balanced.empty
        
        # Handle missing data
        clean_panel = builder.handle_missing_data(balanced)
        assert clean_panel['price_usd'].isnull().sum() <= balanced['price_usd'].isnull().sum()
        
        # Create model panels
        model_panels = builder.create_model_specific_panels(clean_panel)
        assert len(model_panels) > 0
        
        # Save everything
        all_panels = {'integrated': clean_panel}
        all_panels.update(model_panels)
        saved = builder.save_panels(all_panels)
        assert len(saved) > 0
        
        # Generate summary
        summary = builder.generate_panel_summary(all_panels)
        assert not summary.empty