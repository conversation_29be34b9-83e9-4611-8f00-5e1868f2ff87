# Yemen Market Integration Model Configuration
# This file centralizes all model parameters to avoid hardcoded values

# Conflict Validation Configuration
conflict_validation:
  # Conflict intensity thresholds
  conflict_threshold: 10              # Events/fatalities per period for high conflict
  major_conflict_multiplier: 5        # Multiplier for major events
  near_threshold_distance: 10         # Distance for "near threshold" classification
  
  # Time windows
  window_before: 30                   # Days before event
  window_after: 30                    # Days after event
  min_obs_structural_break: 20        # Min observations on each side of break
  
  # Spatial analysis
  spatial_cutoff_km: 100             # Distance cutoff for spatial weights (km)
  co_occurrence_threshold: 0.1        # For conflict clustering (10% of weeks)
  
  # Statistical thresholds
  significance_level: 0.05            # P-value threshold
  min_obs_granger: 30                # Min observations for Granger causality
  min_markets_affected: 3             # Min markets for spatial analysis
  
  # Panel data requirements  
  min_markets: 5                      # Minimum markets for analysis
  min_time_periods: 50               # Minimum time periods

# Panel Builder Configuration
panel_builder:
  # Time series requirements
  min_observations_transmission: 20   # Min obs for price transmission pairs
  min_train_size: 60                 # Minimum training size for models
  
  # Date handling
  monthly_day: 15                    # Day of month for monthly aggregation
  
  # Commodity lists
  tradable_commodities:
    - Wheat
    - Rice
    - Sugar
    - Cooking oil
  
  perishable_commodities:
    - Tomatoes
    - Onions
    - Potatoes
  
  # Exchange rate analysis
  outlier_iqr_multiplier: 1.5        # IQR multiplier for outlier detection
  
  # Threshold analysis
  max_lags_vecm: 4                   # Maximum lags for VECM specification
  volatility_window: 12              # Window for price volatility calculation
  volatility_min_periods: 6          # Minimum periods for volatility

# Three-Tier Model Configuration
three_tier:
  # Data validation settings (NEW)
  data_validation:
    strict_mode: false              # Relaxed for policy analysis
    world_bank_mode: true          # Enable World Bank standards
    outlier_method: 'winsorize'    # Don't drop outliers
    winsorize_limits: [0.01, 0.99] # 1% and 99% percentiles
    max_missing_pct: 0.30          # Allow up to 30% missing
    min_price: 0.01                # Avoid log(0) issues
    max_price: 1000000             # Very high for hyperinflation
    
  # Tier 1 - Pooled Panel
  tier1:
    include_entity_effects: true
    include_time_effects: true
    cluster_var: entity
    cluster_se: 'twoway'           # NEW: Two-way clustering
    min_obs_per_entity: 10
    dependent_var: 'log_price'     # NEW: Use log prices
    control_vars:                  # NEW: Explicit control variables
      - 'events_total'
      - 'events_total_lag1'
      - 'high_conflict'
      - 'control_zone_DFA'
      - 'time_trend'
    robust_se: true                # NEW: Robust standard errors
    
  # Tier 2 - Commodity Specific
  tier2:
    min_observations: 50
    test_thresholds: true
    max_iterations: 1000
    convergence_tol: 0.0001
    commodities:                   # NEW: Priority commodities
      - 'Wheat'
      - 'Rice (Imported)'
      - 'Sugar'
      - 'Fuel (Diesel)'
    min_obs_per_regime: 20         # NEW: Reduced for conflict data
    test_cointegration: true       # NEW: Pre-test requirement
    threshold_search:              # NEW: Search parameters
      trim_pct: 0.15
      grid_points: 100
    
  # Tier 3 - Validation
  tier3:
    n_factors: null                # NEW: Auto-select
    min_variance_explained: 0.70   # NEW: Reduced to 70%
    conflict_validation: true
    standardize: true              # NEW: Standardization flag
    method: 'pca'                  # NEW: Method specification
    rotation: 'varimax'            # NEW: Factor rotation

# Diagnostic Configuration
diagnostics:
  run_all_tests: true
  critical_tests:
    - wooldridge         # Serial correlation
    - pesaran_cd         # Cross-sectional dependence
    - modified_wald      # Heteroskedasticity
    - ips                # Unit roots
  
  fail_on_critical: false
  apply_corrections: true
  auto_correct: true             # NEW: Automatic correction flag
  
  # Test-specific parameters
  wooldridge:
    lags: 1
  
  pesaran_cd:
    standardize: true
    
  ips:
    trend: constant
    max_lags: 4

# Standard Error Corrections
standard_errors:
  driscoll_kraay:
    kernel: bartlett
    bandwidth: auto
    
  newey_west:
    kernel: bartlett
    lags: auto
    
  cluster_robust:
    cluster_var: entity

# Model Comparison Configuration  
model_comparison:
  # Regime agreement
  near_threshold_distance: 10         # Distance from threshold for "near" classification
  
  # Cross-validation
  cv_n_splits: 5
  cv_test_size: 12
  cv_min_train_size: 60
  
  # Ensemble methods
  ensemble_method: inverse_ic        # Options: inverse_ic, softmax, equal
  ensemble_metric: aic              # Options: aic, bic, forecast_rmse

# Analysis Configuration (NEW)
analysis:
  commodities:                       # Core commodities for balanced panel
    - Wheat
    - Wheat Flour
    - Rice (Imported)
    - Sugar
    - Oil (Vegetable)
    - Beans (Kidney Red)
    - Beans (White)
    - Lentils
    - Peas (Yellow, Split)
    - Eggs
    - Onions
    - Tomatoes
    - Potatoes
    - Fuel (Diesel)
    - Fuel (Gas)
    - Fuel (Petrol-Gasoline)
  
  markets:                          # Core markets for balanced panel
    - Aden
    - Sana'a
    - Taiz
    - Al Hudaydah
    - Ibb
    - Dhamar
    - Hajjah
    - Amran
    - Sa'ada
    - Al Mahwit
    - Raymah
    - Al Bayda
    - Shabwah
    - Marib
    - Al Jawf
    - Al Dhale'e
    - Lahj
    - Abyan
    - Hadramaut
    - Al Maharah
    - Socotra
  
  time_period:
    start: "2019-01-01"
    end: "2025-03-15"