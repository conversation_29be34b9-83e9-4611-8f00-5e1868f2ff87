# Production Logging Configuration for Yemen Market Integration
# This configuration ensures comprehensive operational visibility across all components

version: 1
disable_existing_loggers: false

formatters:
  detailed:
    format: '%(asctime)s | %(levelname)-8s | %(name)-30s | %(funcName)-20s | %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  simple:
    format: '%(levelname)-8s | %(name)-20s | %(message)s'
  
  json:
    format: '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "function": "%(funcName)s", "message": "%(message)s", "module": "%(module)s", "lineno": %(lineno)d}'
    datefmt: '%Y-%m-%dT%H:%M:%S'
  
  performance:
    format: '%(asctime)s | PERF | %(name)-30s | %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: detailed
    stream: ext://sys.stdout
  
  file_debug:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/yemen_market_debug.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_info:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/yemen_market_info.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8
  
  file_error:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/yemen_market_error.log
    maxBytes: 10485760  # 10MB
    backupCount: 20
    encoding: utf8
  
  file_performance:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: performance
    filename: logs/yemen_market_performance.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_json:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: logs/yemen_market_structured.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

loggers:
  # Root logger
  '':
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # Yemen Market specific loggers
  yemen_market:
    level: DEBUG
    handlers: [console, file_debug, file_info, file_error, file_json]
    propagate: false
  
  yemen_market.models.three_tier:
    level: DEBUG
    handlers: [console, file_debug, file_info, file_performance]
    propagate: false
  
  yemen_market.data:
    level: DEBUG
    handlers: [console, file_debug, file_info]
    propagate: false
  
  yemen_market.utils.logging:
    level: INFO
    handlers: [console, file_info]
    propagate: false
  
  # Script loggers
  scripts:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  scripts.analysis:
    level: DEBUG
    handlers: [console, file_debug, file_info, file_performance]
    propagate: false
  
  scripts.data_processing:
    level: DEBUG
    handlers: [console, file_debug, file_info]
    propagate: false
  
  # External library loggers (reduce noise)
  matplotlib:
    level: WARNING
    handlers: [file_error]
    propagate: false
  
  urllib3:
    level: WARNING
    handlers: [file_error]
    propagate: false
  
  requests:
    level: WARNING
    handlers: [file_error]
    propagate: false
  
  statsmodels:
    level: WARNING
    handlers: [file_error]
    propagate: false
  
  pandas:
    level: WARNING
    handlers: [file_error]
    propagate: false

# Performance monitoring configuration
performance:
  enable_timing: true
  enable_memory_tracking: true
  slow_query_threshold: 5.0  # seconds
  memory_threshold: 100  # MB
  
# Data logging configuration
data_logging:
  log_data_shapes: true
  log_data_samples: false  # Set to true for debugging
  max_sample_rows: 5
  
# Error handling configuration
error_handling:
  capture_stack_traces: true
  log_full_exceptions: true
  enable_error_notifications: false  # Set to true for production alerts
  
# Development vs Production settings
development:
  console_level: DEBUG
  file_level: DEBUG
  enable_verbose_logging: true
  
production:
  console_level: INFO
  file_level: INFO
  enable_verbose_logging: false
  enable_structured_logging: true
  enable_performance_monitoring: true
