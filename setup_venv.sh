#!/bin/bash
# Setup script for Yemen Market Integration virtual environment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Yemen Market Integration - Virtual Environment Setup${NC}"
echo "======================================================="

# Function to check Python version
check_python_version() {
    local python_cmd=$1
    local version=$($python_cmd --version 2>&1 | cut -d' ' -f2)
    echo "$version"
}

# Find the best Python version (prefer 3.11, then 3.10, then 3.12)
PYTHON_CMD=""
for py_version in python3.11 python3.10 python3.12 python3; do
    if command -v $py_version &> /dev/null; then
        version=$(check_python_version $py_version)
        echo -e "${YELLOW}Found $py_version: version $version${NC}"
        
        # Prefer Python 3.11 or 3.10
        if [[ "$version" == 3.11.* ]] || [[ "$version" == 3.10.* ]]; then
            PYTHON_CMD=$py_version
            break
        elif [[ "$version" == 3.12.* ]] && [[ -z "$PYTHON_CMD" ]]; then
            PYTHON_CMD=$py_version
        elif [[ -z "$PYTHON_CMD" ]]; then
            PYTHON_CMD=$py_version
        fi
    fi
done

if [[ -z "$PYTHON_CMD" ]]; then
    echo -e "${RED}Error: No suitable Python version found.${NC}"
    echo -e "${YELLOW}Please install Python 3.11 or 3.10 for best compatibility.${NC}"
    echo -e "${YELLOW}Installation instructions:${NC}"
    echo -e "  macOS: brew install python@3.11"
    echo -e "  Ubuntu: sudo apt install python3.11 python3.11-venv"
    exit 1
fi

PYTHON_VERSION=$(check_python_version $PYTHON_CMD)
echo -e "${GREEN}Selected Python: $PYTHON_CMD (version $PYTHON_VERSION)${NC}"

# Warn about Python 3.13
if [[ "$PYTHON_VERSION" == 3.13.* ]]; then
    echo -e "${RED}Error: Python 3.13 is not compatible with current dependencies.${NC}"
    echo -e "${YELLOW}NumPy/pandas compatibility issues prevent using Python 3.13.${NC}"
    echo -e "${YELLOW}Please install Python 3.11:${NC}"
    echo -e "  macOS: brew install python@3.11"
    echo -e "  Ubuntu: sudo apt install python3.11 python3.11-venv"
    exit 1
fi

# Remove existing virtual environments
echo -e "\n${YELLOW}Cleaning up existing virtual environments...${NC}"
if [ -d "venv" ]; then
    rm -rf venv
    echo "Removed old venv directory"
fi
if [ -d ".venv" ]; then
    rm -rf .venv
    echo "Removed old .venv directory"
fi

# Create new virtual environment
echo -e "\n${GREEN}Creating new virtual environment with $PYTHON_CMD...${NC}"
$PYTHON_CMD -m venv venv

# Activate virtual environment
echo -e "\n${GREEN}Activating virtual environment...${NC}"
source venv/bin/activate

# Upgrade pip, setuptools, and wheel
echo -e "\n${GREEN}Upgrading pip, setuptools, and wheel...${NC}"
pip install --upgrade pip setuptools wheel

# Install NumPy first with compatible version for pandas
echo -e "\n${GREEN}Installing NumPy with compatible version...${NC}"
if [[ "$PYTHON_VERSION" == 3.12.* ]] || [[ "$PYTHON_VERSION" == 3.13.* ]]; then
    # For Python 3.12+, use specific NumPy version
    pip install "numpy<2.0"
else
    pip install numpy
fi

# Install dependencies
echo -e "\n${GREEN}Installing dependencies from requirements.txt...${NC}"
pip install -r requirements.txt

# Install additional geospatial dependencies
echo -e "\n${GREEN}Installing additional geospatial dependencies...${NC}"
pip install geopandas

# Install project in editable mode
echo -e "\n${GREEN}Installing project in editable mode...${NC}"
pip install -e .

# Install development dependencies
echo -e "\n${GREEN}Installing development dependencies...${NC}"
pip install -e ".[dev]" 2>/dev/null || echo -e "${YELLOW}No dev extras found, continuing...${NC}"

# Install Jupyter kernel
echo -e "\n${GREEN}Setting up Jupyter kernel...${NC}"
python -m ipykernel install --user --name yemen-market --display-name "Yemen Market Integration"

# Verify installation
echo -e "\n${GREEN}Verifying installation...${NC}"
python -c "
import sys
print(f'✓ Python version: {sys.version.split()[0]}')
try:
    import numpy as np
    print(f'✓ NumPy version: {np.__version__}')
except Exception as e:
    print(f'✗ NumPy import failed: {e}')
try:
    import pandas as pd
    print(f'✓ pandas version: {pd.__version__}')
except Exception as e:
    print(f'✗ pandas import failed: {e}')
try:
    import geopandas as gpd
    print(f'✓ geopandas version: {gpd.__version__}')
except Exception as e:
    print(f'✗ geopandas import failed: {e}')
try:
    import yemen_market
    print(f'✓ yemen_market imported successfully')
except Exception as e:
    print(f'✗ yemen_market import failed: {e}')
"

# Run a quick test to check if tests can be collected
echo -e "\n${GREEN}Checking if tests can be collected...${NC}"
python -m pytest --collect-only -q 2>/dev/null | head -5 || echo -e "${YELLOW}Test collection check skipped${NC}"

echo -e "\n${GREEN}Setup complete!${NC}"
echo -e "${YELLOW}To activate the environment in the future, run:${NC}"
echo -e "  source venv/bin/activate"
echo -e "\n${YELLOW}To run tests:${NC}"
echo -e "  make test-models-quick    # Quick model tests"
echo -e "  make test                 # Full test suite"
echo -e "\n${YELLOW}To use in Jupyter, select kernel:${NC}"
echo -e "  'Yemen Market Integration'"