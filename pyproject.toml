[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "yemen-market-integration"
version = "1.0.0"
description = "Econometric analysis of market integration in conflict-affected Yemen"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.10",
    "Topic :: Scientific/Engineering :: Economic Analysis",
]

dependencies = [
    # Core data packages
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scipy>=1.10.0",
    "pyarrow>=12.0.0",

    # Statistical modeling
    "statsmodels>=0.14.0",
    "linearmodels>=5.0",
    "arch>=6.0",
    "scikit-learn>=1.3.0",

    # Bayesian modeling
    "pymc>=5.10.0",
    "arviz>=0.17.0",
    "pytensor>=2.18.0",

    # Geospatial packages
    "geopandas>=0.13.0",
    "shapely>=2.0.0",
    "fiona>=1.9.0",
    "pyproj>=3.5.0",
    "libpysal>=4.7.0",
    "spreg>=1.3.0",

    # Visualization
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.14.0",

    # Data access
    "hdx-python-api>=6.0.0",
    "requests>=2.28.0",
    "python-dotenv>=1.0.0",

    # Analysis tools
    "networkx>=3.1",
    "shap>=0.42.0",
    "joblib>=1.3.0",

    # Logging dependencies
    "loguru>=0.7.0",
    "structlog>=24.0.0",
    "rich>=13.0.0",
    "python-json-logger>=2.0.0",

    # Jupyter
    "jupyter>=1.0.0",
    "ipykernel>=6.25.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.3.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "black>=23.3.0",
    "ruff>=0.0.270",
    "mypy>=1.3.0",
    "pre-commit>=3.3.0",
    "jupyter>=1.0.0",
    "nbconvert>=7.4.0",
    "sphinx>=6.2.0",
]

experiment = [
    "mlflow>=2.10.0",
    "neptune>=1.8.0",
    "wandb>=0.16.0",
]

ml = [
    "tensorflow>=2.12.0",
    "torch>=2.0.0",
]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.ruff]
select = ["E", "F", "I", "N", "UP", "PL", "NPY"]
line-length = 88
target-version = "py310"

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = "-v --cov=yemen_market --cov-report=html --tb=short"
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
minversion = "7.0"

# Comprehensive warning filters to suppress external dependency warnings
filterwarnings = [
    # Ignore all DeprecationWarnings from external packages
    "ignore::DeprecationWarning:pkg_resources.*",
    "ignore::DeprecationWarning:jsonschema.*",
    "ignore::DeprecationWarning:pydantic.*",
    "ignore::DeprecationWarning:geopandas.*",
    "ignore::DeprecationWarning:swagger_spec_validator.*",
    "ignore::DeprecationWarning:pyproj.*",
    "ignore::DeprecationWarning:fiona.*",
    "ignore::DeprecationWarning:shapely.*",

    # Ignore specific pandas FutureWarnings that we can't control
    "ignore:.*Setting an item of incompatible dtype.*:FutureWarning:pandas.*",
    "ignore:.*The default dtype for empty Series.*:FutureWarning:pandas.*",
    "ignore:.*Passing a BlockManager.*:FutureWarning:pandas.*",

    # Ignore UserWarnings from external packages
    "ignore::UserWarning:geopandas.*",
    "ignore::UserWarning:pyproj.*",
    "ignore::UserWarning:matplotlib.*",
    "ignore::UserWarning:seaborn.*",

    # Ignore specific warnings from scientific packages
    "ignore:.*numpy.ndarray size changed.*:RuntimeWarning",
    "ignore:.*invalid value encountered.*:RuntimeWarning:numpy.*",
    "ignore:.*divide by zero encountered.*:RuntimeWarning:numpy.*",

    # Ignore warnings from statistical packages
    "ignore::FutureWarning:statsmodels.*",
    "ignore::FutureWarning:linearmodels.*",
    "ignore::FutureWarning:arch.*",
    "ignore::FutureWarning:sklearn.*",
    "ignore::FutureWarning:scikit-learn.*",

    # Ignore warnings from Bayesian packages
    "ignore::FutureWarning:pymc.*",
    "ignore::FutureWarning:arviz.*",
    "ignore::FutureWarning:pytensor.*",
    "ignore::UserWarning:pymc.*",
    "ignore::UserWarning:arviz.*",

    # Ignore warnings from networking/API packages
    "ignore::DeprecationWarning:urllib3.*",
    "ignore::DeprecationWarning:requests.*",
    "ignore::DeprecationWarning:hdx.*",

    # Ignore warnings from Jupyter/IPython
    "ignore::DeprecationWarning:IPython.*",
    "ignore::DeprecationWarning:jupyter.*",
    "ignore::DeprecationWarning:notebook.*",

    # Convert our own warnings to errors (strict mode for our code)
    "error::DeprecationWarning:yemen_market.*",
    "error::FutureWarning:yemen_market.*",
    "error::UserWarning:yemen_market.*",
]
