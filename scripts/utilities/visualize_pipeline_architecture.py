#!/usr/bin/env python3
"""Create a visual representation of the enhanced pipeline architecture."""

import sys
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def create_pipeline_diagram():
    """Create a simplified visual diagram of the enhanced pipeline."""
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # Color scheme
    colors = {
        'data': '#e3f2fd',
        'prep': '#f3e5f5',
        'model': '#e8f5e9',
        'analysis': '#fff3e0',
        'result': '#fce4ec',
        'output': '#e0f2f1'
    }
    
    # Define components
    components = [
        # Data Sources (top)
        {'name': 'Data Sources\n(WFP, ACLED, ACAPS)', 'pos': (5, 9), 'width': 3, 'height': 0.8, 'color': colors['data']},
        
        # Enhanced Data Prep
        {'name': 'Panel Builder\n+ Integration', 'pos': (2, 7.5), 'width': 2, 'height': 0.8, 'color': colors['prep']},
        {'name': 'Feature Engineering\n+ Spatial K-NN', 'pos': (5, 7.5), 'width': 2, 'height': 0.8, 'color': colors['prep']},
        {'name': 'Data Preparation\n+ Winsorization', 'pos': (8, 7.5), 'width': 2, 'height': 0.8, 'color': colors['prep']},
        
        # Three-Tier Models
        {'name': 'Tier 1: Pooled\n+ Spatial Features', 'pos': (1.5, 5.5), 'width': 2, 'height': 0.8, 'color': colors['model']},
        {'name': 'Tier 2: Commodity\n+ Threshold VECM', 'pos': (4, 5.5), 'width': 2, 'height': 0.8, 'color': colors['model']},
        {'name': 'Tier 3: Validation\n+ Granger, IRF', 'pos': (6.5, 5.5), 'width': 2, 'height': 0.8, 'color': colors['model']},
        
        # Additional Analyses
        {'name': 'Price Transmission\nMarket Pairs', 'pos': (1, 3.5), 'width': 2, 'height': 0.8, 'color': colors['analysis']},
        {'name': 'Exchange\nPass-through', 'pos': (3.5, 3.5), 'width': 2, 'height': 0.8, 'color': colors['analysis']},
        {'name': 'Model Comparison\n& Ensemble', 'pos': (6, 3.5), 'width': 2, 'height': 0.8, 'color': colors['analysis']},
        
        # Results Integration
        {'name': 'Results Analyzer\n+ Policy Insights', 'pos': (5, 2), 'width': 3, 'height': 0.8, 'color': colors['result']},
        
        # Outputs
        {'name': 'Spatial\nVisualizations', 'pos': (2, 0.5), 'width': 1.8, 'height': 0.8, 'color': colors['output']},
        {'name': 'Dynamic\nAnalysis', 'pos': (5, 0.5), 'width': 1.8, 'height': 0.8, 'color': colors['output']},
        {'name': 'Comprehensive\nReport', 'pos': (8, 0.5), 'width': 1.8, 'height': 0.8, 'color': colors['output']},
    ]
    
    # Draw components
    for comp in components:
        box = FancyBboxPatch(
            (comp['pos'][0] - comp['width']/2, comp['pos'][1] - comp['height']/2),
            comp['width'], comp['height'],
            boxstyle="round,pad=0.1",
            facecolor=comp['color'],
            edgecolor='black',
            linewidth=1.5
        )
        ax.add_patch(box)
        ax.text(comp['pos'][0], comp['pos'][1], comp['name'], 
                ha='center', va='center', fontsize=9, fontweight='bold')
    
    # Draw connections
    connections = [
        # Data to prep
        ((5, 8.6), (2, 8.3)),
        ((5, 8.6), (5, 8.3)),
        ((5, 8.6), (8, 8.3)),
        
        # Prep to models
        ((5, 7.1), (1.5, 6.3)),
        ((5, 7.1), (4, 6.3)),
        ((5, 7.1), (6.5, 6.3)),
        
        # Prep to additional
        ((5, 7.1), (1, 4.3)),
        ((5, 7.1), (3.5, 4.3)),
        
        # Models to comparison
        ((4, 5.1), (6, 4.3)),
        
        # All to results
        ((1.5, 5.1), (5, 2.8)),
        ((4, 5.1), (5, 2.8)),
        ((6.5, 5.1), (5, 2.8)),
        ((1, 3.1), (5, 2.8)),
        ((3.5, 3.1), (5, 2.8)),
        ((6, 3.1), (5, 2.8)),
        
        # Results to outputs
        ((5, 1.6), (2, 1.3)),
        ((5, 1.6), (5, 1.3)),
        ((5, 1.6), (8, 1.3)),
    ]
    
    for start, end in connections:
        arrow = ConnectionPatch(start, end, "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=20, fc="black", lw=1.5)
        ax.add_artist(arrow)
    
    # Add title
    ax.text(5, 9.8, 'Enhanced Yemen Market Integration Pipeline', 
            ha='center', va='center', fontsize=16, fontweight='bold')
    
    # Add legend
    legend_elements = [
        mpatches.Patch(color=colors['data'], label='Data Sources'),
        mpatches.Patch(color=colors['prep'], label='Data Preparation'),
        mpatches.Patch(color=colors['model'], label='Econometric Models'),
        mpatches.Patch(color=colors['analysis'], label='Advanced Analysis'),
        mpatches.Patch(color=colors['result'], label='Results Integration'),
        mpatches.Patch(color=colors['output'], label='Outputs')
    ]
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))
    
    # Add key enhancements annotation
    enhancements = [
        "Key Enhancements:",
        "• Spatial features (K-NN)",
        "• Threshold VECM",
        "• Price transmission",
        "• Advanced validation",
        "• Ensemble methods"
    ]
    
    ax.text(0.5, 6, '\n'.join(enhancements), 
            fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor='white', edgecolor='black'))
    
    plt.tight_layout()
    
    # Save figure
    output_dir = project_root / "reports" / "figures"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    plt.savefig(output_dir / "enhanced_pipeline_architecture.png", dpi=300, bbox_inches='tight')
    plt.savefig(output_dir / "enhanced_pipeline_architecture.pdf", bbox_inches='tight')
    
    print(f"Pipeline diagram saved to {output_dir}")
    plt.show()


if __name__ == "__main__":
    create_pipeline_diagram()