#!/usr/bin/env python3
"""
Generate Claude command context automatically.
This script is called by slash commands and Makefile.
"""

import json
import os
import re
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Project root
PROJECT_ROOT = Path(__file__).parent.parent.parent

# Add project root to path for imports
sys.path.insert(0, str(PROJECT_ROOT))

from yemen_market.utils.logging import (
    info, warning, error, timer, bind
)

# Set up logging
bind(module=__name__)


class ProjectContextGenerator:
    """Generate project context for Claude Code."""

    def __init__(self, project_root: Path = PROJECT_ROOT):
        self.project_root = project_root
        self.context = {}

    def gather_all_info(self) -> Dict:
        """Gather all project information."""
        info("📊 Gathering project information...")

        with timer("gather_project_info"):
            self.context = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "git": self._get_git_info(),
                "progress": self._get_progress_info(),
                "tests": self._get_test_info(),
                "models": self._get_model_status(),
                "active": self._get_active_context(),
                "structure": self._get_project_structure()
            }

        return self.context

    def _get_git_info(self) -> Dict:
        """Extract git information."""
        try:
            branch = subprocess.check_output(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                cwd=self.project_root, text=True
            ).strip()

            commits = subprocess.check_output(
                ["git", "log", "--oneline", "-5"],
                cwd=self.project_root, text=True
            ).strip().split('\n')[:3]

            status = subprocess.check_output(
                ["git", "status", "--porcelain"],
                cwd=self.project_root, text=True
            ).strip()

            has_changes = len(status) > 0

            return {
                "branch": branch,
                "recent_commits": commits,
                "has_changes": has_changes,
                "changed_files": len(status.split('\n')) if status else 0
            }
        except Exception as e:
            warning(f"⚠️  Git info error: {e}")
            return {"branch": "unknown", "recent_commits": [], "has_changes": False}

    def _get_progress_info(self) -> Dict:
        """Extract progress from reports/progress/README.md."""
        progress_file = self.project_root / "reports/progress/README.md"

        if not progress_file.exists():
            return {"percentage": "0", "phase": "Unknown", "next_steps": []}

        content = progress_file.read_text()

        # Extract percentage
        match = re.search(r'(\d+)%\s+[Cc]omplete', content)
        percentage = match.group(1) if match else "0"

        # Extract phase
        phase_match = re.search(r'Current Phase[:\s]+([^\n]+)', content, re.I)
        phase = phase_match.group(1).strip() if phase_match else "Implementation"

        # Extract next steps
        next_section = re.search(
            r'Next Steps.*?$(.*?)(?:^#|\Z)',
            content,
            re.MULTILINE | re.DOTALL | re.I
        )
        next_steps = []
        if next_section:
            lines = next_section.group(1).strip().split('\n')
            next_steps = [
                line.strip().lstrip('-*').strip()
                for line in lines
                if line.strip() and not line.startswith('#')
            ][:5]

        return {
            "percentage": percentage,
            "phase": phase,
            "next_steps": next_steps
        }

    def _get_test_info(self) -> Dict:
        """Get test coverage information."""
        test_dir = self.project_root / "tests"
        test_files = list(test_dir.rglob("test_*.py"))

        # Count lines
        total_lines = sum(
            len(f.read_text().splitlines())
            for f in test_files
            if f.exists()
        )

        # Check coverage
        coverage_html = self.project_root / "htmlcov/index.html"
        coverage_pct = "Not generated"

        if coverage_html.exists():
            try:
                content = coverage_html.read_text()
                match = re.search(r'pc_cov">(\d+)%', content)
                if match:
                    coverage_pct = f"{match.group(1)}%"
            except:
                pass

        return {
            "file_count": len(test_files),
            "line_count": total_lines,
            "coverage": coverage_pct
        }

    def _get_model_status(self) -> Dict:
        """Check model implementation status."""
        models_dir = self.project_root / "src/yemen_market/models/three_tier"

        checks = {
            "tier1": models_dir / "tier1_pooled/pooled_panel_model.py",
            "tier2": models_dir / "tier2_commodity/commodity_specific_model.py",
            "tier3": models_dir / "tier3_validation/pca_analysis.py",
            "runner": models_dir / "integration/three_tier_runner.py"
        }

        status = {name: path.exists() for name, path in checks.items()}
        status["percentage"] = (sum(status.values()) / len(status)) * 100

        return status

    def _get_active_context(self) -> Dict:
        """Get active context information."""
        context_file = self.project_root / ".claude/ACTIVE_CONTEXT.md"

        if not context_file.exists():
            return {"sprint": "Unknown", "focus": "Check .claude/ACTIVE_CONTEXT.md"}

        content = context_file.read_text()

        # Extract sprint
        sprint_match = re.search(r'Sprint[:\s]+([^\n]+)', content, re.I)
        sprint = sprint_match.group(1).strip() if sprint_match else "Unknown"

        # Extract focus
        focus_match = re.search(r'Focus[:\s]+([^\n]+)', content, re.I)
        focus = focus_match.group(1).strip() if focus_match else "Implementation"

        return {"sprint": sprint, "focus": focus}

    def _get_project_structure(self) -> Dict:
        """Get key project paths."""
        return {
            "root": str(self.project_root),
            "models": "src/yemen_market/models/three_tier/",
            "tests": "tests/unit/models/three_tier/",
            "data": "data/",
            "notebooks": "notebooks/04_models/"
        }

    def generate_context_file(self) -> str:
        """Generate the context markdown file."""
        ctx = self.context

        # Build status indicators
        tier_status = "✅" if ctx["models"]["percentage"] == 100 else "🟡"
        test_status = "✅" if ctx["tests"]["coverage"] != "Not generated" else "🟡"

        content = f"""# Yemen Market Integration - Project Context
*Auto-generated: {ctx['timestamp']}*

## 🎯 Quick Status
- **Progress**: {ctx['progress']['percentage']}% complete
- **Phase**: {ctx['progress']['phase']}
- **Models**: {tier_status} {ctx['models']['percentage']:.0f}% implemented
- **Tests**: {test_status} {ctx['tests']['file_count']} files, {ctx['tests']['coverage']} coverage
- **Branch**: {ctx['git']['branch']} {'(uncommitted changes)' if ctx['git']['has_changes'] else ''}

## 🚀 Initialize Session

```
PROJECT: Yemen Market Integration - Econometric analysis of conflict-affected markets
STATUS: {ctx['progress']['percentage']}% complete in {ctx['progress']['phase']} phase
APPROACH: Three-tier methodology (pooled panel → commodity VECM → factor validation)

IMPLEMENTATION:
{"✅" if ctx['models']['tier1'] else "⭕"} Tier 1: Pooled Panel (linearmodels.PanelOLS)
{"✅" if ctx['models']['tier2'] else "⭕"} Tier 2: Commodity VECM (threshold models)
{"✅" if ctx['models']['tier3'] else "⭕"} Tier 3: Factor Analysis (PCA validation)
{"✅" if ctx['models']['runner'] else "⭕"} Integration: Three-Tier Runner

TESTING: {ctx['tests']['file_count']} test files | {ctx['tests']['line_count']:,} lines | Coverage: {ctx['tests']['coverage']}

CURRENT FOCUS: {ctx['active']['focus']}
SPRINT: {ctx['active']['sprint']}

CRITICAL RULES:
1. Use enhanced logging (yemen_market.utils.logging) - NEVER print()
2. No temporary files - only production code
3. Complete all steps - no shortcuts
4. Target 100% test coverage
5. Follow documentation hierarchy

NEXT STEPS:
{chr(10).join(f'{i+1}. {step}' for i, step in enumerate(ctx['progress']['next_steps'])) if ctx['progress']['next_steps'] else '1. Check reports/progress/README.md'}
```

## 📁 Key Paths
- **Source**: `{ctx['structure']['models']}`
- **Tests**: `{ctx['structure']['tests']}`
- **Data**: `{ctx['structure']['data']}`
- **Notebooks**: `{ctx['structure']['notebooks']}`

## 🔧 Essential Commands
```bash
make week5-models          # Run three-tier analysis
make test                  # Run full test suite
make test-models-quick     # Quick model tests
make update-claude-commands # Refresh this file
```

## 💻 Code Patterns

### Enhanced Logging (REQUIRED)
```python
from yemen_market.utils.logging import info, timer, progress, bind

bind(module=__name__)  # Set context

with timer("operation"):
    with progress("Processing", total=n) as update:
        for item in items:
            # Process
            info("Done", item=item.id)
            update(1)
```

### Import Pattern
```python
from yemen_market.models.three_tier import ThreeTierRunner
from yemen_market.data import PanelBuilder, WFPProcessor
```

## 📊 Recent Activity
**Git**: {ctx['git']['branch']} branch
{chr(10).join(f'- {commit}' for commit in ctx['git']['recent_commits'][:3])}

---
Run `/project:refresh` or `make update-claude-commands` to update.
"""
        return content

    def save_context(self, content: str) -> Path:
        """Save the context file."""
        output_dir = self.project_root / ".claude/commands"
        output_dir.mkdir(parents=True, exist_ok=True)

        output_file = output_dir / "project_context.md"
        output_file.write_text(content)

        return output_file


def main():
    """Generate Claude context files."""
    with timer("generate_claude_context"):
        generator = ProjectContextGenerator()

        # Gather information
        generator.gather_all_info()

        # Generate content
        content = generator.generate_context_file()

        # Save file
        output_path = generator.save_context(content)

        # Summary
        ctx = generator.context
        info("✅ Context Updated Successfully!")
        info("")
        info("📊 Project Status:")
        info(f"   - Progress: {ctx['progress']['percentage']}% complete")
        info(f"   - Models: {ctx['models']['percentage']:.0f}% implemented")
        info(f"   - Tests: {ctx['tests']['file_count']} files, {ctx['tests']['coverage']} coverage")
        info(f"   - Branch: {ctx['git']['branch']}")
        info("")
        info(f"📁 Output: {output_path}")
        info("")
        info("Use slash commands in Claude Code:")
        info("   /project:init     - Initialize new session")
        info("   /project:refresh  - Update context")
        info("   /project:status   - Detailed status")

    return 0


if __name__ == "__main__":
    sys.exit(main())