#!/usr/bin/env python3
"""
MCP server for pytest code coverage and testing automation.

This server provides tools for running pytest with coverage reporting
and analyzing test failures for the Yemen Market Integration project.
"""

import subprocess
import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
import sys

# MCP imports
from mcp.server.fastmcp import FastMCP

# Create the MCP server
mcp = FastMCP("pytest-coverage-server")

# Configuration
PROJECT_DIR = Path(__file__).parent
VENV_PYTHON = PROJECT_DIR / "venv" / "bin" / "python"
if not VENV_PYTHON.exists():
    VENV_PYTHON = sys.executable


@mcp.tool()
def run_pytest_coverage(
    test_path: Optional[str] = None,
    coverage_fail_under: int = 100,
    parallel: bool = True,
    verbose: bool = True
) -> Dict[str, Any]:
    """
    Run pytest with coverage reporting and return detailed results.
    
    Args:
        test_path: Specific test file or directory to run (optional)
        coverage_fail_under: Minimum coverage percentage required (default: 100)
        parallel: Run tests in parallel using pytest-xdist (default: True)
        verbose: Show verbose output (default: True)
    
    Returns:
        Dictionary containing test results, coverage data, and failures
    """
    cmd = [
        str(VENV_PYTHON), "-m", "pytest",
        f"--cov={PROJECT_DIR}/src/yemen_market",
        "--cov-report=json",
        "--cov-report=term-missing",
        f"--cov-fail-under={coverage_fail_under}",
        "--tb=short",
        "--json-report",
        "--json-report-file=pytest_report.json"
    ]
    
    if parallel:
        cmd.extend(["-n", "auto"])
    
    if verbose:
        cmd.append("-v")
    
    if test_path:
        cmd.append(test_path)
    
    # Run pytest
    result = subprocess.run(
        cmd,
        cwd=PROJECT_DIR,
        capture_output=True,
        text=True
    )
    
    # Parse results
    report = {
        "exit_code": result.returncode,
        "stdout": result.stdout,
        "stderr": result.stderr,
        "passed": result.returncode == 0
    }
    
    # Load pytest json report
    pytest_report_path = PROJECT_DIR / "pytest_report.json"
    if pytest_report_path.exists():
        with open(pytest_report_path) as f:
            pytest_data = json.load(f)
            report["summary"] = pytest_data.get("summary", {})
            report["failed_tests"] = [
                {
                    "nodeid": test["nodeid"],
                    "outcome": test["outcome"],
                    "call": test.get("call", {})
                }
                for test in pytest_data.get("tests", [])
                if test["outcome"] == "failed"
            ]
    
    # Load coverage report
    coverage_path = PROJECT_DIR / "coverage.json"
    if coverage_path.exists():
        with open(coverage_path) as f:
            coverage_data = json.load(f)
            report["coverage"] = {
                "percent_covered": coverage_data["totals"]["percent_covered"],
                "missing_lines": coverage_data["totals"]["missing_lines"],
                "covered_lines": coverage_data["totals"]["covered_lines"],
                "num_statements": coverage_data["totals"]["num_statements"]
            }
            
            # Find files with low coverage
            report["low_coverage_files"] = [
                {
                    "file": file_path,
                    "percent_covered": data["summary"]["percent_covered"],
                    "missing_lines": data["missing_lines"]
                }
                for file_path, data in coverage_data["files"].items()
                if data["summary"]["percent_covered"] < coverage_fail_under
            ]
    
    return report


@mcp.tool()
def analyze_test_failures(
    test_file: Optional[str] = None,
    include_source: bool = True
) -> Dict[str, Any]:
    """
    Analyze test failures and provide actionable suggestions.
    
    Args:
        test_file: Specific test file to analyze (optional)
        include_source: Include source code of failing tests (default: True)
    
    Returns:
        Dictionary with failure analysis and suggestions
    """
    # First run tests to get failures
    test_results = run_pytest_coverage(test_path=test_file, verbose=False)
    
    if not test_results.get("failed_tests"):
        return {
            "status": "success",
            "message": "No test failures found!",
            "coverage": test_results.get("coverage", {})
        }
    
    analysis = {
        "total_failures": len(test_results["failed_tests"]),
        "failures": []
    }
    
    for failure in test_results["failed_tests"]:
        failure_info = {
            "test": failure["nodeid"],
            "outcome": failure["outcome"]
        }
        
        # Extract error details
        if "call" in failure and "longrepr" in failure["call"]:
            failure_info["error"] = failure["call"]["longrepr"]
        
        # Get test source code if requested
        if include_source:
            test_path = failure["nodeid"].split("::")[0]
            full_path = PROJECT_DIR / test_path
            if full_path.exists():
                with open(full_path) as f:
                    content = f.read()
                    # Extract the specific test function
                    test_name = failure["nodeid"].split("::")[-1].split("[")[0]
                    if f"def {test_name}" in content:
                        start = content.find(f"def {test_name}")
                        # Find the next function or class
                        next_def = content.find("\ndef ", start + 1)
                        next_class = content.find("\nclass ", start + 1)
                        end = min(x for x in [next_def, next_class, len(content)] if x > start)
                        failure_info["source"] = content[start:end].strip()
        
        # Add common fix suggestions based on error patterns
        error_str = str(failure_info.get("error", ""))
        suggestions = []
        
        if "AttributeError" in error_str:
            suggestions.append("Check that all mocked objects have the required attributes")
            suggestions.append("Verify import statements are correct")
        elif "KeyError" in error_str:
            suggestions.append("Ensure dictionary keys exist before accessing them")
            suggestions.append("Check column names in DataFrames match expected values")
        elif "ValueError" in error_str:
            suggestions.append("Validate input data meets function requirements")
            suggestions.append("Check data types and ranges")
        elif "AssertionError" in error_str:
            suggestions.append("Review assertion conditions")
            suggestions.append("Check expected vs actual values")
        
        failure_info["suggestions"] = suggestions
        analysis["failures"].append(failure_info)
    
    return analysis


@mcp.tool()
def get_coverage_summary() -> Dict[str, Any]:
    """
    Get a summary of current test coverage and identify coverage gaps.
    
    Returns:
        Dictionary with coverage summary and files needing tests
    """
    # Run coverage report
    cmd = [
        str(VENV_PYTHON), "-m", "coverage", "report",
        "--format=json"
    ]
    
    result = subprocess.run(
        cmd,
        cwd=PROJECT_DIR,
        capture_output=True,
        text=True
    )
    
    if result.returncode != 0:
        # Try to generate coverage first
        run_pytest_coverage(verbose=False)
        result = subprocess.run(cmd, cwd=PROJECT_DIR, capture_output=True, text=True)
    
    try:
        coverage_data = json.loads(result.stdout)
    except:
        return {"error": "Could not parse coverage data", "stderr": result.stderr}
    
    # Analyze coverage gaps
    summary = {
        "total_coverage": coverage_data.get("totals", {}).get("percent_covered", 0),
        "total_lines": coverage_data.get("totals", {}).get("num_statements", 0),
        "covered_lines": coverage_data.get("totals", {}).get("covered_lines", 0),
        "missing_lines": coverage_data.get("totals", {}).get("missing_lines", 0),
        "files_needing_coverage": []
    }
    
    # Find files with less than 100% coverage
    for file_path, file_data in coverage_data.get("files", {}).items():
        if file_data["summary"]["percent_covered"] < 100:
            summary["files_needing_coverage"].append({
                "file": file_path,
                "coverage": file_data["summary"]["percent_covered"],
                "missing_lines": file_data["missing_lines"],
                "missing_branches": file_data.get("missing_branches", [])
            })
    
    # Sort by lowest coverage first
    summary["files_needing_coverage"].sort(key=lambda x: x["coverage"])
    
    return summary


@mcp.tool()
def test_specific_module(
    module_path: str,
    focus_on_failures: bool = True
) -> Dict[str, Any]:
    """
    Run tests for a specific module with detailed feedback.
    
    Args:
        module_path: Path to the module to test (e.g., 'tests/unit/models/three_tier')
        focus_on_failures: Only show failing tests (default: True)
    
    Returns:
        Dictionary with test results and specific feedback
    """
    results = run_pytest_coverage(
        test_path=module_path,
        verbose=True,
        parallel=False  # Disable parallel for more detailed output
    )
    
    # If focusing on failures, filter the output
    if focus_on_failures and results.get("failed_tests"):
        filtered_output = []
        lines = results["stdout"].split("\n")
        
        capture = False
        for line in lines:
            if "FAILED" in line or "ERROR" in line:
                capture = True
            elif "PASSED" in line and capture:
                capture = False
            
            if capture or "FAILED" in line or "ERROR" in line or line.startswith("="):
                filtered_output.append(line)
        
        results["filtered_output"] = "\n".join(filtered_output)
    
    return results


# Run the server
if __name__ == "__main__":
    mcp.run()