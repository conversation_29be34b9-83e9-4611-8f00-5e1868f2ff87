# Yemen Market Integration Analysis

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Overview

This repository implements econometric analysis of market integration in conflict-affected Yemen, examining how dual exchange rates and territorial fragmentation affect commodity price transmission using a three-tier panel methodology.

## Installation

```bash
# Clone the repository
git clone https://github.com/worldbank/yemen-market-integration.git
cd yemen-market-integration

# Create virtual environment using setup script
./setup_venv.sh

# Or manually create environment
python3.10 -m venv venv  # Note: Use venv, not .venv

# Activate the virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
# venv\\Scripts\\activate

# Install dependencies
pip install --upgrade pip
pip install -r requirements.txt
```

## Quick Start

```bash
# 1. Download data
python scripts/data_collection/download_data.py
python scripts/data_collection/download_acled_data.py

# 2. Process data
python scripts/data_processing/process_wfp_data.py
python scripts/data_processing/process_acaps_data.py
python scripts/data_processing/process_acled_data.py
python scripts/data_processing/run_spatial_joins.py

# 3. Prepare data for modeling
python scripts/analysis/prepare_data_for_modeling.py

# 4. Run enhanced analysis pipeline (NEW)
python scripts/analysis/enhanced_analysis_pipeline.py

# 4. Run econometric models
python scripts/analysis/run_three_tier_models_updated.py

# 5. Extract and analyze results
python scripts/analysis/extract_and_analyze_results.py

# 6. Run tests
pytest tests/

```

## Project Structure

yemen-market-integration/
├── data/                   # Data directories (gitignored)
│   ├── raw/               # Downloaded from HDX
│   ├── interim/           # Intermediate processing
│   └── processed/         # Analysis-ready datasets
├── docs/                   # Documentation
│   ├── api/               # API documentation
│   ├── guides/            # User guides
│   ├── models/            # Model specifications
│   └── data/              # Data documentation
├── notebooks/             # Jupyter notebooks for analysis
├── reports/               # Generated outputs
│   ├── figures/          # Visualizations
│   └── progress/         # Project progress tracking
├── scripts/              # Executable scripts
│   ├── data_collection/  # Data download scripts
│   ├── data_processing/  # Processing scripts
│   ├── analysis/         # Analysis scripts
│   └── utilities/        # Utility scripts
├── src/yemen_market/     # Main package
│   ├── config/          # Configuration
│   ├── data/            # Data processing modules
│   │   ├── panel_builder.py    # Balanced panel creation
│   │   └── spatial_joins.py    # Geospatial processing
│   ├── features/        # Feature engineering
│   │   ├── feature_engineering.py  # Core features + interactions
│   │   └── data_preparation.py     # Econometric prep + spatial features
│   ├── models/          # Econometric models
│   │   └── three_tier/  # Three-tier framework
│   │       ├── core/    # Base classes
│   │       ├── diagnostics/  # Econometric tests + RESET/Chow
│   │       ├── tier1_pooled/
│   │       ├── tier2_commodity/
│   │       ├── tier3_validation/
│   │       └── integration/    # Results analysis
│   ├── utils/           # Utilities (enhanced logging)
│   └── visualization/   # Plotting functions
├── tests/               # Unit and integration tests
└── archived/            # Deprecated code (for reference)

## Key Components

### Data Pipeline

- **HDXClient**: Fetch humanitarian data from HDX platform
- **WFPProcessor**: Process World Food Programme price data
- **ACAPSProcessor**: Process territorial control data
- **ACLEDProcessor**: Process conflict event data
- **SpatialJoiner**: Map markets to control zones
- **PanelBuilder**: Create analysis-ready panel datasets

### Econometric Models (Enhanced Three-Tier Approach)

- **Tier 1**: Pooled panel regression with multi-way fixed effects
  - Enhanced with spatial features (K-nearest neighbors)
  - Interaction effects (Zone × Time, Conflict × Zone)
  - Driscoll-Kraay standard errors for spatial correlation
  
- **Tier 2**: Commodity-specific models
  - Standard and **Threshold VECM** for regime-switching analysis
  - Grid search for optimal thresholds
  - Market-specific cointegration tests
  
- **Tier 3**: Advanced validation
  - Factor analysis with conflict indicators
  - **Granger causality tests**
  - **Impulse response functions**
  - **Variance decomposition**

### Additional Advanced Analyses

- **Price Transmission**: Analyze price dynamics between market pairs
- **Exchange Rate Pass-through**: Study dual exchange rate impacts
- **Model Ensemble**: Combine predictions from multiple models
- **Spatial Visualization**: Geographic price patterns and market networks

**Note**: The framework includes automated diagnostic testing with World Bank-standard econometric tests (Wooldridge, Pesaran CD, etc.) that run automatically after model estimation.

### 🚀 New Enhancements (Jan 2025)

- **Spatial Features**: K-nearest neighbor price lags and spatial spillover effects
- **Interaction Effects**: Zone × Time, Conflict × Commodity, and Ramadan effects
- **Dual Exchange Rate Modeling**: Premium calculations and zone-specific impacts
- **Advanced Diagnostics**: Ramsey RESET test, Chow structural break tests
- **Enhanced Pipeline**: Fully automated analysis with `enhanced_analysis_pipeline.py`

### Documentation

📍 **New to the project?** See our [Documentation Map](./docs/DOCUMENTATION_MAP.md) for easy navigation!

- [Methodology](./METHODOLOGY.md) - Technical approach
- [API Reference](./docs/api/README.md) - Complete API documentation
- [User Guides](./docs/guides/) - How-to guides
- [Data Documentation](./docs/data/) - Data pipeline details
- [Contributing](./CONTRIBUTING.md) - How to contribute

## Data Sources

- **World Food Programme (WFP)**: Weekly commodity prices and exchange rates
- **ACAPS Yemen Analysis Hub**: Bi-weekly territorial control data
- **ACLED**: Conflict event data
- **HDX**: Administrative boundaries and infrastructure

## Usage Examples

### Basic Analysis

```python
from yemen_market.data import PanelBuilder
from yemen_market.models.three_tier.integration import ThreeTierAnalysis

# Load integrated panel
builder = PanelBuilder()
panel = builder.load_integrated_panel()

# Configure and run three-tier analysis
config = {
    'run_diagnostics': True,  # Automatic diagnostic testing
    'output_dir': 'results/analysis'
}
analysis = ThreeTierAnalysis(config)
results = analysis.run_full_analysis(panel)

# View results
print(results.tier1_summary())  # Pooled panel results
print(results.tier2_summary())  # Commodity-specific results
print(results.tier3_summary())  # Factor analysis results
```

### Custom Analysis

See [notebooks/](./notebooks/) for detailed examples and [docs/guides/](./docs/guides/) for comprehensive tutorials.

## Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/
pytest tests/integration/

# Run with coverage
pytest --cov=yemen_market --cov-report=html
```

## Contributing

Please see [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines on contributing to this project.

## License

This project is licensed under the MIT License - see [LICENSE](./LICENSE) for details.

## Citation

If you use this code in your research, please cite:

```bibtex
@software{yemen_market_integration,
  title={Yemen Market Integration Analysis},
  author={World Bank Development Research Group},
  year={2025},
  url={https://github.com/worldbank/yemen-market-integration}
}
```

## Contact

For questions or issues, please open an issue on GitHub or contact the maintainers.
