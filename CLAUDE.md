# CLAUDE.md - Development Rules and Claude Code Integration

## 🚨 CRITICAL DEVELOPMENT RULES 🚨

### 1. Keep the Codebase Clean

- **NEVER** create temporary test files that won't be reused
- **NEVER** leave exploratory code in production directories
- **ALWAYS** delete failed attempts and redundant scripts immediately
- **ALWAYS** ask "Will this file be used again?" before creating it
- If creating a temporary file for testing, use a clear prefix like `temp_` and delete it after use

### 2. Complete All Steps (No Shortcuts)

- **NEVER** skip steps because they seem intensive or complicated
- **ALWAYS** implement the full solution, not a simplified version
- If a task requires 100 operations, do all 100 - don't sample 10
- If processing takes time, use progress bars and logging to show status
- Complex tasks deserve more code, not less

### 3. Use Enhanced Logging Throughout

- **ALWAYS** use the enhanced logging system, not basic print() or logging
- **ALWAYS** include timers for operations that might take >1 second
- **ALWAYS** use progress bars for loops with >10 iterations
- **ALWAYS** log data shapes after transformations
- **NEVER** use old-style logging except in backward compatibility layer

Example of proper implementation:

```python
from yemen_market.utils.logging import (
    info, timer, progress, log_data_shape, bind
)

# Set context for this module
bind(module=__name__)

# Time the entire operation
with timer("process_all_markets"):
    # Show progress for long operations
    with progress("Processing markets", total=len(markets)) as update:
        for market in markets:
            # Don't skip any markets!
            result = process_market(market)
            log_data_shape(f"market_{market}", result)
            update(1)
```

## Code Style Guidelines

- NumPy-style docstrings for all public functions
- Type hints for all parameters and returns
- Black formatting (88 char limit)
- Comprehensive unit tests (>90% coverage)
- **Enhanced logging for ALL operations** (not print statements)
- Conventional commits (feat:, fix:, docs:, test:, chore:)
- **Clean commits**: No temporary files in git history

## Documentation Hierarchy (NEVER VIOLATE)

1. **Progress Tracking**: ONLY in `reports/progress/README.md`
   - All percentages, timelines, completed tasks
   - Milestone tracking and sprint details
   - NO progress info in any other file

2. **Active Development**: ONLY in `.claude/ACTIVE_CONTEXT.md`
   - Current sprint focus
   - Immediate next actions (max 5)
   - "To Resume" instructions
   - Recent accomplishments (max 3)

3. **Development Rules**: ONLY in `CLAUDE.md`
   - Critical development patterns
   - Logging requirements
   - Code style guidelines
   - NO project status or progress

4. **Methodology**: ONLY in `METHODOLOGY.md`
   - Technical approaches and frameworks
   - Model specifications
   - Data processing methods
   - NO implementation details

5. **Public Documentation**: `README.md`
   - Installation and usage only
   - NO progress badges or timelines
   - NO "recent updates" sections
   - Link to other docs if needed

## File Creation Rules

Before creating ANY documentation file:

1. Check if content belongs in existing files
2. Verify it doesn't duplicate existing content
3. Ensure clear, unique purpose
4. If unsure, add to existing file first

## Claude Code Integration

### MCP Servers Configuration

For optimal Claude Code performance, enable:

- **filesystem** (default) - Code development
- **git** - Version control
- **github** or **mcp-github-project-manager** - Issue tracking
- **fetch** - API data collection

### Quick Start with Claude Code

```bash
# 1. Navigate to project
cd /Users/<USER>/Documents/GitHub/yemen-market-integration

# 2. Start Claude Code
claude

# 3. Use prepared prompts from .claude/prompts/
# 4. Check active context in .claude/ACTIVE_CONTEXT.md
```

### Claude Best Practices

1. **Context Management**:
   - Keep `.claude/ACTIVE_CONTEXT.md` updated
   - Use `.claude/prompts/` for reusable prompts
   - Don't store progress in `.claude/` directory

2. **Custom Commands**:
   - Place in `.claude/commands/` if needed
   - Document command usage clearly
   - Test commands before committing

3. **Memory vs Context**:
   - Active context = current work
   - Progress tracking = reports/progress/
   - Historical memory = not needed

## Common Commands

```bash
make install        # Set up environment
make test          # Run test suite
make lint          # Check code style
make format        # Format with black
make week5-models  # Run three-tier econometric models
```

## Test-Driven Development Pattern

When implementing new functionality:

1. **Tests define the contract** - Read tests to understand expected behavior
2. **Implement exactly what tests expect** - No simplifications or shortcuts
3. **All mocks represent real behavior** - Don't simplify sophisticated mocking
4. **Edge cases are requirements** - Implement all error handling tests expect

Example: If a test expects `validate_commodity_data()` to return `Tuple[bool, List[str]]`, implement exactly that signature, not a simplified version.

## ResultsContainer Diagnostic Pattern

When implementing diagnostic tests for models:

1. **Use DiagnosticAdapter** - Extract data from ResultsContainer via adapter
2. **Implement tier-specific tests** - Different tiers need different diagnostics
3. **Store results in container** - All test results go in `results.diagnostics`
4. **Provide automatic corrections** - Apply fixes when tests fail (e.g., Driscoll-Kraay SEs)
5. **Generate clear reports** - Include test statistic, p-value, and recommendation

Example implementation:
```python
# In panel_diagnostics.py
def run_diagnostics(self, results: ResultsContainer) -> None:
    """Run all diagnostics appropriate for this tier."""
    adapter = DiagnosticAdapter(results)
    
    # Extract what we need via the adapter
    residuals = adapter.get_residuals()
    panel_info = adapter.get_panel_structure()
    
    # Run appropriate tests
    serial_corr = self._wooldridge_serial_correlation(residuals, panel_info)
    cross_sect = self._pesaran_cd_test(residuals, panel_info)
    
    # Store results back in container
    results.diagnostics = {
        'serial_correlation': serial_corr,
        'cross_sectional_dependence': cross_sect,
        # Other test results...
    }
    
    # Apply corrections if needed
    if serial_corr['reject_null'] or cross_sect['reject_null']:
        self._apply_driscoll_kraay_correction(results)
```

## Error Handling Standards

- Use resilient_operation decorator for critical operations
- Implement proper circuit breakers
- Log errors with appropriate context
- Provide meaningful error messages
- Never swallow exceptions silently

## Documentation Quick Reference

| What to Document | Where It Goes | Example |
|-----------------|---------------|---------|
| **Progress/Status** | `reports/progress/README.md` | "88% Complete", "Week 5-6 Goals" |
| **Current Work** | `.claude/ACTIVE_CONTEXT.md` | "Working on Tier 1", "Next: Run analysis" |
| **Dev Rules** | `CLAUDE.md` (this file) | "Use enhanced logging", "No temp files" |
| **Methods** | `METHODOLOGY.md` | "Three-tier approach", "Panel structure" |
| **Public Info** | `README.md` | "Installation", "Usage examples" |
| **Tech Details** | `docs/` folders | API docs, guides, specifications |

**Never Create**: PROJECT_STATUS.md, NOTES.md, TODO.md, or similar redundant files

## See Also

- [Methodology](./METHODOLOGY.md) - Technical approaches
- [Progress Dashboard](./reports/progress/README.md) - Current status
- [Active Context](./.claude/ACTIVE_CONTEXT.md) - What we're working on
