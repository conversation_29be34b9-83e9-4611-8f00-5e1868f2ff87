# Methodology for Yemen Market Integration Analysis

## 1. Overview

This document outlines the methodological framework for analyzing market integration in Yemen during conflict, specifically addressing the challenge of multi-dimensional panel data (market × commodity × time) using a three-tier approach.

> **Note**: This methodology has been enhanced with World Bank-standard econometric techniques. For the complete enhanced approach including spatial features, interaction effects, and advanced diagnostics, see [`docs/methodology/world_bank_enhanced_approach.md`](docs/methodology/world_bank_enhanced_approach.md).

## 2. Data Structure Challenge

### 2.1 The Problem

Our dataset has a three-dimensional structure:

- **Markets**: 28 unique locations across Yemen
- **Commodities**: 23 different goods (wheat, rice, sugar, fuel, etc.)
- **Time**: Monthly observations from 2019-2025

Standard econometric packages expect two-dimensional panels (entity × time), creating a fundamental incompatibility.

### 2.2 The Solution: Three-Tier Methodology

We adopt a comprehensive three-tier approach that leverages the full data structure while maintaining compatibility with standard econometric tools.

## 3. Three-Tier Methodological Framework

### 3.1 Tier 1: Pooled Panel Regression (Primary Analysis)

**Objective**: Estimate the average effect of conflict on market integration across all markets and commodities.

**Specification**:
$$P_{i,j,t} = \alpha + \theta_i + \phi_j + \tau_t + \delta \cdot Conflict_{i,t} + \beta' X_{i,j,t} + \varepsilon_{i,j,t}$$

Where:

- $P_{i,j,t}$: Log price in market $i$, commodity $j$, time $t$
- $\theta_i$: Market fixed effects
- $\phi_j$: Commodity fixed effects  
- $\tau_t$: Time fixed effects
- $Conflict_{i,t}$: Conflict intensity measure
- $X_{i,j,t}$: Control variables (exchange rates, seasonality)

**Key Features**:

- Multi-way fixed effects control for unobserved heterogeneity
- Entity defined as market-commodity pairs
- Clustered standard errors at market level
- Driscoll-Kraay corrections for spatial correlation

### 3.2 Tier 2: Commodity-Specific Analysis (Supporting Evidence)

**Objective**: Examine heterogeneous effects across different commodity types, particularly imported vs. local goods.

**Specification** (Threshold VECM for each commodity):
$$\Delta P_{i,t} = \begin{cases}
\alpha_1 + \beta_1 EC_{t-1} + \Gamma_1 \Delta P_{t-1} + \epsilon_{1t} & \text{if } Conflict_t \leq \gamma \\
\alpha_2 + \beta_2 EC_{t-1} + \Gamma_2 \Delta P_{t-1} + \epsilon_{2t} & \text{if } Conflict_t > \gamma
\end{cases}$$

Where:
- $EC_{t-1}$: Error correction term (price deviations from equilibrium)
- $\gamma$: Estimated conflict threshold
- $\beta_k$: Speed of adjustment in regime $k$

**Priority Commodities**:
1. Wheat (imported staple)
2. Rice (imported staple)
3. Sugar (imported)
4. Fuel (critical import)

### 3.3 Tier 3: Factor Analysis (Validation)

**Objective**: Identify latent patterns in price movements and validate conflict's role in market fragmentation.

**Specification** (Dynamic Factor Model):
$$P_{i,j,t} = \Lambda_i F_t + \Psi_j G_t + u_{i,j,t}$$

Where:
- $F_t$: Common time factors (national trends)
- $G_t$: Commodity-specific factors
- $\Lambda_i, \Psi_j$: Factor loadings
- $u_{i,j,t}$: Idiosyncratic component

**Validation Process**:
- Extract 2-3 principal factors
- Correlate factors with conflict patterns
- Assess variance explained by common vs. idiosyncratic components

## 4. Identification Strategy

### 4.1 Causal Inference
- **Within Variation**: Fixed effects exploit within-market changes over time
- **Control Boundaries**: Use boundary discontinuities for identification
- **Timing Variation**: Exploit differential timing of conflict escalation

### 4.2 Endogeneity Concerns
- **Reverse Causality**: High prices may trigger conflict
- **Omitted Variables**: Unobserved shocks affecting both prices and conflict
- **Solutions**: Lagged instruments, control function approach, spatial lags

## 5. Robustness Framework

### 5.1 Standard Error Corrections
1. **Cluster-robust**: Account for within-market correlation
2. **Driscoll-Kraay**: Handle cross-sectional dependence
3. **HAC**: Heteroskedasticity and autocorrelation consistent

### 5.2 Sensitivity Analyses
1. **Alternative Specifications**: Different FE combinations
2. **Sample Restrictions**: Exclude outlier markets
3. **Time Windows**: Pre/post major events
4. **Missing Data**: Multiple imputation approaches

## 6. Policy Simulation Framework

### 6.1 Conflict Reduction Scenarios
- **Baseline**: Current conflict levels
- **Peace Dividend**: Zero conflict intensity
- **Gradual De-escalation**: Phased reduction

### 6.2 Market Integration Metrics
$$Integration_{t} = 1 - \frac{Var(P_{i,j,t} - \bar{P}_{j,t})}{Var(P_{i,j,t})}$$

Higher values indicate greater integration.

## 7. Data Requirements

### 7.1 Core Variables
- **Prices**: Market-commodity-time observations
- **Conflict**: ACLED events aggregated to market-month
- **Geography**: Market coordinates and distances
- **Control Zones**: Time-varying territorial control

### 7.2 Auxiliary Variables
- **Exchange Rates**: Parallel and official rates
- **Infrastructure**: Road conditions, checkpoints
- **Weather**: Rainfall anomalies
- **Aid Flows**: Humanitarian assistance

## 8. Computational Approach

### 8.1 Software Stack
- **Panel Econometrics**: linearmodels (Python)
- **Time Series**: statsmodels
- **Factor Analysis**: scikit-learn, statsmodels
- **Visualization**: matplotlib, seaborn

### 8.2 Reproducibility
- Version-controlled code
- Containerized environment
- Automated testing
- Documentation standards

### 8.3 Diagnostic Testing Framework

To ensure methodological rigor and meet World Bank publication standards, we implement a comprehensive diagnostic testing framework integrated with the three-tier architecture.

#### 8.3.1 Required Diagnostic Tests by Tier

**Tier 1 - Panel Regression Diagnostics**:
- **Wooldridge Test**: Detects serial correlation in panel data (critical for valid inference)
- **Pesaran CD Test**: Tests cross-sectional dependence (essential given spatial nature of markets)
- **Im-Pesaran-Shin Test**: Panel unit root test for stationarity
- **Hausman Test**: Fixed vs. random effects specification
- **Modified Wald Test**: Panel heteroskedasticity

**Tier 2 - Threshold VECM Diagnostics**:
- **Threshold Linearity Test**: Validates presence of threshold effects
- **Regime Stability Test**: Ensures parameter constancy within regimes
- **Johansen Cointegration Test**: Confirms long-run relationships
- **Weak Exogeneity Test**: Tests variable exogeneity assumptions
- **VECM Residual Diagnostics**: Serial correlation and normality

**Tier 3 - Factor Model Diagnostics**:
- **Factor Adequacy Test**: Determines sufficient number of factors
- **Rotation Stability**: Validates factor interpretation
- **Cross-validation**: Out-of-sample predictive performance
- **Bartlett's Sphericity Test**: Tests appropriateness of factor model

#### 8.3.2 Integration with Model Estimation

Diagnostics run automatically with model estimation:

```python
# Automatic diagnostic execution
model = PooledPanelModel(config)
model.fit(data)  # Diagnostics included
diagnostic_report = model.results.get_diagnostic_report()
```

#### 8.3.3 Critical Failure Handling

If critical tests fail (e.g., serial correlation, cross-sectional dependence):
1. Automatic re-estimation with corrected standard errors
2. Warning messages with specific remediation recommendations
3. Documentation of diagnostic failures in results

#### 8.3.4 Publication Standards

All models must pass:
- Serial correlation tests (p > 0.05)
- Cross-sectional independence (or use appropriate corrections)
- Specification tests for functional form
- Stability tests for parameter constancy

See [Diagnostic Testing Framework](./.claude/models/diagnostic_testing_framework.md) for implementation details.

## 9. Expected Outcomes

### 9.1 Primary Results (Tier 1)
- Conflict coefficient magnitude and significance
- Percentage increase in prices due to conflict
- Spatial spillover effects

### 9.2 Heterogeneous Effects (Tier 2)
- Commodity-specific thresholds
- Differential impacts on imported vs. local goods
- Speed of price adjustment by commodity

### 9.3 Structural Insights (Tier 3)
- Number of common factors driving prices
- Correlation between factors and conflict
- Degree of market fragmentation

## 10. Limitations and Extensions

### 10.1 Limitations
- Cannot capture all informal market mechanisms
- Conflict measurement may miss low-level tensions
- Price data quality varies by region

### 10.2 Future Extensions
- High-frequency analysis with weekly data
- Network models of trader relationships
- Machine learning for pattern detection
- Integration with satellite data

## References

Key methodological sources:
- Baltagi, B. (2021). *Econometric Analysis of Panel Data*
- Pesaran, M.H. (2015). *Time Series and Panel Data Econometrics*
- World Bank (2022). *Handbook of Deep Trade Agreements*
- Matyas & Sevestre (2008). *The Econometrics of Panel Data*

## See Also
- [Three-Tier Implementation Guide](./docs/models/yemen_panel_methodology.md)
- [API Documentation](./docs/api/README.md)
- [Data Pipeline](./docs/data/data_pipeline_detailed.md)
