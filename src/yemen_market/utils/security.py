"""Security utilities for safe file operations and data handling.

This module provides security-focused utilities for the Yemen Market Integration
project, ensuring safe file operations, input validation, and secure data handling.

Classes:
    SecureFileHandler: Safe file operations with validation
    InputValidator: Input validation and sanitization
    DataSanitizer: Data cleaning and sanitization utilities

Example:
    >>> from yemen_market.utils.security import SecureFileHandler
    >>> handler = SecureFileHandler()
    >>> data = handler.safe_load_csv("data.csv")
"""

import hashlib
import json
import tempfile
import warnings
from pathlib import Path
from typing import List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from contextlib import contextmanager

from yemen_market.utils.logging import info, warning, error, bind

# Set module context
bind(module=__name__)


class SecurityError(Exception):
    """Custom exception for security-related errors."""
    pass


class SecureFileHandler:
    """Secure file operations with validation and safety checks."""

    # Allowed file extensions for different operations
    ALLOWED_DATA_EXTENSIONS = {'.csv', '.parquet', '.xlsx', '.json', '.pkl', '.pickle'}
    ALLOWED_CONFIG_EXTENSIONS = {'.json', '.yaml', '.yml', '.toml', '.ini'}
    ALLOWED_OUTPUT_EXTENSIONS = {'.csv', '.parquet', '.json', '.pkl', '.pickle', '.html', '.pdf'}

    # Maximum file sizes (in bytes)
    MAX_FILE_SIZE = 1024 * 1024 * 1024  # 1GB
    MAX_CONFIG_SIZE = 10 * 1024 * 1024  # 10MB

    def __init__(self, base_path: Optional[Union[str, Path]] = None):
        """Initialize secure file handler.

        Parameters
        ----------
        base_path : str or Path, optional
            Base directory for file operations. If None, uses current directory.
        """
        self.base_path = Path(base_path) if base_path else Path.cwd()
        self.base_path = self.base_path.resolve()

        info(f"Initialized SecureFileHandler with base path: {self.base_path}")

    def validate_path(self, file_path: Union[str, Path],
                     operation: str = "read") -> Path:
        """Validate file path for security issues.

        Parameters
        ----------
        file_path : str or Path
            Path to validate
        operation : str
            Type of operation ('read', 'write', 'config')

        Returns
        -------
        Path
            Validated and resolved path

        Raises
        ------
        SecurityError
            If path is unsafe or invalid
        """
        path = Path(file_path).resolve()

        # Check for path traversal attacks
        try:
            path.relative_to(self.base_path)
        except ValueError:
            # Allow absolute paths in specific safe directories
            safe_dirs = [
                Path.home() / "Documents",
                Path("/tmp"),
                Path("/var/tmp"),
                Path.cwd()
            ]

            if not any(str(path).startswith(str(safe_dir)) for safe_dir in safe_dirs):
                raise SecurityError(f"Path outside allowed directories: {path}")

        # Check file extension
        if operation == "read" and path.suffix.lower() not in self.ALLOWED_DATA_EXTENSIONS:
            raise SecurityError(f"File extension not allowed for reading: {path.suffix}")
        elif operation == "config" and path.suffix.lower() not in self.ALLOWED_CONFIG_EXTENSIONS:
            raise SecurityError(f"File extension not allowed for config: {path.suffix}")
        elif operation == "write" and path.suffix.lower() not in self.ALLOWED_OUTPUT_EXTENSIONS:
            raise SecurityError(f"File extension not allowed for writing: {path.suffix}")

        # Check file existence for read operations
        if operation == "read" and not path.exists():
            raise SecurityError(f"File does not exist: {path}")

        # Check file size for existing files
        if path.exists():
            file_size = path.stat().st_size
            max_size = self.MAX_CONFIG_SIZE if operation == "config" else self.MAX_FILE_SIZE

            if file_size > max_size:
                raise SecurityError(f"File too large: {file_size} bytes (max: {max_size})")

        return path



    @contextmanager
    def secure_temp_file(self, suffix: str = ".tmp"):
        """Create a secure temporary file.

        Parameters
        ----------
        suffix : str
            File suffix

        Yields
        ------
        Path
            Path to temporary file
        """
        with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as tmp:
            temp_path = Path(tmp.name)

        try:
            yield temp_path
        finally:
            if temp_path.exists():
                temp_path.unlink()

    def compute_file_hash(self, file_path: Union[str, Path]) -> str:
        """Compute SHA-256 hash of file.

        Parameters
        ----------
        file_path : str or Path
            Path to file

        Returns
        -------
        str
            SHA-256 hash
        """
        path = self.validate_path(file_path, "read")

        sha256_hash = hashlib.sha256()
        with open(path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)

        return sha256_hash.hexdigest()

    def validate_file_size(self, file_path: Union[str, Path]) -> None:
        """Validate file size against limits.

        Parameters
        ----------
        file_path : str or Path
            Path to file to validate

        Raises
        ------
        SecurityError
            If file is too large
        """
        path = Path(file_path)
        if path.exists():
            file_size = path.stat().st_size
            if file_size > self.MAX_FILE_SIZE:
                raise SecurityError(f"File too large: {file_size} bytes (max: {self.MAX_FILE_SIZE})")

    def safe_load_csv(self, file_path: Union[str, Path], **kwargs) -> pd.DataFrame:
        """Safely load CSV file with validation.

        Parameters
        ----------
        file_path : str or Path
            Path to CSV file
        **kwargs
            Additional arguments for pd.read_csv

        Returns
        -------
        pd.DataFrame
            Loaded data
        """
        path = self.validate_path(file_path, "read")
        self.validate_file_size(path)

        # Set safe defaults
        safe_kwargs = {
            'engine': 'python',
            'on_bad_lines': 'skip',
            **kwargs
        }

        # Remove low_memory if using python engine
        if 'low_memory' in safe_kwargs and safe_kwargs.get('engine') == 'python':
            safe_kwargs.pop('low_memory')

        try:
            return pd.read_csv(path, **safe_kwargs)
        except Exception as e:
            error(f"Failed to load CSV {path}: {e}")
            raise SecurityError(f"Failed to load CSV file: {e}")

    def safe_save_csv(self, data: pd.DataFrame, file_path: Union[str, Path], **kwargs) -> None:
        """Safely save DataFrame to CSV.

        Parameters
        ----------
        data : pd.DataFrame
            Data to save
        file_path : str or Path
            Output file path
        **kwargs
            Additional arguments for DataFrame.to_csv
        """
        path = self.validate_path(file_path, "write")

        # Sanitize data before saving
        clean_data = DataSanitizer.sanitize_dataframe(data)

        try:
            # Set default index=False if not specified
            if 'index' not in kwargs:
                kwargs['index'] = False
            clean_data.to_csv(path, **kwargs)
            info(f"Saved CSV to {path}")
        except Exception as e:
            error(f"Failed to save CSV {path}: {e}")
            raise SecurityError(f"Failed to save CSV file: {e}")

    def safe_load_json(self, file_path: Union[str, Path]) -> dict:
        """Safely load JSON file.

        Parameters
        ----------
        file_path : str or Path
            Path to JSON file

        Returns
        -------
        dict
            Loaded JSON data
        """
        path = self.validate_path(file_path, "read")
        self.validate_file_size(path)

        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            error(f"Failed to load JSON {path}: {e}")
            raise SecurityError(f"Failed to load JSON file: {e}")

    def safe_save_json(self, data: dict, file_path: Union[str, Path], **kwargs) -> None:
        """Safely save data to JSON file.

        Parameters
        ----------
        data : dict
            Data to save
        file_path : str or Path
            Output file path
        **kwargs
            Additional arguments for json.dump
        """
        path = self.validate_path(file_path, "write")

        safe_kwargs = {
            'indent': 2,
            'ensure_ascii': False,
            **kwargs
        }

        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, **safe_kwargs)
            info(f"Saved JSON to {path}")
        except Exception as e:
            error(f"Failed to save JSON {path}: {e}")
            raise SecurityError(f"Failed to save JSON file: {e}")


class InputValidator:
    """Input validation and sanitization utilities."""

    @staticmethod
    def validate_dataframe(data: pd.DataFrame,
                          required_columns: Optional[List[str]] = None,
                          min_rows: int = 1) -> Tuple[bool, List[str]]:
        """Validate DataFrame structure and content.

        Parameters
        ----------
        data : pd.DataFrame
            Data to validate
        required_columns : list, optional
            Required column names
        min_rows : int
            Minimum number of rows

        Returns
        -------
        tuple
            (is_valid, list_of_errors)
        """
        errors = []

        # Check if DataFrame is empty
        if data.empty:
            errors.append("DataFrame is empty")
            return False, errors

        # Check minimum rows
        if len(data) < min_rows:
            errors.append(f"DataFrame has {len(data)} rows, minimum required: {min_rows}")

        # Check required columns
        if required_columns:
            missing_cols = set(required_columns) - set(data.columns)
            if missing_cols:
                errors.append(f"Missing required columns: {missing_cols}")

        # Check for suspicious column names
        suspicious_patterns = ['__', 'eval', 'exec', 'import', 'os.', 'sys.']
        for col in data.columns:
            if any(pattern in str(col).lower() for pattern in suspicious_patterns):
                errors.append(f"Suspicious column name: {col}")

        return len(errors) == 0, errors

    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """Sanitize string input.

        Parameters
        ----------
        value : str
            String to sanitize
        max_length : int
            Maximum allowed length

        Returns
        -------
        str
            Sanitized string
        """
        if not isinstance(value, str):
            value = str(value)

        # Truncate if too long
        if len(value) > max_length:
            value = value[:max_length]
            warning(f"String truncated to {max_length} characters")

        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '&', '"', "'", '`', '\x00']
        for char in dangerous_chars:
            value = value.replace(char, '')

        return value.strip()

    @staticmethod
    def validate_string(value: str, max_length: int = 1000, pattern: Optional[str] = None) -> str:
        """Validate string input.

        Parameters
        ----------
        value : str
            String to validate
        max_length : int
            Maximum allowed length
        pattern : str, optional
            Regex pattern to match

        Returns
        -------
        str
            Validated string

        Raises
        ------
        ValueError
            If validation fails
        """
        if not isinstance(value, str):
            raise ValueError("Input must be a string")

        if len(value) > max_length:
            raise ValueError(f"String too long: {len(value)} > {max_length}")

        if pattern:
            import re
            if not re.match(pattern, value):
                raise ValueError(f"String does not match pattern: {pattern}")

        return value

    @staticmethod
    def validate_numeric(value, min_val: Optional[float] = None, max_val: Optional[float] = None):
        """Validate numeric input.

        Parameters
        ----------
        value
            Value to validate
        min_val : float, optional
            Minimum allowed value
        max_val : float, optional
            Maximum allowed value

        Returns
        -------
        float or int
            Validated numeric value

        Raises
        ------
        ValueError
            If validation fails
        """
        try:
            if isinstance(value, str):
                # Try to convert string to number
                if '.' in value:
                    num_val = float(value)
                else:
                    num_val = int(value)
            else:
                num_val = float(value)
        except (ValueError, TypeError):
            raise ValueError(f"Cannot convert to numeric: {value}")

        if min_val is not None and num_val < min_val:
            raise ValueError(f"Value {num_val} below minimum {min_val}")

        if max_val is not None and num_val > max_val:
            raise ValueError(f"Value {num_val} above maximum {max_val}")

        return num_val

    @staticmethod
    def validate_date_string(date_str: str, date_format: str = "%Y-%m-%d") -> str:
        """Validate date string.

        Parameters
        ----------
        date_str : str
            Date string to validate
        date_format : str
            Expected date format

        Returns
        -------
        str
            Validated date string

        Raises
        ------
        ValueError
            If date is invalid
        """
        from datetime import datetime

        try:
            datetime.strptime(date_str, date_format)
            return date_str
        except ValueError as e:
            raise ValueError(f"Invalid date: {e}")

    @staticmethod
    def sanitize_input(value: str) -> str:
        """Sanitize general input.

        Parameters
        ----------
        value : str
            Input to sanitize

        Returns
        -------
        str
            Sanitized input
        """
        if not isinstance(value, str):
            value = str(value)

        # Strip whitespace
        value = value.strip()

        # Remove HTML tags
        import re
        value = re.sub(r'<[^>]+>', '', value)

        # Escape special characters
        value = value.replace('&', '&amp;')
        value = value.replace('<', '&lt;')
        value = value.replace('>', '&gt;')
        value = value.replace('"', '&quot;')
        value = value.replace("'", '&#x27;')

        return value


class DataSanitizer:
    """Data cleaning and sanitization utilities."""

    @staticmethod
    def sanitize_dataframe(data: pd.DataFrame) -> pd.DataFrame:
        """Sanitize DataFrame content.

        Parameters
        ----------
        data : pd.DataFrame
            Data to sanitize

        Returns
        -------
        pd.DataFrame
            Sanitized data
        """
        data = data.copy()

        # Sanitize string columns
        for col in data.select_dtypes(include=['object']).columns:
            data[col] = data[col].apply(
                lambda x: InputValidator.sanitize_string(str(x)) if pd.notna(x) else x
            )

        # Handle infinite values
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        data[numeric_cols] = data[numeric_cols].replace([np.inf, -np.inf], np.nan)

        # Log sanitization results
        info(f"Sanitized DataFrame: {data.shape[0]} rows, {data.shape[1]} columns")

        return data

    @staticmethod
    def detect_outliers(data: pd.Series, method: str = "iqr",
                       threshold: float = 1.5) -> pd.Series:
        """Detect outliers in numeric data.

        Parameters
        ----------
        data : pd.Series
            Numeric data
        method : str
            Detection method ('iqr', 'zscore')
        threshold : float
            Threshold for outlier detection

        Returns
        -------
        pd.Series
            Boolean series indicating outliers
        """
        if method == "iqr":
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            return (data < lower_bound) | (data > upper_bound)

        elif method == "zscore":
            z_scores = np.abs((data - data.mean()) / data.std())
            return z_scores > threshold

        else:
            raise ValueError(f"Unknown outlier detection method: {method}")

    @staticmethod
    def clean_dataframe(data: pd.DataFrame, remove_duplicates: bool = True,
                       handle_missing: str = 'drop') -> pd.DataFrame:
        """Clean DataFrame by removing duplicates and handling missing values.

        Parameters
        ----------
        data : pd.DataFrame
            Data to clean
        remove_duplicates : bool
            Whether to remove duplicate rows
        handle_missing : str
            How to handle missing values ('drop', 'fill')

        Returns
        -------
        pd.DataFrame
            Cleaned data
        """
        cleaned = data.copy()

        # Remove duplicates
        if remove_duplicates:
            initial_rows = len(cleaned)
            cleaned = cleaned.drop_duplicates()
            removed = initial_rows - len(cleaned)
            if removed > 0:
                info(f"Removed {removed} duplicate rows")

        # Handle missing values
        if handle_missing == 'drop':
            initial_rows = len(cleaned)
            cleaned = cleaned.dropna()
            removed = initial_rows - len(cleaned)
            if removed > 0:
                info(f"Dropped {removed} rows with missing values")
        elif handle_missing == 'fill':
            # Fill numeric columns with median, categorical with mode
            for col in cleaned.columns:
                if cleaned[col].dtype in ['int64', 'float64']:
                    cleaned[col] = cleaned[col].fillna(cleaned[col].median())
                else:
                    mode_val = cleaned[col].mode()
                    if len(mode_val) > 0:
                        cleaned[col] = cleaned[col].fillna(mode_val[0])

        return cleaned

    @staticmethod
    def remove_outliers(data: pd.Series, method: str = "iqr",
                       threshold: float = 1.5) -> pd.Series:
        """Remove outliers from numeric data.

        Parameters
        ----------
        data : pd.Series
            Numeric data
        method : str
            Detection method ('iqr', 'zscore')
        threshold : float
            Threshold for outlier detection

        Returns
        -------
        pd.Series
            Data with outliers removed
        """
        outliers = DataSanitizer.detect_outliers(data, method, threshold)
        return data[~outliers]
