"""Performance optimization utilities for econometric analysis.

This module provides performance optimization tools for the Yemen Market Integration
project, including memory management, parallel processing, and computation optimization.

Classes:
    MemoryManager: Memory usage monitoring and optimization
    ParallelProcessor: Parallel processing utilities
    ComputationOptimizer: Optimization for numerical computations

Example:
    >>> from yemen_market.utils.performance import MemoryManager
    >>> with MemoryManager() as mm:
    ...     data = mm.optimize_dataframe(large_dataframe)
"""

import gc
import psutil
import time
import warnings
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Union, Callable
import pandas as pd
import numpy as np
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from functools import wraps
import multiprocessing as mp

from yemen_market.utils.logging import info, warning, error, bind, log_metric

# Set module context
bind(module=__name__)


class MemoryManager:
    """Memory usage monitoring and optimization utilities."""
    
    def __init__(self, warning_threshold: float = 0.8, critical_threshold: float = 0.9):
        """Initialize memory manager.
        
        Parameters
        ----------
        warning_threshold : float
            Memory usage threshold for warnings (0-1)
        critical_threshold : float
            Memory usage threshold for critical alerts (0-1)
        """
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.initial_memory = None
        
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics.
        
        Returns
        -------
        dict
            Memory usage statistics
        """
        process = psutil.Process()
        memory_info = process.memory_info()
        virtual_memory = psutil.virtual_memory()
        
        return {
            'process_rss_mb': memory_info.rss / 1024 / 1024,
            'process_vms_mb': memory_info.vms / 1024 / 1024,
            'system_used_percent': virtual_memory.percent / 100,
            'system_available_mb': virtual_memory.available / 1024 / 1024,
            'system_total_mb': virtual_memory.total / 1024 / 1024
        }
    
    def check_memory_usage(self) -> None:
        """Check memory usage and issue warnings if needed."""
        usage = self.get_memory_usage()
        
        if usage['system_used_percent'] > self.critical_threshold:
            error(f"Critical memory usage: {usage['system_used_percent']:.1%}")
        elif usage['system_used_percent'] > self.warning_threshold:
            warning(f"High memory usage: {usage['system_used_percent']:.1%}")
        
        log_metric("memory_usage_percent", usage['system_used_percent'])
        log_metric("process_memory_mb", usage['process_rss_mb'])
    
    def optimize_dataframe(self, df: pd.DataFrame, 
                          aggressive: bool = False) -> pd.DataFrame:
        """Optimize DataFrame memory usage.
        
        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to optimize
        aggressive : bool
            Whether to use aggressive optimization
            
        Returns
        -------
        pd.DataFrame
            Optimized DataFrame
        """
        original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
        
        # Optimize numeric columns
        for col in df.select_dtypes(include=[np.number]).columns:
            col_min = df[col].min()
            col_max = df[col].max()
            
            if df[col].dtype == 'int64':
                if col_min >= -128 and col_max <= 127:
                    df[col] = df[col].astype('int8')
                elif col_min >= -32768 and col_max <= 32767:
                    df[col] = df[col].astype('int16')
                elif col_min >= -2147483648 and col_max <= 2147483647:
                    df[col] = df[col].astype('int32')
            
            elif df[col].dtype == 'float64':
                if aggressive or (col_min >= np.finfo(np.float32).min and 
                                col_max <= np.finfo(np.float32).max):
                    df[col] = df[col].astype('float32')
        
        # Optimize object columns
        for col in df.select_dtypes(include=['object']).columns:
            if df[col].nunique() / len(df) < 0.5:  # Less than 50% unique values
                df[col] = df[col].astype('category')
        
        optimized_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
        reduction = (original_memory - optimized_memory) / original_memory * 100
        
        info(f"Memory optimization: {original_memory:.1f}MB → {optimized_memory:.1f}MB "
             f"({reduction:.1f}% reduction)")
        
        return df
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return statistics.
        
        Returns
        -------
        dict
            Garbage collection statistics
        """
        before_memory = self.get_memory_usage()['process_rss_mb']
        
        # Force garbage collection
        collected = gc.collect()
        
        after_memory = self.get_memory_usage()['process_rss_mb']
        freed_mb = before_memory - after_memory
        
        stats = {
            'objects_collected': collected,
            'memory_freed_mb': freed_mb,
            'memory_before_mb': before_memory,
            'memory_after_mb': after_memory
        }
        
        if freed_mb > 1:  # Only log if significant memory was freed
            info(f"Garbage collection freed {freed_mb:.1f}MB ({collected} objects)")
        
        return stats
    
    def __enter__(self):
        """Context manager entry."""
        self.initial_memory = self.get_memory_usage()
        info(f"Memory manager started - Initial usage: "
             f"{self.initial_memory['system_used_percent']:.1%}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        final_memory = self.get_memory_usage()
        change = (final_memory['system_used_percent'] - 
                 self.initial_memory['system_used_percent'])
        
        info(f"Memory manager finished - Final usage: "
             f"{final_memory['system_used_percent']:.1%} "
             f"(change: {change:+.1%})")


class ParallelProcessor:
    """Parallel processing utilities for econometric computations."""
    
    def __init__(self, n_jobs: Optional[int] = None, backend: str = "thread"):
        """Initialize parallel processor.
        
        Parameters
        ----------
        n_jobs : int, optional
            Number of parallel jobs. If None, uses CPU count.
        backend : str
            Parallel backend ('thread', 'process')
        """
        self.n_jobs = n_jobs or mp.cpu_count()
        self.backend = backend
        
        info(f"Initialized ParallelProcessor: {self.n_jobs} jobs, {backend} backend")
    
    def parallel_apply(self, func: Callable, data_chunks: List[Any], 
                      **kwargs) -> List[Any]:
        """Apply function to data chunks in parallel.
        
        Parameters
        ----------
        func : callable
            Function to apply
        data_chunks : list
            List of data chunks to process
        **kwargs
            Additional arguments for the function
            
        Returns
        -------
        list
            Results from parallel processing
        """
        executor_class = (ProcessPoolExecutor if self.backend == "process" 
                         else ThreadPoolExecutor)
        
        results = []
        
        with executor_class(max_workers=self.n_jobs) as executor:
            # Submit all tasks
            future_to_chunk = {
                executor.submit(func, chunk, **kwargs): i 
                for i, chunk in enumerate(data_chunks)
            }
            
            # Collect results in order
            chunk_results = [None] * len(data_chunks)
            
            for future in as_completed(future_to_chunk):
                chunk_idx = future_to_chunk[future]
                try:
                    result = future.result()
                    chunk_results[chunk_idx] = result
                except Exception as e:
                    error(f"Error processing chunk {chunk_idx}: {e}")
                    chunk_results[chunk_idx] = None
        
        return chunk_results
    
    def parallel_dataframe_apply(self, df: pd.DataFrame, func: Callable,
                                axis: int = 0, **kwargs) -> pd.DataFrame:
        """Apply function to DataFrame in parallel.
        
        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to process
        func : callable
            Function to apply
        axis : int
            Axis to split on (0 for rows, 1 for columns)
        **kwargs
            Additional arguments for the function
            
        Returns
        -------
        pd.DataFrame
            Processed DataFrame
        """
        # Split DataFrame into chunks
        if axis == 0:  # Split by rows
            chunk_size = max(1, len(df) // self.n_jobs)
            chunks = [df.iloc[i:i+chunk_size] for i in range(0, len(df), chunk_size)]
        else:  # Split by columns
            chunk_size = max(1, len(df.columns) // self.n_jobs)
            chunks = [df.iloc[:, i:i+chunk_size] for i in range(0, len(df.columns), chunk_size)]
        
        # Process chunks in parallel
        results = self.parallel_apply(func, chunks, **kwargs)
        
        # Combine results
        if axis == 0:
            return pd.concat([r for r in results if r is not None], ignore_index=True)
        else:
            return pd.concat([r for r in results if r is not None], axis=1)


class ComputationOptimizer:
    """Optimization utilities for numerical computations."""
    
    @staticmethod
    def optimize_numpy_operations():
        """Optimize NumPy operations for performance."""
        # Set optimal number of threads for NumPy operations
        import os
        
        # Use all available cores for NumPy operations
        n_cores = mp.cpu_count()
        
        os.environ['OMP_NUM_THREADS'] = str(n_cores)
        os.environ['MKL_NUM_THREADS'] = str(n_cores)
        os.environ['NUMEXPR_NUM_THREADS'] = str(n_cores)
        
        info(f"Optimized NumPy operations for {n_cores} cores")
    
    @staticmethod
    def enable_pandas_optimizations():
        """Enable pandas performance optimizations."""
        # Use string dtype for better performance
        pd.options.future.infer_string = True
        
        # Optimize display options
        pd.options.display.max_columns = 20
        pd.options.display.max_rows = 100
        
        # Enable copy-on-write for better memory management
        pd.options.mode.copy_on_write = True
        
        info("Enabled pandas performance optimizations")
    
    @staticmethod
    @contextmanager
    def suppress_warnings():
        """Context manager to suppress common warnings during computation."""
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", FutureWarning)
            warnings.simplefilter("ignore", UserWarning)
            warnings.simplefilter("ignore", RuntimeWarning)
            yield


def performance_monitor(func: Callable) -> Callable:
    """Decorator to monitor function performance.
    
    Parameters
    ----------
    func : callable
        Function to monitor
        
    Returns
    -------
    callable
        Decorated function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            execution_time = end_time - start_time
            memory_change = end_memory - start_memory
            
            log_metric(f"{func.__name__}_execution_time", execution_time)
            log_metric(f"{func.__name__}_memory_change_mb", memory_change)
            
            info(f"{func.__name__} completed in {execution_time:.2f}s "
                 f"(memory change: {memory_change:+.1f}MB)")
            
            return result
            
        except Exception as e:
            error(f"{func.__name__} failed after {time.time() - start_time:.2f}s: {e}")
            raise
    
    return wrapper


def batch_processor(batch_size: int = 1000):
    """Decorator for processing data in batches.
    
    Parameters
    ----------
    batch_size : int
        Size of each batch
        
    Returns
    -------
    callable
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(data: pd.DataFrame, *args, **kwargs):
            if len(data) <= batch_size:
                return func(data, *args, **kwargs)
            
            results = []
            n_batches = (len(data) + batch_size - 1) // batch_size
            
            info(f"Processing {len(data)} rows in {n_batches} batches of {batch_size}")
            
            for i in range(0, len(data), batch_size):
                batch = data.iloc[i:i+batch_size]
                batch_result = func(batch, *args, **kwargs)
                results.append(batch_result)
            
            # Combine results
            if isinstance(results[0], pd.DataFrame):
                return pd.concat(results, ignore_index=True)
            elif isinstance(results[0], dict):
                combined = {}
                for key in results[0].keys():
                    combined[key] = [r[key] for r in results]
                return combined
            else:
                return results
        
        return wrapper
    return decorator
