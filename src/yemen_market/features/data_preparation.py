"""Data preparation utilities for econometric analysis.

This module provides functions for preparing panel data for three-tier
econometric analysis, including:
- Data quality assessment
- Outlier treatment (winsorization)
- Stationarity testing
- Conflict regime definition
- Feature engineering
- Validation with flexible standards
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, List, Any
from scipy.stats import mstats
from scipy.spatial.distance import cdist
from statsmodels.tsa.stattools import adfuller
import json
from pathlib import Path

from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, bind, progress
)

# Set module context
bind(module=__name__)


def generate_data_quality_report(panel: pd.DataFrame) -> Dict[str, Any]:
    """Generate comprehensive data quality metrics.
    
    Parameters
    ----------
    panel : pd.DataFrame
        The panel DataFrame to analyze
        
    Returns
    -------
    dict
        Dictionary containing quality metrics including:
        - total_observations
        - date_range
        - n_markets
        - n_commodities
        - price_coverage
        - zero_prices
        - negative_prices
        - conflict_coverage (if available)
        - price_quantiles
        - extreme value counts
    """
    with timer("generate_data_quality_report"):
        info("Generating data quality report...")
        
        report = {
            'total_observations': len(panel),
            'date_range': f"{panel['date'].min()} to {panel['date'].max()}",
            'n_markets': panel['market'].nunique(),
            'n_commodities': panel['commodity'].nunique(),
            'price_coverage': f"{(panel['price'].notna().sum() / len(panel) * 100):.1f}%",
            'zero_prices': int((panel['price'] == 0).sum()),
            'negative_prices': int((panel['price'] < 0).sum()),
        }
        
        # Add USD price coverage if available
        if 'usdprice' in panel.columns:
            report['usd_price_coverage'] = f"{(panel['usdprice'].notna().sum() / len(panel) * 100):.1f}%"
        
        # Conflict data coverage
        if 'events_total' in panel.columns:
            report['conflict_coverage'] = f"{(panel['events_total'].notna().sum() / len(panel) * 100):.1f}%"
            report['total_conflict_events'] = int(panel['events_total'].sum())
        
        # Extreme values analysis
        if panel['price'].notna().any():
            price_quantiles = panel['price'].quantile([0.01, 0.05, 0.95, 0.99])
            report['price_quantiles'] = {
                '1%': f"{price_quantiles[0.01]:.2f}",
                '5%': f"{price_quantiles[0.05]:.2f}",
                '95%': f"{price_quantiles[0.95]:.2f}",
                '99%': f"{price_quantiles[0.99]:.2f}"
            }
            
            # Extreme value counts
            report['extreme_low_prices'] = int((panel['price'] < price_quantiles[0.01]).sum())
            report['extreme_high_prices'] = int((panel['price'] > price_quantiles[0.99]).sum())
        
        log_data_shape("quality_report", report)
        return report


def winsorize_prices(panel: pd.DataFrame, limits: Tuple[float, float] = (0.01, 0.01)) -> pd.DataFrame:
    """Winsorize extreme price values by commodity.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel DataFrame with price data
    limits : tuple of float, optional
        Tuple of (lower, upper) percentiles to winsorize
        Default is (0.01, 0.01) for 1% and 99% levels
        
    Returns
    -------
    pd.DataFrame
        Panel with winsorized price columns added:
        - price_winsorized
        - usdprice_winsorized (if usdprice exists)
    """
    with timer("winsorize_prices"):
        info(f"Winsorizing prices at {limits[0]*100}% and {100-limits[1]*100}% levels...")
        
        panel = panel.copy()
        
        # Winsorize by commodity to account for different price scales
        panel['price_winsorized'] = panel.groupby('commodity')['price'].transform(
            lambda x: mstats.winsorize(x.fillna(x.median()), limits=limits)
        )
        
        if 'usdprice' in panel.columns:
            panel['usdprice_winsorized'] = panel.groupby('commodity')['usdprice'].transform(
                lambda x: mstats.winsorize(x.fillna(x.median()), limits=limits)
            )
        
        # Report changes
        n_changed = (panel['price'] != panel['price_winsorized']).sum()
        info(f"Winsorized {n_changed} price observations ({n_changed/len(panel)*100:.1f}%)")
        
        return panel


def test_panel_stationarity(panel: pd.DataFrame, min_obs: int = 30) -> Tuple[Dict, Dict]:
    """Run panel unit root tests on price series.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel DataFrame with price data
    min_obs : int, optional
        Minimum observations required for testing (default: 30)
        
    Returns
    -------
    tuple
        (detailed_results, summary_statistics)
        - detailed_results: Dict with test results for each market-commodity pair
        - summary_statistics: Dict with overall summary
    """
    with timer("test_panel_stationarity"):
        info("Testing panel stationarity...")
        
        results = {}
        
        # Test each market-commodity series
        for (market, commodity), group in panel.groupby(['market', 'commodity']):
            price_series = group['price'].dropna()
            
            if len(price_series) >= min_obs:
                try:
                    adf_result = adfuller(price_series, autolag='AIC')
                    results[f"{market}_{commodity}"] = {
                        'stationary': adf_result[1] < 0.05,  # p-value < 0.05
                        'adf_stat': float(adf_result[0]),
                        'p_value': float(adf_result[1]),
                        'n_obs': len(price_series)
                    }
                except Exception as e:
                    warning(f"Could not test {market}-{commodity}: {e}")
        
        # Calculate summary statistics
        if results:
            stationary_count = sum(r['stationary'] for r in results.values())
            stationary_pct = stationary_count / len(results) * 100
            
            summary = {
                'total_tested': len(results),
                'stationary_count': stationary_count,
                'stationary_pct': f"{stationary_pct:.1f}%",
                'non_stationary_count': len(results) - stationary_count
            }
        else:
            summary = {'error': 'No series with sufficient observations'}
        
        info(f"Stationarity test complete: {summary.get('stationary_pct', 'N/A')} stationary")
        return results, summary


def define_conflict_regimes(panel: pd.DataFrame) -> pd.DataFrame:
    """Define conflict intensity regimes based on distribution.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel DataFrame with conflict data (events_total column)
        
    Returns
    -------
    pd.DataFrame
        Panel with additional columns:
        - conflict_regime: Categorical ('no_conflict', 'low', 'medium', 'high')
        - high_conflict: Binary indicator (1 if high conflict)
        - any_conflict: Binary indicator (1 if any conflict)
    """
    if 'events_total' not in panel.columns:
        warning("No conflict data available - skipping regime definition")
        return panel
    
    with timer("define_conflict_regimes"):
        info("Defining conflict regimes...")
        
        panel = panel.copy()
        
        # Calculate percentiles for non-zero conflict
        conflict_positive = panel[panel['events_total'] > 0]['events_total']
        
        if len(conflict_positive) > 0:
            q33 = conflict_positive.quantile(0.33)
            q67 = conflict_positive.quantile(0.67)
            
            # Create regime categories
            conditions = [
                panel['events_total'] == 0,
                (panel['events_total'] > 0) & (panel['events_total'] <= q33),
                (panel['events_total'] > q33) & (panel['events_total'] <= q67),
                panel['events_total'] > q67
            ]
            
            choices = ['no_conflict', 'low', 'medium', 'high']
            
            panel['conflict_regime'] = np.select(conditions, choices, default='unknown')
            
            # Binary indicators for regression
            panel['high_conflict'] = (panel['conflict_regime'] == 'high').astype(int)
            panel['any_conflict'] = (panel['events_total'] > 0).astype(int)
            
            # Report distribution
            regime_dist = panel['conflict_regime'].value_counts()
            info("Conflict regime distribution:")
            for regime, count in regime_dist.items():
                info(f"  {regime}: {count} ({count/len(panel)*100:.1f}%)")
            
            info(f"Thresholds: Low <= {q33:.0f}, Medium <= {q67:.0f}, High > {q67:.0f}")
        
        return panel


def add_econometric_features(panel: pd.DataFrame) -> pd.DataFrame:
    """Add features required for econometric analysis.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel DataFrame
        
    Returns
    -------
    pd.DataFrame
        Panel with additional econometric features:
        - log_price, log_usdprice: Log-transformed prices
        - time_trend: Linear time trend
        - month, quarter, year: Seasonal indicators
        - entity_id: Market-commodity identifier
        - events_total_lag1, events_total_lag2: Lagged conflict (if available)
    """
    with timer("add_econometric_features"):
        info("Adding econometric features...")
        
        panel = panel.copy()
        
        # Log prices (handling zeros)
        if 'price' in panel.columns:
            panel['log_price'] = np.log(panel['price'].clip(lower=0.01))
        
        if 'usdprice' in panel.columns:
            panel['log_usdprice'] = np.log(panel['usdprice'].clip(lower=0.01))
        
        # Time trend
        panel['date'] = pd.to_datetime(panel['date'])
        panel['time_trend'] = (panel['date'] - panel['date'].min()).dt.days / 30  # Months
        
        # Seasonal indicators
        panel['month'] = panel['date'].dt.month
        panel['quarter'] = panel['date'].dt.quarter
        panel['year'] = panel['date'].dt.year
        
        # Create entity identifier for panel models
        panel['entity_id'] = panel['market'] + '_' + panel['commodity']
        
        # Add lagged conflict if available
        if 'events_total' in panel.columns:
            panel = panel.sort_values(['entity_id', 'date'])
            panel['events_total_lag1'] = panel.groupby('entity_id')['events_total'].shift(1)
            panel['events_total_lag2'] = panel.groupby('entity_id')['events_total'].shift(2)
        
        log_data_shape("panel_with_features", panel)
        return panel


def validate_for_modeling(panel: pd.DataFrame, strict: bool = False) -> Dict[str, Any]:
    """Validate panel is ready for econometric modeling.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel DataFrame to validate
    strict : bool, optional
        Whether to use strict academic standards (default: False)
        If False, uses relaxed standards for policy work
        
    Returns
    -------
    dict
        Validation results with keys:
        - valid: bool indicating if panel is valid
        - errors: List of critical errors
        - warnings: List of non-critical issues
    """
    with timer("validate_for_modeling"):
        info(f"Validating panel for modeling (strict={strict})...")
        
        validation = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check required columns
        required_cols = ['date', 'market', 'commodity', 'price']
        missing_cols = [col for col in required_cols if col not in panel.columns]
        if missing_cols:
            validation['valid'] = False
            validation['errors'].append(f"Missing required columns: {missing_cols}")
        
        # Check panel balance
        obs_per_entity = panel.groupby(['market', 'commodity']).size()
        if obs_per_entity.nunique() > 1:
            validation['warnings'].append("Panel is not perfectly balanced")
        
        # Check for sufficient variation
        if 'price' in panel.columns:
            zero_variance = panel.groupby('entity_id')['price'].var() == 0
            if zero_variance.any():
                n_zero_var = zero_variance.sum()
                validation['warnings'].append(f"{n_zero_var} entities have zero price variance")
        
        # Data quality checks
        if strict:
            # Strict mode for academic work
            if panel['price'].isna().sum() / len(panel) > 0.05:
                validation['errors'].append("More than 5% missing prices")
                validation['valid'] = False
        else:
            # Relaxed mode for policy work
            if panel['price'].isna().sum() / len(panel) > 0.30:
                validation['warnings'].append("More than 30% missing prices")
        
        # Log validation results
        if validation['valid']:
            info("Panel validation passed")
        else:
            error("Panel validation failed")
        
        for err in validation['errors']:
            error(f"  ERROR: {err}")
        for warn in validation['warnings']:
            warning(f"  WARNING: {warn}")
        
        return validation


def save_prepared_data(panel: pd.DataFrame, output_dir: Path) -> None:
    """Save prepared panel data and metadata.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Prepared panel DataFrame
    output_dir : Path
        Output directory for saving files
    """
    with timer("save_prepared_data"):
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save panel
        panel.to_parquet(output_dir / "panel_prepared_for_modeling.parquet", index=False)
        panel.to_csv(output_dir / "panel_prepared_for_modeling.csv", index=False)
        
        # Save metadata
        metadata = {
            'preparation_date': pd.Timestamp.now().isoformat(),
            'n_observations': len(panel),
            'n_markets': panel['market'].nunique(),
            'n_commodities': panel['commodity'].nunique(),
            'date_range': f"{panel['date'].min()} to {panel['date'].max()}",
            'columns': list(panel.columns),
            'conflict_regimes': panel['conflict_regime'].value_counts().to_dict() if 'conflict_regime' in panel.columns else None
        }
        
        with open(output_dir / "preparation_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        info(f"Saved prepared data to {output_dir}")


def calculate_market_distances(markets_df: pd.DataFrame) -> pd.DataFrame:
    """Calculate distances between all market pairs.
    
    Parameters
    ----------
    markets_df : pd.DataFrame
        DataFrame with market locations (columns: market, latitude, longitude)
        
    Returns
    -------
    pd.DataFrame
        Distance matrix with markets as both index and columns
    """
    # Get unique markets with coordinates
    markets = markets_df[['market', 'latitude', 'longitude']].drop_duplicates()
    
    # Create coordinate matrix
    coords = markets[['latitude', 'longitude']].values
    
    # Calculate haversine distance matrix
    from math import radians, cos, sin, asin, sqrt
    
    def haversine(lat1, lon1, lat2, lon2):
        """Calculate the great circle distance between two points."""
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # Radius of earth in kilometers
        return c * r
    
    # Calculate distance matrix
    n_markets = len(markets)
    distances = np.zeros((n_markets, n_markets))
    
    for i in range(n_markets):
        for j in range(n_markets):
            if i != j:
                distances[i, j] = haversine(
                    coords[i, 0], coords[i, 1],
                    coords[j, 0], coords[j, 1]
                )
    
    # Create DataFrame
    distance_df = pd.DataFrame(
        distances,
        index=markets['market'].values,
        columns=markets['market'].values
    )
    
    return distance_df


def add_spatial_features(panel: pd.DataFrame, k: int = 3, 
                        distance_matrix: Optional[pd.DataFrame] = None) -> pd.DataFrame:
    """Add K-nearest neighbor spatial features to panel data.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel data with market, commodity, date, and price columns
    k : int, optional
        Number of nearest neighbors to consider (default: 3)
    distance_matrix : pd.DataFrame, optional
        Pre-calculated distance matrix. If None, will use market coordinates
        
    Returns
    -------
    pd.DataFrame
        Panel with spatial features added:
        - spatial_lag_price: Average price of k nearest neighbors
        - spatial_lag_conflict: Average conflict of k nearest neighbors
        - inverse_distance_weighted_price: Distance-weighted average price
        - spatial_price_deviation: Deviation from spatial average
    """
    with timer("add_spatial_features"):
        info(f"Adding spatial features with k={k} nearest neighbors...")
        
        panel = panel.copy()
        
        # Get or calculate distance matrix
        if distance_matrix is None:
            if 'latitude' in panel.columns and 'longitude' in panel.columns:
                distance_matrix = calculate_market_distances(panel)
            else:
                warning("No coordinates available for spatial features")
                return panel
        
        # Get unique markets
        markets = panel['market'].unique()
        
        # Find k-nearest neighbors for each market
        knn_dict = {}
        for market in markets:
            if market in distance_matrix.index:
                # Get distances to other markets
                distances = distance_matrix.loc[market].drop(market)
                # Find k nearest neighbors
                knn_dict[market] = distances.nsmallest(k).index.tolist()
            else:
                knn_dict[market] = []
        
        # Add spatial features by date and commodity
        spatial_features = []
        
        with progress("Computing spatial features", total=len(panel['date'].unique())) as update:
            for date in panel['date'].unique():
                for commodity in panel['commodity'].unique():
                    # Get data for this date-commodity
                    mask = (panel['date'] == date) & (panel['commodity'] == commodity)
                    date_comm_data = panel[mask].set_index('market')
                    
                    for market in date_comm_data.index:
                        neighbors = knn_dict.get(market, [])
                        
                        if neighbors:
                            # Get neighbor prices
                            neighbor_prices = []
                            neighbor_conflicts = []
                            weights = []
                            
                            for neighbor in neighbors:
                                if neighbor in date_comm_data.index:
                                    # Price
                                    price = date_comm_data.loc[neighbor, 'price']
                                    if pd.notna(price):
                                        neighbor_prices.append(price)
                                        # Weight by inverse distance
                                        dist = distance_matrix.loc[market, neighbor]
                                        if dist > 0:
                                            weights.append(1 / dist)
                                        else:
                                            weights.append(1)
                                    
                                    # Conflict (if available)
                                    if 'events_total' in date_comm_data.columns:
                                        conflict = date_comm_data.loc[neighbor, 'events_total']
                                        if pd.notna(conflict):
                                            neighbor_conflicts.append(conflict)
                            
                            # Calculate spatial features
                            spatial_row = {
                                'market': market,
                                'date': date,
                                'commodity': commodity,
                                'n_spatial_neighbors': len(neighbor_prices)
                            }
                            
                            if neighbor_prices:
                                # Simple average
                                spatial_row['spatial_lag_price'] = np.mean(neighbor_prices)
                                
                                # Weighted average
                                if weights:
                                    weights = np.array(weights) / np.sum(weights)
                                    spatial_row['inverse_distance_weighted_price'] = np.average(
                                        neighbor_prices, weights=weights
                                    )
                                
                                # Price deviation from spatial average
                                own_price = date_comm_data.loc[market, 'price']
                                if pd.notna(own_price):
                                    spatial_row['spatial_price_deviation'] = (
                                        own_price - spatial_row['spatial_lag_price']
                                    ) / spatial_row['spatial_lag_price'] * 100
                            
                            if neighbor_conflicts:
                                spatial_row['spatial_lag_conflict'] = np.mean(neighbor_conflicts)
                            
                            spatial_features.append(spatial_row)
                
                update(1)
        
        # Convert to DataFrame and merge
        if spatial_features:
            spatial_df = pd.DataFrame(spatial_features)
            
            # Merge with original panel
            merge_cols = ['market', 'date', 'commodity']
            panel = panel.merge(spatial_df, on=merge_cols, how='left')
            
            # Log statistics
            info(f"Added spatial features for {len(spatial_df)} observations")
            if 'spatial_lag_price' in panel.columns:
                coverage = panel['spatial_lag_price'].notna().sum() / len(panel) * 100
                info(f"Spatial price coverage: {coverage:.1f}%")
        else:
            warning("No spatial features could be calculated")
        
        log_data_shape("panel_with_spatial", panel)
        return panel


def add_exchange_rate_features(panel: pd.DataFrame) -> pd.DataFrame:
    """Add dual exchange rate system features.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel data with exchange rate information
        
    Returns
    -------
    pd.DataFrame
        Panel with exchange rate features:
        - er_premium: Premium of parallel over official rate (%)
        - er_premium_x_DFA: Interaction with DFA control zones
        - er_premium_ma3: 3-month moving average of premium
        - er_volatility: Rolling standard deviation of exchange rate
    """
    with timer("add_exchange_rate_features"):
        info("Adding exchange rate features...")
        
        panel = panel.copy()
        
        # Check for exchange rate data
        has_parallel = 'parallel_rate' in panel.columns
        has_official = 'official_rate' in panel.columns
        
        if not (has_parallel and has_official):
            # Try to derive from USD prices if available
            if 'price' in panel.columns and 'usd_price' in panel.columns:
                panel['implied_exchange_rate'] = panel['price'] / panel['usd_price']
                info("Derived exchange rate from price/usd_price ratio")
            else:
                warning("No exchange rate data available")
                return panel
        
        # Calculate exchange rate premium
        if has_parallel and has_official:
            panel['er_premium'] = (
                (panel['parallel_rate'] / panel['official_rate'] - 1) * 100
            )
            
            # Interaction with control zones
            if 'zone_DFA' in panel.columns:
                panel['er_premium_x_DFA'] = panel['er_premium'] * (panel['zone_DFA'] == 1)
                panel['er_premium_x_nonDFA'] = panel['er_premium'] * (panel['zone_DFA'] == 0)
            
            # Time-varying premium (3-month moving average)
            panel = panel.sort_values(['market', 'date'])
            panel['er_premium_ma3'] = panel.groupby('market')['er_premium'].transform(
                lambda x: x.rolling(3, min_periods=1).mean()
            )
            
            # Exchange rate volatility
            panel['er_volatility'] = panel.groupby('market')['parallel_rate'].transform(
                lambda x: x.pct_change().rolling(3, min_periods=2).std() * 100
            )
            
            # Log transformation of exchange rates
            panel['log_parallel_rate'] = np.log(panel['parallel_rate'].clip(lower=0.01))
            panel['log_official_rate'] = np.log(panel['official_rate'].clip(lower=0.01))
            
            # Report statistics
            info(f"Exchange rate premium: mean={panel['er_premium'].mean():.1f}%, "
                 f"max={panel['er_premium'].max():.1f}%")
        
        log_data_shape("panel_with_exchange_features", panel)
        return panel