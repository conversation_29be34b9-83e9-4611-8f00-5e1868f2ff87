"""Panel dataset builder for Yemen market integration analysis.

This module integrates all data sources (WFP prices, exchange rates, ACAPS control
zones, spatial mappings) into analysis-ready panel datasets for econometric modeling.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from ..config.settings import PROCESSED_DATA_DIR, ANALYSIS_CONFIG
from ..utils.logging import (
    bind, context, timer, progress, log_data_shape,
    info, warning, error, debug
)


class PanelBuilder:
    """Build integrated panel datasets for econometric analysis.
    
    This class handles:
    - Merging WFP price data with control zone mappings
    - Creating balanced panel structures
    - Calculating exchange rate differentials between zones
    - Adding temporal features and lags
    - Handling missing data appropriately
    - Creating model-specific datasets
    - Creating perfectly balanced panels for econometric analysis
    - Integrating conflict data, control zones, and geographic features
    
    Key Methods:
    - create_core_balanced_panel(): Create perfectly balanced panel (21×16×75)
    - integrate_panel_data(): Add conflict, control zones, and geographic data
    - load_balanced_panel(): Load pre-created balanced panel
    - load_integrated_panel(): Load fully integrated balanced panel
    - validate_balanced_panel(): Validate panel structure and quality
    
    Attributes:
        data_dir: Directory containing processed data
        output_dir: Directory for saving panel datasets
        commodities: List of commodities to include
        frequency: Panel frequency ('M' for monthly, 'W' for weekly)
    """
    
    def __init__(self,
                 data_dir: Optional[Path] = None,
                 output_dir: Optional[Path] = None,
                 commodities: Optional[List[str]] = None,
                 frequency: str = 'M'):
        """Initialize panel builder.
        
        Args:
            data_dir: Directory with processed data components
            output_dir: Output directory for panel datasets
            commodities: Commodities to include (None for all)
            frequency: Panel frequency ('M' or 'W')
        """
        self.data_dir = data_dir or PROCESSED_DATA_DIR
        self.output_dir = output_dir or PROCESSED_DATA_DIR / "panels"
        if commodities is None:
            # Get from ANALYSIS_CONFIG and convert to proper case
            config_commodities = ANALYSIS_CONFIG.get(
                'commodities', 
                ['Wheat', 'Wheat Flour', 'Rice (Imported)', 'Sugar', 'Oil (Vegetable)']
            )
            # Convert to match WFP data format
            self.commodities = []
            for c in config_commodities:
                # Handle special cases
                if c.lower() == "wheat flour":
                    self.commodities.append("Wheat Flour")
                elif c.lower() == "rice (imported)":
                    self.commodities.append("Rice (Imported)")
                elif c.lower() == "beans (kidney red)":
                    self.commodities.append("Beans (Kidney Red)")
                elif c.lower() == "beans (white)":
                    self.commodities.append("Beans (White)")
                elif c.lower() == "peas (yellow, split)":
                    self.commodities.append("Peas (Yellow, Split)")
                elif c.lower() == "meat (chicken)":
                    self.commodities.append("Meat (Chicken)")
                elif c.lower() == "meat (mutton)":
                    self.commodities.append("Meat (Mutton)")
                elif c.lower() == "oil (vegetable)":
                    self.commodities.append("Oil (Vegetable)")
                elif c.lower() == "oil (sunflower)":
                    self.commodities.append("Oil (Sunflower)")
                elif c.lower() == "fuel (diesel)":
                    self.commodities.append("Fuel (Diesel)")
                elif c.lower() == "fuel (petrol-gasoline)":
                    self.commodities.append("Fuel (Petrol-Gasoline)")
                elif c.lower() == "fuel (gas)":
                    self.commodities.append("Fuel (Gas)")
                else:
                    # Simple title case for others
                    self.commodities.append(c.title())
        else:
            self.commodities = commodities
        self.frequency = frequency
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        info("PanelBuilder initialized", 
             commodities=len(self.commodities),
             frequency=frequency)
    
    def load_component_data(self) -> Dict[str, pd.DataFrame]:
        """Load all component datasets.
        
        Returns:
            Dictionary of loaded datasets
        """
        with timer("load_component_data"):
            info("Loading component datasets")
        
        data = {}
        
        # Load WFP commodity price data
        commodity_price_path = self.data_dir / "wfp_commodity_prices.parquet"
        if commodity_price_path.exists():
            data['prices'] = pd.read_parquet(commodity_price_path)
            log_data_shape("commodity_price_data", data['prices'])
        else:
            # Fallback to panel data if commodity prices not available
            panel_path = self.data_dir / "wfp_market_panel.parquet"
            if panel_path.exists():
                data['prices'] = pd.read_parquet(panel_path)
                warning("Using aggregated panel data, commodity-level prices not available")
                log_data_shape("panel_data", data['prices'])
            else:
                raise FileNotFoundError(f"No price data found at {commodity_price_path} or {panel_path}")
        
        # Load exchange rate data - check multiple locations
        exchange_paths = [
            self.data_dir.parent / "interim" / "exchange_rates.parquet",  # Current location
            self.data_dir / "wfp" / "exchange_rates.parquet",  # Legacy location
        ]
        
        for exchange_path in exchange_paths:
            if exchange_path.exists():
                data['exchange_rates'] = pd.read_parquet(exchange_path)
                log_data_shape("exchange_rate_data", data['exchange_rates'])
                break
        
        # Load market-zone mappings
        spatial_path = self.data_dir / "spatial" / "market_zones_temporal.parquet"
        if spatial_path.exists():
            data['market_zones'] = pd.read_parquet(spatial_path)
            log_data_shape("market_zone_data", data['market_zones'])
        else:
            # Try current mapping if temporal not available
            current_path = self.data_dir / "spatial" / "market_zones_current.parquet"
            if current_path.exists():
                data['market_zones'] = pd.read_parquet(current_path)
                info("Using current market-zone mapping (no temporal data)")
        
        # Load control zone time series
        control_path = self.data_dir / "control_zones" / "control_zones_monthly.parquet"
        if control_path.exists():
            data['control_zones'] = pd.read_parquet(control_path)
            log_data_shape("control_zone_data", data['control_zones'])
        
        # Load conflict data
        conflict_path = self.data_dir / "conflict" / "conflict_metrics.parquet"
        if conflict_path.exists():
            data['conflict'] = pd.read_parquet(conflict_path)
            log_data_shape("conflict_data", data['conflict'])
            info("Loaded conflict metrics", 
                 markets=data['conflict']['market_id'].nunique(),
                 time_periods=data['conflict']['year_month'].nunique())
        else:
            warning("No conflict data found - threshold models may be limited")
        
        return data
    
    def create_price_panel(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Create commodity price panel dataset.
        
        Args:
            data: Dictionary of component datasets
            
        Returns:
            Panel DataFrame with prices and controls
        """
        with timer("create_price_panel"):
            info("Creating price panel dataset")
        
        # Start with price data
        prices = data['prices'].copy()
        
        # Filter commodities
        if self.commodities:
            prices = prices[prices['commodity'].isin(self.commodities)]
            info("Filtered commodities", records=len(prices), commodities=self.commodities)
        
        # Ensure date column
        if 'date' in prices.columns:
            prices['date'] = pd.to_datetime(prices['date'])
        
        # Add time dimensions
        prices['year'] = prices['date'].dt.year
        prices['month'] = prices['date'].dt.month
        prices['year_month'] = prices['date'].dt.to_period('M')
        
        # Merge with market zones
        if 'market_zones' in data:
            zones = data['market_zones'].copy()
            
            # Handle temporal vs static mapping
            if 'date' in zones.columns:
                # Temporal mapping - merge on market and date
                zones['year_month'] = pd.to_datetime(zones['date']).dt.to_period('M')
                
                panel = prices.merge(
                    zones[['market_id', 'year_month', 'control_zone', 
                          'market_governorate', 'market_district']],
                    left_on=['market_id', 'year_month'],
                    right_on=['market_id', 'year_month'],
                    how='left'
                )
            else:
                # Static mapping - merge on market only
                panel = prices.merge(
                    zones[['market_id', 'control_zone', 
                          'market_governorate', 'market_district']],
                    on='market_id',
                    how='left'
                )
            
            match_rate = panel['control_zone'].notna().sum() / len(panel) * 100
            info("Merged with control zones", 
                 matched=panel['control_zone'].notna().sum(),
                 total=len(panel),
                 match_rate=f"{match_rate:.1f}%")
        else:
            panel = prices
            warning("No market-zone mapping available")
        
        # Merge with conflict data
        if 'conflict' in data:
            conflict = data['conflict'].copy()
            
            # Convert year_month to period if it's a string
            if 'year_month' in conflict.columns and conflict['year_month'].dtype == 'object':
                conflict['year_month'] = pd.to_datetime(conflict['year_month']).dt.to_period('M')
            
            # Select key conflict variables for threshold models
            conflict_vars = [
                'market_id', 'year_month',
                'conflict_intensity', 'total_fatalities', 'n_battles',
                'n_explosions', 'n_violence_civilians',
                'conflict_intensity_lag1', 'conflict_intensity_lag2', 
                'conflict_intensity_lag3', 'conflict_ma3'
            ]
            
            # Only keep columns that exist
            conflict_vars = [col for col in conflict_vars if col in conflict.columns]
            conflict = conflict[conflict_vars]
            
            # Merge on market_id and year_month
            panel = panel.merge(
                conflict,
                on=['market_id', 'year_month'],
                how='left'
            )
            
            conflict_match_rate = panel['conflict_intensity'].notna().sum() / len(panel) * 100
            info("Merged with conflict data",
                 matched=panel['conflict_intensity'].notna().sum(),
                 total=len(panel),
                 match_rate=f"{conflict_match_rate:.1f}%")
        else:
            warning("No conflict data available for integration")
        
        # Sort panel
        panel = panel.sort_values(['commodity', 'market_id', 'date'])
        
        return panel
    
    def create_exchange_rate_panel(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Create exchange rate panel with zone differentials.
        
        Args:
            data: Dictionary of component datasets
            
        Returns:
            Exchange rate panel DataFrame
        """
        with timer("create_exchange_rate_panel"):
            info("Creating exchange rate panel with dual exchange rate indicators")
        
        if 'exchange_rates' not in data:
            warning("No exchange rate data available - attempting to derive from price data")
            return self._derive_exchange_rates_from_prices(data)
        
        rates = data['exchange_rates'].copy()
        
        # Handle different date formats
        if 'date' in rates.columns:
            rates['date'] = pd.to_datetime(rates['date'])
            rates['year_month'] = rates['date'].dt.to_period('M')
        elif 'year_month' in rates.columns and 'date' not in rates.columns:
            # If only year_month exists, keep it as is
            pass
        else:
            raise ValueError("Exchange rates must have either 'date' or 'year_month' column")
        
        # Create market_id if not present
        if 'market_id' not in rates.columns and 'governorate' in rates.columns and 'market_name' in rates.columns:
            rates['market_id'] = (rates['governorate'] + '_' + rates['market_name']).str.replace(' ', '_')
        
        # Merge with market zones
        if 'market_zones' in data:
            zones = data['market_zones'].copy()
            
            if 'date' in zones.columns:
                zones['year_month'] = pd.to_datetime(zones['date']).dt.to_period('M')
                exchange_panel = rates.merge(
                    zones[['market_id', 'year_month', 'control_zone']],
                    on=['market_id', 'year_month'],
                    how='left'
                )
            else:
                exchange_panel = rates.merge(
                    zones[['market_id', 'control_zone']],
                    on='market_id',
                    how='left'
                )
        else:
            exchange_panel = rates
        
        # Calculate zone-level exchange rates
        if 'control_zone' in exchange_panel.columns:
            zone_rates = exchange_panel.groupby(
                ['year_month', 'control_zone']
            )['exchange_rate'].agg(['mean', 'std', 'count']).reset_index()
            
            zone_rates.columns = ['year_month', 'control_zone', 
                                 'zone_exchange_rate', 'zone_rate_std', 'n_markets']
            
            # Calculate differentials
            info("Calculating exchange rate differentials between zones")
            
            # Get rates for main zones
            houthi_rates = zone_rates[zone_rates['control_zone'].str.lower() == 'houthi'][
                ['year_month', 'zone_exchange_rate']
            ].rename(columns={'zone_exchange_rate': 'houthi_rate'})
            
            gov_rates = zone_rates[zone_rates['control_zone'].str.lower() == 'government'][
                ['year_month', 'zone_exchange_rate']
            ].rename(columns={'zone_exchange_rate': 'gov_rate'})
            
            # Merge to calculate differentials
            differentials = houthi_rates.merge(gov_rates, on='year_month', how='outer')
            differentials['rate_differential'] = (
                differentials['houthi_rate'] - differentials['gov_rate']
            )
            differentials['rate_differential_pct'] = (
                differentials['rate_differential'] / differentials['gov_rate'] * 100
            )
            
            # Add back to panel
            exchange_panel = exchange_panel.merge(
                zone_rates[['year_month', 'control_zone', 'zone_exchange_rate']],
                on=['year_month', 'control_zone'],
                how='left'
            )
            
            exchange_panel = exchange_panel.merge(
                differentials[['year_month', 'rate_differential', 'rate_differential_pct']],
                on='year_month',
                how='left'
            )
        
        return exchange_panel
    
    def add_temporal_features(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Add temporal features for time series analysis.
        
        Args:
            panel: Input panel DataFrame
            
        Returns:
            Panel with additional temporal features
        """
        with timer("add_temporal_features"):
            info("Adding temporal features")
        
        # Ensure sorted by entity and time
        if 'commodity' in panel.columns:
            panel = panel.sort_values(['commodity', 'market_id', 'date'])
            group_cols = ['commodity', 'market_id']
        else:
            panel = panel.sort_values(['market_id', 'date'])
            group_cols = ['market_id']
        
        # Add lags for key variables
        lag_vars = []
        if 'price_usd' in panel.columns:
            lag_vars.append('price_usd')
        if 'parallel_rate' in panel.columns:
            lag_vars.append('parallel_rate')
        if 'rate_differential' in panel.columns:
            lag_vars.append('rate_differential')
        
        for var in lag_vars:
            for lag in [1, 2, 3]:
                panel[f'{var}_lag{lag}'] = panel.groupby(group_cols)[var].shift(lag)
                
            # Add differences
            panel[f'{var}_diff'] = panel.groupby(group_cols)[var].diff()
            panel[f'{var}_pct_change'] = panel.groupby(group_cols)[var].pct_change()
        
        # Add rolling statistics
        for var in lag_vars:
            # 3-month rolling average
            panel[f'{var}_ma3'] = panel.groupby(group_cols)[var].transform(
                lambda x: x.rolling(window=3, min_periods=1).mean()
            )
            
            # 3-month rolling std
            panel[f'{var}_std3'] = panel.groupby(group_cols)[var].transform(
                lambda x: x.rolling(window=3, min_periods=2).std()
            )
        
        # Add time trend
        panel['time_trend'] = panel.groupby(group_cols).cumcount() + 1
        
        # Add seasonal indicators
        panel['quarter'] = panel['date'].dt.quarter
        panel['is_ramadan'] = panel['month'].isin([3, 4, 5])  # Approximate
        
        # Add conflict intensity proxy (if control changes)
        if 'control_zone' in panel.columns:
            panel['is_contested'] = panel['control_zone'] == 'contested'
            
            # Count control changes in last 3 months
            if 'zone_changed' in panel.columns:
                panel['recent_instability'] = panel.groupby(group_cols)['zone_changed'].transform(
                    lambda x: x.rolling(window=3, min_periods=1).sum()
                )
        
        # Add conflict regime indicators based on methodology
        if 'conflict_intensity' in panel.columns:
            # Define conflict regimes (low, medium, high) using quantiles
            q33 = panel['conflict_intensity'].quantile(0.33)
            q67 = panel['conflict_intensity'].quantile(0.67)
            
            panel['conflict_regime'] = pd.cut(
                panel['conflict_intensity'],
                bins=[-np.inf, q33, q67, np.inf],
                labels=['low', 'medium', 'high']
            )
            
            # Binary indicators for regime switching models
            panel['high_conflict'] = (panel['conflict_intensity'] > q67).astype(int)
            panel['low_conflict'] = (panel['conflict_intensity'] <= q33).astype(int)
            
            info("Added conflict regime indicators",
                 low_threshold=f"{q33:.1f}",
                 high_threshold=f"{q67:.1f}")
        
        temporal_cols = [c for c in panel.columns if 'lag' in c or 'ma' in c or 'diff' in c]
        info("Temporal features added", 
             features=len(temporal_cols),
             types={'lags': len([c for c in temporal_cols if 'lag' in c]),
                    'moving_avg': len([c for c in temporal_cols if 'ma' in c]),
                    'differences': len([c for c in temporal_cols if 'diff' in c])})
        
        return panel
    
    def create_balanced_panel(self, panel: pd.DataFrame, 
                            min_obs_per_entity: int = 12) -> pd.DataFrame:
        """Create balanced panel by filling missing time periods.
        
        Args:
            panel: Input panel DataFrame
            min_obs_per_entity: Minimum observations required per entity
            
        Returns:
            Balanced panel DataFrame
        """
        with timer("create_balanced_panel"):
            info("Creating balanced panel structure", min_obs=min_obs_per_entity)
        
        # Identify panel structure
        if 'commodity' in panel.columns:
            entity_cols = ['commodity', 'market_id']
        else:
            entity_cols = ['market_id']
        
        # Get date range
        date_min = panel['date'].min()
        date_max = panel['date'].max()
        
        # Create all possible combinations
        # For monthly data, WFP uses 15th of each month
        if self.frequency == 'M':
            # Get the day from the actual data (should be 15)
            sample_day = panel['date'].dt.day.mode()[0] if not panel.empty else 15
            all_dates = pd.date_range(
                date_min.replace(day=sample_day), 
                date_max, 
                freq='MS'  # Month start
            )
            # Adjust to the specific day (e.g., 15th)
            all_dates = all_dates + pd.Timedelta(days=sample_day-1)
        else:
            all_dates = pd.date_range(date_min, date_max, freq=self.frequency)
        
        # Get unique entities
        entities = panel[entity_cols].drop_duplicates()
        
        # Create full index
        index_df = pd.MultiIndex.from_product(
            [entities[col].unique() for col in entity_cols] + [all_dates],
            names=entity_cols + ['date']
        ).to_frame(index=False)
        
        # Merge with actual data
        balanced = index_df.merge(
            panel,
            on=entity_cols + ['date'],
            how='left'
        )
        
        # Fill time-invariant variables
        time_invariant = ['market_name', 'governorate', 'district', 'lat', 'lon']
        for col in time_invariant:
            if col in balanced.columns:
                balanced[col] = balanced.groupby(entity_cols)[col].transform(lambda x: x.ffill())
                balanced[col] = balanced.groupby(entity_cols)[col].transform(lambda x: x.bfill())
        
        # Add year_month for consistency
        balanced['year_month'] = balanced['date'].dt.to_period('M')
        
        # Filter entities with sufficient observations
        entity_counts = balanced.groupby(entity_cols).size()
        valid_entities = entity_counts[entity_counts >= min_obs_per_entity].index
        
        if isinstance(valid_entities, pd.MultiIndex):
            mask = balanced.set_index(entity_cols).index.isin(valid_entities)
            balanced = balanced[mask].reset_index(drop=True)
        else:
            balanced = balanced[balanced[entity_cols[0]].isin(valid_entities)]
        
        info("Balanced panel created",
             observations=len(balanced),
             entities=len(valid_entities),
             time_periods=balanced['date'].nunique())
        
        return balanced
    
    def handle_missing_data(self, panel: pd.DataFrame,
                          price_method: str = 'interpolate',
                          rate_method: str = 'forward_fill') -> pd.DataFrame:
        """Handle missing data in panel.
        
        Args:
            panel: Panel DataFrame
            price_method: Method for missing prices ('interpolate', 'forward_fill')
            rate_method: Method for missing exchange rates
            
        Returns:
            Panel with handled missing data
        """
        with timer("handle_missing_data"):
            info("Handling missing data", price_method=price_method, rate_method=rate_method)
        
        # Track missing data
        missing_before = panel.isnull().sum()
        
        # Identify grouping columns
        if 'commodity' in panel.columns:
            group_cols = ['commodity', 'market_id']
        else:
            group_cols = ['market_id']
        
        # Handle prices
        if 'price_usd' in panel.columns:
            if price_method == 'interpolate':
                panel['price_usd'] = panel.groupby(group_cols)['price_usd'].transform(
                    lambda x: x.interpolate(method='linear', limit=2)
                )
            elif price_method == 'forward_fill':
                panel['price_usd'] = panel.groupby(group_cols)['price_usd'].transform(
                    lambda x: x.ffill(limit=2)
                )
        
        # Handle exchange rates
        if 'parallel_rate' in panel.columns:
            if rate_method == 'forward_fill':
                panel['parallel_rate'] = panel.groupby('market_id')['parallel_rate'].transform(
                    lambda x: x.ffill(limit=3)
                )
            
            # Fill remaining with zone average
            if 'control_zone' in panel.columns:
                zone_avg = panel.groupby(['year_month', 'control_zone'])['parallel_rate'].transform('mean')
                panel['parallel_rate'] = panel['parallel_rate'].fillna(zone_avg)
        
        # Handle control zones
        if 'control_zone' in panel.columns:
            # Forward fill control zones (assume persistence)
            panel['control_zone'] = panel.groupby('market_id')['control_zone'].transform(
                lambda x: x.ffill()
            )
            panel['control_zone'] = panel.groupby('market_id')['control_zone'].transform(
                lambda x: x.bfill()
            )
        
        # Report missing data handling
        missing_after = panel.isnull().sum()
        
        missing_summary = {}
        for col in missing_before.index:
            if missing_before[col] > 0:
                missing_summary[col] = {
                    'before': int(missing_before[col]),
                    'after': int(missing_after[col]),
                    'reduction': int(missing_before[col] - missing_after[col])
                }
        
        if missing_summary:
            info("Missing data handled", summary=missing_summary)
        
        return panel
    
    def create_model_specific_panels(self, base_panel: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Create model-specific panel datasets.
        
        Args:
            base_panel: Base integrated panel
            
        Returns:
            Dictionary of model-specific panels
        """
        with timer("create_model_specific_panels"):
            info("Creating model-specific panel datasets")
        
        panels = {}
        
        # 1. Price transmission panel (market pairs)
        if 'price_usd' in base_panel.columns:
            panels['price_transmission'] = self._create_price_transmission_panel(base_panel)
        
        # 2. Exchange rate pass-through panel
        if 'parallel_rate' in base_panel.columns:
            panels['exchange_passthrough'] = self._create_passthrough_panel(base_panel)
        
        # 3. Threshold cointegration panel
        panels['threshold_coint'] = self._create_threshold_panel(base_panel)
        
        # 4. Spatial panel
        if 'lat' in base_panel.columns and 'lon' in base_panel.columns:
            panels['spatial'] = self._create_spatial_panel(base_panel)
        
        return panels
    
    def _create_price_transmission_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """
        Create panel for price transmission analysis between market pairs.
        
        Implements World Bank methodology for spatial price transmission analysis,
        following Fackler & Goodwin (2001) and Conforti (2004).
        
        Parameters
        ----------
        panel : pd.DataFrame
            Base panel data
            
        Returns
        -------
        pd.DataFrame
            Market-pair panel with transmission metrics
        """
        from yemen_market.utils.logging import info, timer
        from scipy.spatial.distance import cdist
        from itertools import combinations
        
        with timer("create_price_transmission_panel"):
            info("Creating price transmission panel with market pairs")
            
            if 'commodity' not in panel.columns:
                warning("No commodity column - cannot create transmission panel")
                return pd.DataFrame()
            
            # Focus on major tradable commodities
            tradable_commodities = ['Wheat', 'Rice', 'Sugar', 'Cooking oil']
            panel_tradable = panel[panel['commodity'].isin(tradable_commodities)].copy()
            
            if panel_tradable.empty:
                warning("No tradable commodities found")
                return pd.DataFrame()
            
            # Get unique markets with location data
            if 'latitude' in panel.columns and 'longitude' in panel.columns:
                markets = panel_tradable[['market_id', 'governorate', 'latitude', 'longitude']].drop_duplicates()
                has_coords = True
            else:
                markets = panel_tradable[['market_id', 'governorate']].drop_duplicates()
                has_coords = False
            
            # Create market pairs
            market_pairs = []
            market_list = markets['market_id'].unique()
            
            for market1, market2 in combinations(market_list, 2):
                pair_info = {
                    'market1_id': market1,
                    'market2_id': market2,
                    'market_pair': f"{market1}_{market2}"
                }
                
                # Add distance if coordinates available
                if has_coords:
                    m1_data = markets[markets['market_id'] == market1].iloc[0]
                    m2_data = markets[markets['market_id'] == market2].iloc[0]
                    
                    # Calculate distance
                    coords1 = [[m1_data['latitude'], m1_data['longitude']]]
                    coords2 = [[m2_data['latitude'], m2_data['longitude']]]
                    distance_km = cdist(coords1, coords2, metric='euclidean')[0, 0] * 111  # Rough km conversion
                    
                    pair_info['distance_km'] = distance_km
                    pair_info['market1_gov'] = m1_data['governorate']
                    pair_info['market2_gov'] = m2_data['governorate']
                
                market_pairs.append(pair_info)
            
            pairs_df = pd.DataFrame(market_pairs)
            info(f"Created {len(pairs_df)} market pairs")
            
            # Now create time series for each market pair and commodity
            transmission_data = []
            
            for _, pair in pairs_df.iterrows():
                for commodity in tradable_commodities:
                    # Get price series for both markets
                    m1_prices = panel_tradable[
                        (panel_tradable['market_id'] == pair['market1_id']) & 
                        (panel_tradable['commodity'] == commodity)
                    ][['date', 'usd_price']].rename(columns={'usd_price': 'price1'})
                    
                    m2_prices = panel_tradable[
                        (panel_tradable['market_id'] == pair['market2_id']) & 
                        (panel_tradable['commodity'] == commodity)
                    ][['date', 'usd_price']].rename(columns={'usd_price': 'price2'})
                    
                    # Merge on date
                    pair_prices = m1_prices.merge(m2_prices, on='date', how='inner')
                    
                    if len(pair_prices) < 20:  # Need sufficient observations
                        continue
                    
                    # Calculate transmission metrics
                    pair_prices['price_ratio'] = pair_prices['price2'] / pair_prices['price1']
                    pair_prices['price_diff'] = pair_prices['price2'] - pair_prices['price1']
                    pair_prices['log_price_diff'] = np.log(pair_prices['price2']) - np.log(pair_prices['price1'])
                    
                    # Price correlation (rolling)
                    pair_prices['price_corr_30d'] = pair_prices['price1'].rolling(30).corr(pair_prices['price2'])
                    
                    # Price adjustment metrics
                    pair_prices['price1_pct_change'] = pair_prices['price1'].pct_change()
                    pair_prices['price2_pct_change'] = pair_prices['price2'].pct_change()
                    
                    # Granger causality preparation (lags)
                    for lag in range(1, 4):
                        pair_prices[f'price1_lag{lag}'] = pair_prices['price1'].shift(lag)
                        pair_prices[f'price2_lag{lag}'] = pair_prices['price2'].shift(lag)
                    
                    # Add pair info
                    for col, val in pair.items():
                        pair_prices[col] = val
                    pair_prices['commodity'] = commodity
                    
                    transmission_data.append(pair_prices)
            
            if not transmission_data:
                warning("No valid market pairs with sufficient data")
                return pd.DataFrame()
            
            # Combine all pairs
            transmission_panel = pd.concat(transmission_data, ignore_index=True)
            
            # Add additional features
            if 'control_zone' in panel.columns:
                # Add control zone info for both markets
                zone_map = panel[['market_id', 'control_zone']].drop_duplicates()
                zone_dict = dict(zip(zone_map['market_id'], zone_map['control_zone']))
                
                transmission_panel['market1_zone'] = transmission_panel['market1_id'].map(zone_dict)
                transmission_panel['market2_zone'] = transmission_panel['market2_id'].map(zone_dict)
                transmission_panel['same_zone'] = (
                    transmission_panel['market1_zone'] == transmission_panel['market2_zone']
                ).astype(int)
            
            # Calculate integration metrics
            transmission_panel['price_convergence'] = -np.abs(transmission_panel['log_price_diff'])
            
            info(f"Created transmission panel with {len(transmission_panel)} observations")
            info(f"Unique market pairs: {transmission_panel['market_pair'].nunique()}")
            
            return transmission_panel
    
    def _create_passthrough_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """
        Create panel for exchange rate pass-through analysis.
        
        Implements exchange rate pass-through methodology following
        Campa & Goldberg (2005) and Burstein & Gopinath (2014) for
        analyzing price responses to exchange rate movements.
        
        Parameters
        ----------
        panel : pd.DataFrame
            Base panel data
            
        Returns
        -------
        pd.DataFrame
            Panel with pass-through metrics
        """
        from yemen_market.utils.logging import info, timer
        
        with timer("create_passthrough_panel"):
            info("Creating exchange rate pass-through panel")
            
            # Check for required price data
            if 'usd_price' not in panel.columns:
                warning("No USD price data for pass-through analysis")
                return pd.DataFrame()
            
            # Identify exchange rate columns
            er_cols = []
            if 'parallel_rate' in panel.columns:
                er_cols.append('parallel_rate')
            if 'official_rate' in panel.columns:
                er_cols.append('official_rate')
            if 'exchange_rate' in panel.columns:
                er_cols.append('exchange_rate')
            
            if not er_cols:
                warning("No exchange rate data for pass-through analysis")
                return pd.DataFrame()
            
            # Base columns
            base_cols = ['date', 'market_id', 'usd_price']
            if 'commodity' in panel.columns:
                base_cols.append('commodity')
            if 'governorate' in panel.columns:
                base_cols.append('governorate')
            
            # Create pass-through panel
            passthrough = panel[base_cols + er_cols].copy()
            
            # Sort by entity and time
            sort_cols = ['market_id', 'date']
            if 'commodity' in passthrough.columns:
                sort_cols = ['commodity', 'market_id', 'date']
            passthrough = passthrough.sort_values(sort_cols)
            
            # Calculate log prices and exchange rates
            passthrough['log_price'] = np.log(passthrough['usd_price'])
            for er_col in er_cols:
                passthrough[f'log_{er_col}'] = np.log(passthrough[er_col])
            
            # Calculate changes (first differences of logs = growth rates)
            group_cols = ['market_id']
            if 'commodity' in passthrough.columns:
                group_cols = ['commodity', 'market_id']
            
            passthrough['dlog_price'] = passthrough.groupby(group_cols)['log_price'].diff()
            for er_col in er_cols:
                passthrough[f'dlog_{er_col}'] = passthrough.groupby(group_cols)[f'log_{er_col}'].diff()
            
            # Calculate pass-through elasticities (rolling windows)
            # Short-run (1-month), medium-run (3-month), long-run (12-month)
            windows = {'sr': 30, 'mr': 90, 'lr': 365}
            
            for er_col in er_cols:
                for period, window in windows.items():
                    # Rolling regression coefficient (simplified)
                    # In practice, would use rolling OLS
                    passthrough[f'passthrough_{er_col}_{period}'] = (
                        passthrough.groupby(group_cols).apply(
                            lambda x: x['dlog_price'].rolling(window, min_periods=window//2).corr(x[f'dlog_{er_col}']) *
                                     (x['dlog_price'].rolling(window, min_periods=window//2).std() / 
                                      x[f'dlog_{er_col}'].rolling(window, min_periods=window//2).std())
                        ).reset_index(level=list(range(len(group_cols))), drop=True)
                    )
            
            # Add control variables
            if 'control_zone' in panel.columns:
                passthrough['control_zone'] = panel['control_zone']
                
                # Zone-specific pass-through indicators
                zone_dummies = pd.get_dummies(passthrough['control_zone'], prefix='zone')
                for col in zone_dummies.columns:
                    passthrough[col] = zone_dummies[col]
            
            # Add time-varying controls
            if 'conflict_intensity' in panel.columns:
                passthrough['conflict_intensity'] = panel['conflict_intensity']
                passthrough['high_conflict'] = (passthrough['conflict_intensity'] > 50).astype(int)
            
            # Interaction terms for state-dependent pass-through
            if 'parallel_rate' in er_cols and 'official_rate' in er_cols:
                # Dual exchange rate indicator
                passthrough['dual_rate_gap'] = (
                    passthrough['parallel_rate'] - passthrough['official_rate']
                ) / passthrough['official_rate']
                
                passthrough['large_gap'] = (passthrough['dual_rate_gap'] > 0.1).astype(int)
                
                # Interaction with pass-through
                for period in windows.keys():
                    passthrough[f'passthrough_parallel_{period}_x_gap'] = (
                        passthrough[f'passthrough_parallel_rate_{period}'] * passthrough['large_gap']
                    )
            
            # Add commodity-specific indicators for heterogeneous pass-through
            if 'commodity' in passthrough.columns:
                # Tradability index (simplified)
                tradable_commodities = ['Wheat', 'Rice', 'Sugar', 'Cooking oil']
                passthrough['tradable'] = passthrough['commodity'].isin(tradable_commodities).astype(int)
                
                # Perishability index
                perishable_commodities = ['Tomatoes', 'Onions', 'Potatoes']
                passthrough['perishable'] = passthrough['commodity'].isin(perishable_commodities).astype(int)
            
            # Calculate asymmetric pass-through (appreciation vs depreciation)
            for er_col in er_cols:
                passthrough[f'{er_col}_depreciation'] = (passthrough[f'dlog_{er_col}'] > 0).astype(int)
                passthrough[f'{er_col}_appreciation'] = (passthrough[f'dlog_{er_col}'] < 0).astype(int)
                
                # Separate pass-through for appreciation and depreciation
                for period in windows.keys():
                    pt_col = f'passthrough_{er_col}_{period}'
                    if pt_col in passthrough.columns:
                        passthrough[f'{pt_col}_depreciation'] = (
                            passthrough[pt_col] * passthrough[f'{er_col}_depreciation']
                        )
                        passthrough[f'{pt_col}_appreciation'] = (
                            passthrough[pt_col] * passthrough[f'{er_col}_appreciation']
                        )
            
            # Add lagged variables for dynamic pass-through
            for lag in range(1, 4):
                passthrough[f'dlog_price_lag{lag}'] = passthrough.groupby(group_cols)['dlog_price'].shift(lag)
                for er_col in er_cols:
                    passthrough[f'dlog_{er_col}_lag{lag}'] = passthrough.groupby(group_cols)[f'dlog_{er_col}'].shift(lag)
            
            info(f"Created pass-through panel with {len(passthrough)} observations")
            info(f"Exchange rate variables: {er_cols}")
            
            return passthrough
    
    def _create_threshold_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """
        Create panel for threshold cointegration analysis.
        
        Implements threshold VECM panel structure following Hansen & Seo (2002)
        and Balke & Fomby (1997) for regime-switching cointegration analysis.
        
        Parameters
        ----------
        panel : pd.DataFrame
            Base panel data
            
        Returns
        -------
        pd.DataFrame
            Panel prepared for threshold models
        """
        from yemen_market.utils.logging import info, timer
        from statsmodels.tsa.stattools import adfuller
        
        with timer("create_threshold_panel"):
            info("Creating threshold cointegration panel")
            
            # Base variables always needed
            base_vars = ['date', 'market_id']
            if 'commodity' in panel.columns:
                base_vars.append('commodity')
            if 'year_month' in panel.columns:
                base_vars.append('year_month')
            
            # Price variables
            price_vars = []
            if 'usd_price' in panel.columns:
                price_vars.extend(['usd_price'])
            else:
                warning("No USD price data for threshold analysis")
                return pd.DataFrame()
            
            # Start with base panel
            threshold_panel = panel[base_vars + price_vars].copy()
            
            # Sort for time series operations
            sort_cols = ['market_id', 'date']
            if 'commodity' in threshold_panel.columns:
                sort_cols = ['commodity', 'market_id', 'date']
            threshold_panel = threshold_panel.sort_values(sort_cols)
            
            # Group columns for transformations
            group_cols = ['market_id']
            if 'commodity' in threshold_panel.columns:
                group_cols = ['commodity', 'market_id']
            
            # Create log prices
            threshold_panel['log_price'] = np.log(threshold_panel['usd_price'])
            
            # Create lags for VECM specification
            max_lags = 4  # Following standard practice
            for lag in range(1, max_lags + 1):
                threshold_panel[f'log_price_lag{lag}'] = threshold_panel.groupby(group_cols)['log_price'].shift(lag)
                threshold_panel[f'usd_price_lag{lag}'] = threshold_panel.groupby(group_cols)['usd_price'].shift(lag)
            
            # First differences
            threshold_panel['d_log_price'] = threshold_panel.groupby(group_cols)['log_price'].diff()
            threshold_panel['d_usd_price'] = threshold_panel.groupby(group_cols)['usd_price'].diff()
            
            # Lagged differences for VECM
            for lag in range(1, max_lags):
                threshold_panel[f'd_log_price_lag{lag}'] = threshold_panel.groupby(group_cols)['d_log_price'].shift(lag)
            
            # Error correction term (simplified - in practice would use cointegration residuals)
            # Using deviation from market average as proxy
            market_avg = threshold_panel.groupby('date')['log_price'].transform('mean')
            threshold_panel['ecm_term'] = threshold_panel['log_price'] - market_avg
            threshold_panel['ecm_term_lag1'] = threshold_panel.groupby(group_cols)['ecm_term'].shift(1)
            
            # Add exchange rate variables if available
            if 'parallel_rate' in panel.columns:
                threshold_panel['parallel_rate'] = panel['parallel_rate']
                threshold_panel['log_parallel_rate'] = np.log(threshold_panel['parallel_rate'])
                threshold_panel['d_log_parallel_rate'] = threshold_panel.groupby(group_cols)['log_parallel_rate'].diff()
                
                if 'official_rate' in panel.columns:
                    threshold_panel['official_rate'] = panel['official_rate']
                    threshold_panel['rate_differential'] = (
                        threshold_panel['parallel_rate'] - threshold_panel['official_rate']
                    )
                    threshold_panel['rate_premium'] = (
                        threshold_panel['rate_differential'] / threshold_panel['official_rate']
                    )
            
            # Add control zone information
            if 'control_zone' in panel.columns:
                threshold_panel['control_zone'] = panel['control_zone']
                threshold_panel['is_contested'] = panel.get('is_contested', 0)
            
            # Create threshold variables (potential regime indicators)
            threshold_candidates = []
            
            # 1. Conflict intensity as threshold variable
            if 'conflict_intensity' in panel.columns:
                threshold_panel['conflict_intensity'] = panel['conflict_intensity']
                threshold_candidates.append('conflict_intensity')
                
                # Add lags and moving averages
                for lag in range(1, 4):
                    threshold_panel[f'conflict_intensity_lag{lag}'] = threshold_panel.groupby(group_cols)['conflict_intensity'].shift(lag)
                
                # Moving averages for smoothing
                threshold_panel['conflict_ma3'] = threshold_panel.groupby(group_cols)['conflict_intensity'].rolling(3, min_periods=1).mean().reset_index(0, drop=True)
                threshold_panel['conflict_ma6'] = threshold_panel.groupby(group_cols)['conflict_intensity'].rolling(6, min_periods=1).mean().reset_index(0, drop=True)
                threshold_candidates.extend(['conflict_ma3', 'conflict_ma6'])
            
            # 2. Price volatility as threshold variable
            threshold_panel['price_volatility'] = threshold_panel.groupby(group_cols)['d_log_price'].rolling(12, min_periods=6).std().reset_index(0, drop=True)
            threshold_candidates.append('price_volatility')
            
            # 3. Exchange rate gap as threshold variable
            if 'rate_premium' in threshold_panel.columns:
                threshold_candidates.append('rate_premium')
            
            # 4. Market integration measure as threshold
            # Using absolute ECM term as proxy for market disconnection
            threshold_panel['market_disconnection'] = np.abs(threshold_panel['ecm_term'])
            threshold_candidates.append('market_disconnection')
            
            # Add other conflict-related variables if available
            if 'total_fatalities' in panel.columns:
                threshold_panel['total_fatalities'] = panel['total_fatalities']
                threshold_candidates.append('total_fatalities')
            
            if 'n_battles' in panel.columns:
                threshold_panel['n_battles'] = panel['n_battles']
                threshold_candidates.append('n_battles')
            
            # Create interaction terms for regime-dependent effects
            for threshold_var in threshold_candidates:
                if threshold_var in threshold_panel.columns:
                    # Create high/low regime indicators (will be refined by model)
                    threshold_panel[f'{threshold_var}_high'] = (
                        threshold_panel[threshold_var] > threshold_panel[threshold_var].median()
                    ).astype(int)
            
            # Add time trend and seasonality
            threshold_panel['time_trend'] = threshold_panel.groupby(group_cols).cumcount()
            threshold_panel['month'] = pd.to_datetime(threshold_panel['date']).dt.month
            
            # Create seasonal dummies
            for month in range(1, 13):
                threshold_panel[f'month_{month}'] = (threshold_panel['month'] == month).astype(int)
            
            # Add squared and cubed terms for nonlinear threshold effects
            for threshold_var in threshold_candidates[:3]:  # Limit to avoid too many variables
                if threshold_var in threshold_panel.columns:
                    threshold_panel[f'{threshold_var}_sq'] = threshold_panel[threshold_var] ** 2
                    threshold_panel[f'{threshold_var}_cube'] = threshold_panel[threshold_var] ** 3
            
            # Log available threshold variables
            available_thresholds = [v for v in threshold_candidates if v in threshold_panel.columns]
            info(f"Threshold variables available: {available_thresholds}")
            
            # Drop rows with too many missing values in critical variables
            critical_vars = ['log_price', 'log_price_lag1', 'd_log_price', 'ecm_term_lag1']
            threshold_panel = threshold_panel.dropna(subset=critical_vars)
            
            info(f"Created threshold panel with {len(threshold_panel)} observations")
            info(f"Potential threshold variables: {len(available_thresholds)}")
            
            return threshold_panel
    
    def _create_spatial_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel with spatial information."""
        spatial_vars = ['lat', 'lon', 'control_zone', 'distance_to_zone_km']
        available_spatial = [v for v in spatial_vars if v in panel.columns]
        
        return panel[['date', 'market_id'] + available_spatial + ['price_usd']].copy()
    
    def save_panels(self, panels: Dict[str, pd.DataFrame]) -> Dict[str, Path]:
        """Save all panel datasets.
        
        Args:
            panels: Dictionary of panel DataFrames
            
        Returns:
            Dictionary of saved file paths
        """
        saved_files = {}
        
        for panel_name, panel_df in panels.items():
            if panel_df.empty:
                warning(f"Skipping empty panel: {panel_name}")
                continue
            
            # Save as parquet
            parquet_path = self.output_dir / f"{panel_name}_panel.parquet"
            panel_df.to_parquet(parquet_path, index=False)
            saved_files[f'{panel_name}_parquet'] = parquet_path
            
            # Save sample as CSV for inspection
            csv_path = self.output_dir / f"{panel_name}_panel_sample.csv"
            panel_df.head(1000).to_csv(csv_path, index=False)
            saved_files[f'{panel_name}_csv'] = csv_path
            
            info(f"Saved {panel_name} panel", 
                 observations=len(panel_df),
                 path=str(parquet_path))
        
        # Save metadata
        metadata = {
            'created_date': datetime.now().isoformat(),
            'panels': list(panels.keys()),
            'commodities': self.commodities,
            'frequency': self.frequency,
            'observations': {name: len(df) for name, df in panels.items()},
            'date_range': {
                name: {
                    'start': str(df['date'].min()) if 'date' in df.columns else None,
                    'end': str(df['date'].max()) if 'date' in df.columns else None
                }
                for name, df in panels.items() if not df.empty
            }
        }
        
        import json
        metadata_path = self.output_dir / "panel_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        saved_files['metadata'] = metadata_path
        
        return saved_files
    
    def generate_panel_summary(self, panels: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Generate summary statistics for all panels.
        
        Args:
            panels: Dictionary of panel DataFrames
            
        Returns:
            Summary statistics DataFrame
        """
        summaries = []
        
        for panel_name, panel_df in panels.items():
            if panel_df.empty:
                continue
            
            summary = {
                'panel': panel_name,
                'n_observations': len(panel_df),
                'n_markets': panel_df['market_id'].nunique() if 'market_id' in panel_df.columns else 0,
                'n_commodities': panel_df['commodity'].nunique() if 'commodity' in panel_df.columns else 1,
                'date_range': f"{panel_df['date'].min()} to {panel_df['date'].max()}" if 'date' in panel_df.columns else 'N/A',
                'missing_price_pct': (panel_df['price_usd'].isnull().sum() / len(panel_df) * 100) if 'price_usd' in panel_df.columns else 0,
                'n_control_zones': panel_df['control_zone'].nunique() if 'control_zone' in panel_df.columns else 0,
                'has_conflict_data': 'conflict_intensity' in panel_df.columns,
                'avg_conflict_intensity': panel_df['conflict_intensity'].mean() if 'conflict_intensity' in panel_df.columns else None,
                'conflict_coverage_pct': (panel_df['conflict_intensity'].notna().sum() / len(panel_df) * 100) if 'conflict_intensity' in panel_df.columns else 0
            }
            
            summaries.append(summary)
        
        return pd.DataFrame(summaries)
    
    def _derive_exchange_rates_from_prices(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Derive implied exchange rates from dual-currency price data.
        
        This method implements World Bank methodology for inferring exchange rates
        from commodity prices when direct exchange rate data is unavailable.
        Uses the law of one price and purchasing power parity principles.
        
        Parameters
        ----------
        data : dict
            Dictionary containing price data
            
        Returns
        -------
        pd.DataFrame
            Derived exchange rate panel
        """
        from yemen_market.utils.logging import info, warning
        
        info("Deriving exchange rates from commodity price data")
        
        if 'prices' not in data:
            warning("No price data available to derive exchange rates")
            return pd.DataFrame()
        
        prices = data['prices'].copy()
        
        # Check if we have both USD and YER prices
        if 'price_usd' not in prices.columns or 'price_yer' not in prices.columns:
            warning("Need both USD and YER prices to derive exchange rates")
            return pd.DataFrame()
        
        # Calculate implied exchange rate for each observation
        prices['implied_exchange_rate'] = prices['price_yer'] / prices['price_usd']
        
        # Remove outliers using IQR method
        Q1 = prices['implied_exchange_rate'].quantile(0.25)
        Q3 = prices['implied_exchange_rate'].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        prices = prices[
            (prices['implied_exchange_rate'] >= lower_bound) & 
            (prices['implied_exchange_rate'] <= upper_bound)
        ]
        
        # Aggregate by market and time period
        if 'date' in prices.columns:
            prices['year_month'] = pd.to_datetime(prices['date']).dt.to_period('M')
        
        # Use tradable commodities (less affected by local factors)
        tradable_commodities = ['Wheat', 'Rice', 'Sugar', 'Cooking oil']
        tradable_mask = prices['commodity'].isin(tradable_commodities)
        
        # Calculate market-level exchange rates
        exchange_rates = prices[tradable_mask].groupby(['market_id', 'year_month']).agg({
            'implied_exchange_rate': ['mean', 'std', 'count']
        }).reset_index()
        
        exchange_rates.columns = ['market_id', 'year_month', 'exchange_rate', 'rate_std', 'n_obs']
        
        # Add control zone information if available
        if 'market_zones' in data:
            zones = data['market_zones'].copy()
            if 'year_month' not in zones.columns and 'date' in zones.columns:
                zones['year_month'] = pd.to_datetime(zones['date']).dt.to_period('M')
            
            exchange_rates = exchange_rates.merge(
                zones[['market_id', 'year_month', 'control_zone']].drop_duplicates(),
                on=['market_id', 'year_month'],
                how='left'
            )
        
        # Calculate zone-level rates and differentials
        if 'control_zone' in exchange_rates.columns:
            zone_rates = exchange_rates.groupby(['year_month', 'control_zone'])['exchange_rate'].agg([
                'mean', 'std', 'count'
            ]).reset_index()
            
            zone_rates.columns = ['year_month', 'control_zone', 'zone_rate', 'zone_std', 'n_markets']
            
            # Calculate dual exchange rate indicators
            pivot_rates = zone_rates.pivot(
                index='year_month',
                columns='control_zone',
                values='zone_rate'
            ).reset_index()
            
            # Calculate differentials for all zone pairs
            zone_names = [col for col in pivot_rates.columns if col != 'year_month']
            
            for i, zone1 in enumerate(zone_names):
                for zone2 in zone_names[i+1:]:
                    if zone1 in pivot_rates.columns and zone2 in pivot_rates.columns:
                        diff_col = f'{zone1}_{zone2}_differential'
                        pct_col = f'{zone1}_{zone2}_differential_pct'
                        
                        pivot_rates[diff_col] = pivot_rates[zone1] - pivot_rates[zone2]
                        pivot_rates[pct_col] = (pivot_rates[diff_col] / pivot_rates[zone2]) * 100
            
            # Merge back differentials
            exchange_rates = exchange_rates.merge(
                pivot_rates,
                on='year_month',
                how='left'
            )
            
            # Add volatility measures
            exchange_rates['rate_volatility'] = exchange_rates.groupby('market_id')['exchange_rate'].transform(
                lambda x: x.rolling(window=3, min_periods=1).std()
            )
            
            # Add persistence measure (AR(1) coefficient proxy)
            exchange_rates['rate_persistence'] = exchange_rates.groupby('market_id')['exchange_rate'].transform(
                lambda x: x.autocorr(lag=1) if len(x) > 1 else np.nan
            )
        
        # Add metadata
        exchange_rates['source'] = 'derived_from_prices'
        exchange_rates['derivation_method'] = 'tradable_commodities_ppp'
        
        info(f"Derived exchange rates for {len(exchange_rates)} market-month observations")
        
        return exchange_rates
    
    def load_balanced_panel(self, panel_type: str = 'filled') -> pd.DataFrame:
        """Load the pre-created balanced panel dataset.
        
        Args:
            panel_type: Type of panel to load ('filled' or 'raw')
            
        Returns:
            Balanced panel DataFrame
        """
        panel_dir = self.data_dir / "balanced_panels"
        
        if panel_type == 'filled':
            panel_path = panel_dir / "balanced_panel_filled.parquet"
        else:
            panel_path = panel_dir / "balanced_panel_raw.parquet"
            
        if not panel_path.exists():
            raise FileNotFoundError(
                f"Balanced panel not found at {panel_path}. "
                "Please run scripts/analysis/create_balanced_panel.py first."
            )
        
        panel = pd.read_parquet(panel_path)
        log_data_shape(f"balanced_panel_{panel_type}", panel)
        
        info(f"Loaded balanced panel",
             type=panel_type,
             observations=len(panel),
             markets=panel['market'].nunique() if 'market' in panel.columns else 'N/A',
             commodities=panel['commodity'].nunique() if 'commodity' in panel.columns else 'N/A')
        
        return panel
    
    def load_integrated_panel(self) -> pd.DataFrame:
        """Load the fully integrated balanced panel with conflict and control zone data.
        
        Returns:
            Integrated balanced panel DataFrame
        """
        panel_path = self.data_dir / "integrated_panel" / "yemen_integrated_balanced_panel.parquet"
        
        if not panel_path.exists():
            raise FileNotFoundError(
                f"Integrated panel not found at {panel_path}. "
                "Please run scripts/analysis/create_integrated_balanced_panel.py first."
            )
        
        panel = pd.read_parquet(panel_path)
        log_data_shape("integrated_balanced_panel", panel)
        
        # Report on key features
        info("Loaded integrated balanced panel",
             observations=len(panel),
             columns=len(panel.columns),
             markets=panel['market'].nunique() if 'market' in panel.columns else 'N/A',
             commodities=panel['commodity'].nunique() if 'commodity' in panel.columns else 'N/A')
        
        # Check key variable coverage
        key_vars = ['price', 'events_total', 'control_zone', 'latitude', 'longitude']
        coverage_info = {}
        for var in key_vars:
            if var in panel.columns:
                coverage = panel[var].notna().sum() / len(panel) * 100
                coverage_info[var] = f"{coverage:.1f}%"
        
        info("Key variable coverage", **coverage_info)
        
        return panel
    
    def validate_balanced_panel(self, panel: pd.DataFrame) -> Dict[str, Any]:
        """Validate the balanced panel structure and quality.
        
        Args:
            panel: Panel DataFrame to validate
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {}
        
        # Check balance
        if 'market' in panel.columns and 'commodity' in panel.columns:
            obs_per_entity = panel.groupby(['market', 'commodity']).size()
            is_balanced = obs_per_entity.nunique() == 1
            
            validation_results['is_balanced'] = is_balanced
            validation_results['obs_per_entity'] = {
                'min': obs_per_entity.min(),
                'max': obs_per_entity.max(),
                'mean': obs_per_entity.mean()
            }
        
        # Check missing data
        missing_pct = panel.isnull().sum() / len(panel) * 100
        validation_results['missing_data'] = missing_pct.to_dict()
        
        # Check time coverage
        if 'date' in panel.columns:
            validation_results['date_range'] = {
                'start': str(panel['date'].min()),
                'end': str(panel['date'].max()),
                'n_periods': panel['date'].nunique()
            }
        
        # Check conflict data
        if 'events_total' in panel.columns:
            conflict_coverage = panel['events_total'].notna().sum() / len(panel) * 100
            validation_results['conflict_coverage'] = f"{conflict_coverage:.1f}%"
        
        # Check spatial data
        if 'latitude' in panel.columns and 'longitude' in panel.columns:
            geo_coverage = panel[['latitude', 'longitude']].notna().all(axis=1).sum() / len(panel) * 100
            validation_results['geographic_coverage'] = f"{geo_coverage:.1f}%"
        
        # Check control zones
        if 'control_zone' in panel.columns:
            zone_coverage = panel['control_zone'].notna().sum() / len(panel) * 100
            validation_results['control_zone_coverage'] = f"{zone_coverage:.1f}%"
            validation_results['control_zones'] = panel['control_zone'].dropna().unique().tolist()
        
        info("Panel validation complete", **validation_results)
        
        return validation_results
    
    def create_core_balanced_panel(self, 
                                   min_coverage_pct: float = 85.0,
                                   min_markets: int = 20,
                                   min_commodities: int = 15) -> pd.DataFrame:
        """Create a perfectly balanced panel by selecting core markets and commodities.
        
        This method implements the logic from create_balanced_panel.py script.
        
        Args:
            min_coverage_pct: Minimum coverage percentage for selection
            min_markets: Minimum number of markets for a commodity
            min_commodities: Minimum number of commodities for a market
            
        Returns:
            Perfectly balanced panel DataFrame
        """
        with timer("create_core_balanced_panel"):
            info("Creating perfectly balanced panel dataset")
            
            # Read raw data
            raw_data_path = self.data_dir.parent / "raw/hdx/wfp-food-prices-for-yemen/wfp_food_prices_202505.csv"
            if not raw_data_path.exists():
                raise FileNotFoundError(f"Raw WFP data not found at {raw_data_path}")
            
            df = pd.read_csv(raw_data_path, skiprows=[1])
            df['date'] = pd.to_datetime(df['date'])
            
            # Filter for project period and exclude non-commodity items
            df = df[df['date'] >= '2019-01-01']
            df = df[~df['commodity'].str.contains('Exchange rate|Wage|Milling cost|Livestock', case=False)]
            
            info(f"Initial data: {len(df):,} observations")
            
            # Convert to monthly frequency
            df['year_month'] = df['date'].dt.to_period('M')
            
            # Step 1: Select core commodities
            info("Selecting core commodities...")
            commodity_stats = df.groupby('commodity').agg({
                'market': 'nunique',
                'year_month': 'nunique',
                'date': 'count'
            }).rename(columns={'market': 'n_markets', 'year_month': 'n_months', 'date': 'n_obs'})
            
            total_months = df['year_month'].nunique()
            commodity_stats['coverage_pct'] = (
                commodity_stats['n_obs'] / (commodity_stats['n_markets'] * total_months) * 100
            ).round(1)
            
            core_commodities = commodity_stats[
                (commodity_stats['n_markets'] >= min_markets) & 
                (commodity_stats['coverage_pct'] >= min_coverage_pct)
            ].index.tolist()
            
            info(f"Selected {len(core_commodities)} core commodities")
            
            # Step 2: Select core markets
            info("Selecting core markets...")
            df_core = df[df['commodity'].isin(core_commodities)]
            
            market_stats = df_core.groupby('market').agg({
                'commodity': 'nunique',
                'year_month': 'nunique',
                'date': 'count'
            }).rename(columns={'commodity': 'n_commodities', 'year_month': 'n_months', 'date': 'n_obs'})
            
            market_stats['expected_obs'] = market_stats['n_commodities'] * total_months
            market_stats['coverage_pct'] = (market_stats['n_obs'] / market_stats['expected_obs'] * 100).round(1)
            
            core_markets = market_stats[
                (market_stats['n_commodities'] >= min_commodities) &
                (market_stats['coverage_pct'] >= min_coverage_pct)
            ].index.tolist()
            
            info(f"Selected {len(core_markets)} core markets")
            
            # Step 3: Create balanced panel structure
            info("Creating balanced panel structure...")
            all_months = pd.period_range(
                start=df['year_month'].min(),
                end=df['year_month'].max(),
                freq='M'
            )
            
            from itertools import product
            balanced_index = pd.DataFrame(
                list(product(core_markets, core_commodities, all_months)),
                columns=['market', 'commodity', 'year_month']
            )
            
            # Step 4: Merge with actual data
            df_filtered = df[
                (df['market'].isin(core_markets)) & 
                (df['commodity'].isin(core_commodities))
            ].copy()
            
            price_data = df_filtered.groupby(['market', 'commodity', 'year_month']).agg({
                'price': 'mean',
                'usdprice': 'mean',
                'admin1': 'first',
                'admin2': 'first',
                'latitude': 'first',
                'longitude': 'first',
                'market_id': 'first'
            }).reset_index()
            
            balanced_panel = balanced_index.merge(
                price_data,
                on=['market', 'commodity', 'year_month'],
                how='left'
            )
            
            # Add time variables
            balanced_panel['date'] = balanced_panel['year_month'].dt.to_timestamp()
            balanced_panel['year'] = balanced_panel['date'].dt.year
            balanced_panel['month'] = balanced_panel['date'].dt.month
            
            # Fill missing location data
            location_cols = ['admin1', 'admin2', 'latitude', 'longitude', 'market_id']
            for col in location_cols:
                balanced_panel[col] = balanced_panel.groupby('market')[col].transform(
                    lambda x: x.ffill().bfill()
                )
            
            # Handle missing prices with interpolation
            balanced_panel = balanced_panel.sort_values(['market', 'commodity', 'date'])
            
            price_cols = ['price', 'usdprice']
            for col in price_cols:
                # Linear interpolation with limit of 2 months
                balanced_panel[col] = balanced_panel.groupby(['market', 'commodity'])[col].transform(
                    lambda x: x.interpolate(method='linear', limit=2)
                )
                # Forward/backward fill for remaining gaps (with limit)
                balanced_panel[col] = balanced_panel.groupby(['market', 'commodity'])[col].transform(
                    lambda x: x.ffill(limit=3).bfill(limit=3)
                )
            
            # Add commodity type classification
            commodity_types = {
                'Wheat': 'cereals',
                'Wheat flour': 'cereals',
                'Rice (imported)': 'cereals',
                'Beans (kidney red)': 'pulses',
                'Beans (white)': 'pulses',
                'Lentils': 'pulses',
                'Peas (yellow, split)': 'pulses',
                'Oil (vegetable)': 'oil',
                'Sugar': 'other',
                'Salt': 'other',
                'Eggs': 'protein',
                'Onions': 'vegetables',
                'Tomatoes': 'vegetables',
                'Potatoes': 'vegetables',
                'Fuel (diesel)': 'fuel',
                'Fuel (gas)': 'fuel',
                'Fuel (petrol-gasoline)': 'fuel'
            }
            
            balanced_panel['commodity_type'] = balanced_panel['commodity'].map(commodity_types)
            
            # Add import dependency flag
            imported_commodities = ['Rice (imported)', 'Wheat', 'Wheat flour', 'Sugar', 'Oil (vegetable)']
            balanced_panel['is_imported'] = balanced_panel['commodity'].isin(imported_commodities).astype(int)
            
            info(f"Created balanced panel with {len(balanced_panel):,} observations")
            info(f"Missing prices: {balanced_panel['price'].isna().sum()} ({balanced_panel['price'].isna().sum()/len(balanced_panel)*100:.1f}%)")
            
            return balanced_panel
    
    def integrate_panel_data(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Integrate conflict, control zones, and geographic data into panel.
        
        This method implements the logic from create_integrated_balanced_panel.py script.
        
        Args:
            panel: Base panel DataFrame
            
        Returns:
            Integrated panel with all additional data
        """
        with timer("integrate_panel_data"):
            info("Integrating conflict, control zones, and geographic data")
            
            # Ensure date columns
            if 'date' not in panel.columns and 'year_month' in panel.columns:
                panel['date'] = pd.to_datetime(panel['year_month'].astype(str))
            panel['year_month'] = panel['date'].dt.to_period('M')
            
            # Load and integrate conflict data
            panel = self._integrate_conflict_data(panel)
            
            # Load and integrate control zones
            panel = self._integrate_control_zones(panel)
            
            # Add geographic features
            panel = self._add_geographic_features(panel)
            
            # Add derived features
            panel = self._add_derived_econometric_features(panel)
            
            info(f"Integration complete. Final panel: {len(panel):,} observations, {len(panel.columns)} columns")
            
            return panel
    
    def _integrate_conflict_data(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Integrate ACLED conflict metrics into panel."""
        conflict_path = self.data_dir / "conflict/conflict_metrics.parquet"
        if not conflict_path.exists():
            warning(f"Conflict data not found at {conflict_path}")
            return panel
        
        conflict = pd.read_parquet(conflict_path)
        conflict['year_month'] = pd.to_datetime(conflict['year_month']).dt.to_period('M')
        
        # Create market_id in panel to match conflict data format
        panel['market_id'] = panel['admin1'].str.replace(' ', '_') + '_' + panel['market'].str.replace(' ', '_')
        
        # Select and rename conflict columns
        conflict_cols = ['market_id', 'year_month', 'n_events', 'n_battles', 'n_explosions',
                         'n_violence_civilians', 'total_fatalities', 'avg_distance_km',
                         'conflict_intensity', 'conflict_ma3']
        
        conflict_renamed = conflict[conflict_cols].rename(columns={
            'n_events': 'events_total',
            'n_battles': 'events_battles',
            'n_explosions': 'events_explosions',
            'n_violence_civilians': 'events_against_civilians',
            'total_fatalities': 'fatalities_total'
        })
        
        panel = panel.merge(conflict_renamed, on=['market_id', 'year_month'], how='left')
        
        # Fill missing conflict data with zeros
        conflict_numeric_cols = ['events_total', 'events_battles', 'events_explosions', 
                               'events_against_civilians', 'fatalities_total', 'avg_distance_km', 
                               'conflict_ma3']
        for col in conflict_numeric_cols:
            if col in panel.columns:
                panel[col] = panel[col].fillna(0)
        
        if 'conflict_intensity' in panel.columns:
            panel['conflict_intensity'] = panel['conflict_intensity'].astype(str).replace('nan', 'none')
        
        info(f"Integrated conflict data. Coverage: {(panel['events_total'] > 0).sum() / len(panel) * 100:.1f}%")
        
        return panel
    
    def _integrate_control_zones(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Integrate ACAPS control zone data into panel."""
        zones_path = self.data_dir / "spatial/market_zones_temporal.parquet"
        if not zones_path.exists():
            warning(f"Control zone data not found at {zones_path}")
            return panel
        
        zones = pd.read_parquet(zones_path)
        zones['date'] = pd.to_datetime(zones['date'])
        zones['year_month'] = zones['date'].dt.to_period('M')
        
        # Get the most recent control zone for each market
        zones_latest = zones.sort_values('date').groupby('market_name').last().reset_index()
        zones_latest = zones_latest.rename(columns={
            'market_name': 'market',
            'lat': 'latitude_zone',
            'lon': 'longitude_zone'
        })
        
        zone_cols = ['market', 'control_zone', 'zone_changed', 'latitude_zone', 'longitude_zone']
        panel = panel.merge(zones_latest[zone_cols], on='market', how='left')
        
        # Use zone coordinates to fill missing coordinates
        if 'latitude' in panel.columns:
            panel['latitude'] = panel['latitude'].fillna(panel['latitude_zone'])
            panel['longitude'] = panel['longitude'].fillna(panel['longitude_zone'])
        
        panel = panel.drop(['latitude_zone', 'longitude_zone'], axis=1)
        
        # Create zone dummies
        if 'control_zone' in panel.columns:
            zone_dummies = pd.get_dummies(panel['control_zone'], prefix='zone')
            panel = pd.concat([panel, zone_dummies], axis=1)
            
            info(f"Integrated control zone data. Coverage: {(panel['control_zone'].notna().sum() / len(panel) * 100):.1f}%")
        
        return panel
    
    def _add_geographic_features(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Add geographic features to panel."""
        # Load geographic data
        geo_path = self.data_dir / "spatial/market_zones_current.parquet"
        if geo_path.exists():
            geo = pd.read_parquet(geo_path)
            geo = geo.rename(columns={
                'market_name': 'market',
                'lat': 'latitude',
                'lon': 'longitude',
                'market_governorate': 'admin1_name',
                'market_district': 'admin2_name'
            })
            
            geo_cols = ['market', 'latitude', 'longitude', 'admin1_name', 'admin2_name']
            available_cols = [col for col in geo_cols if col in geo.columns]
            geo = geo[available_cols].drop_duplicates()
            
            panel = panel.merge(geo, on='market', how='left', suffixes=('', '_geo'))
            
            # Fill missing values
            for col in ['latitude', 'longitude']:
                if f'{col}_geo' in panel.columns:
                    panel[col] = panel[col].fillna(panel[f'{col}_geo'])
                    panel.drop(f'{col}_geo', axis=1, inplace=True)
        
        # Calculate distance to capital (Sana'a)
        if 'latitude' in panel.columns and 'longitude' in panel.columns:
            sana_lat, sana_lon = 15.3694, 44.1910
            
            # Haversine distance
            R = 6371  # Earth radius in km
            lat1 = np.radians(panel['latitude'])
            lat2 = np.radians(sana_lat)
            lon1 = np.radians(panel['longitude'])
            lon2 = np.radians(sana_lon)
            
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
            c = 2 * np.arcsin(np.sqrt(a))
            panel['distance_to_capital_km'] = R * c
        
        # Add port access indicator
        port_cities = ['Aden City', 'Mukalla City', 'Al Hudaydah']
        panel['has_port_access'] = panel['market'].isin(port_cities).astype(int)
        
        return panel
    
    def _add_derived_econometric_features(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Add econometric features for analysis."""
        # Sort for proper lagging
        panel = panel.sort_values(['market', 'commodity', 'date'])
        
        # Price changes and volatility
        if 'price' in panel.columns:
            panel['price_log'] = np.log(panel['price'] + 1)  # Add 1 to handle any zeros
            panel['price_change'] = panel.groupby(['market', 'commodity'])['price_log'].diff()
            panel['price_change_pct'] = panel.groupby(['market', 'commodity'])['price'].pct_change()
            
            # Rolling volatility
            panel['price_volatility'] = panel.groupby(['market', 'commodity'])['price_change'].transform(
                lambda x: x.rolling(window=3, min_periods=2).std()
            )
        
        # Conflict lags
        conflict_vars = ['events_total', 'fatalities_total']
        for var in conflict_vars:
            if var in panel.columns:
                panel[f'{var}_lag1'] = panel.groupby('market')[var].shift(1)
                panel[f'{var}_ma3'] = panel.groupby('market')[var].transform(
                    lambda x: x.rolling(window=3, min_periods=1).mean()
                )
        
        # Time trends
        if 'date' in panel.columns:
            panel['time_trend'] = (panel['date'] - panel['date'].min()).dt.days / 30
            panel['time_trend_sq'] = panel['time_trend'] ** 2
        
        # Seasonal indicators
        if 'month' in panel.columns:
            panel['quarter'] = panel['date'].dt.quarter
            panel['is_ramadan'] = ((panel['month'] >= 3) & (panel['month'] <= 5)).astype(int)
        
        # Market integration proxy
        if 'price' in panel.columns:
            panel['price_cv'] = panel.groupby(['commodity', 'year_month'])['price'].transform(
                lambda x: x.std() / x.mean() if x.mean() > 0 else np.nan
            )
        
        return panel
    
    def save_balanced_panels(self, balanced_panel: pd.DataFrame, 
                           output_dir: Optional[Path] = None) -> Dict[str, Path]:
        """Save balanced panel in multiple formats with metadata.
        
        Args:
            balanced_panel: The balanced panel DataFrame
            output_dir: Output directory (default: data/processed/balanced_panels)
            
        Returns:
            Dictionary of saved file paths
        """
        if output_dir is None:
            output_dir = self.data_dir / "balanced_panels"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save raw version (with missing values)
        balanced_raw = balanced_panel.copy()
        balanced_raw.to_parquet(output_dir / "balanced_panel_raw.parquet", index=False)
        
        # Save filled version
        balanced_panel.to_parquet(output_dir / "balanced_panel_filled.parquet", index=False)
        
        # Save integrated version if it has conflict/control data
        if 'events_total' in balanced_panel.columns:
            integrated_dir = self.data_dir / "integrated_panel"
            integrated_dir.mkdir(parents=True, exist_ok=True)
            balanced_panel.to_parquet(integrated_dir / "yemen_integrated_balanced_panel.parquet", index=False)
            balanced_panel.to_csv(integrated_dir / "yemen_integrated_balanced_panel.csv", index=False)
            
            # Save metadata
            metadata = {
                'created_date': datetime.now().isoformat(),
                'n_observations': len(balanced_panel),
                'n_markets': balanced_panel['market'].nunique() if 'market' in balanced_panel.columns else 0,
                'n_commodities': balanced_panel['commodity'].nunique() if 'commodity' in balanced_panel.columns else 0,
                'n_months': balanced_panel['year_month'].nunique() if 'year_month' in balanced_panel.columns else 0,
                'columns': list(balanced_panel.columns),
                'missing_percentages': balanced_panel.isnull().sum().div(len(balanced_panel)).mul(100).round(2).to_dict()
            }
            
            import json
            with open(integrated_dir / "integrated_panel_metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
        
        info(f"Saved balanced panel files to {output_dir}")
        
        return {
            'raw': output_dir / "balanced_panel_raw.parquet",
            'filled': output_dir / "balanced_panel_filled.parquet"
        }