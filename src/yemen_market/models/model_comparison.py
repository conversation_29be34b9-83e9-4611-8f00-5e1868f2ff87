"""Model comparison utilities for dual-track approach.

This module provides tools to compare results between Track 1 (complex)
and Track 2 (simple) models, helping validate the dual-track methodology.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Union, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

from ..utils.logging import bind, timer, info, warning, error, log_metric, progress
from .three_tier.core.base_model import BaseThreeTierModel


class ModelComparison:
    """Compare multiple econometric models.

    This class facilitates comparison between different modeling approaches,
    particularly useful for validating the dual-track methodology.
    """

    def __init__(self, models: Optional[Dict[str, BaseThreeTierModel]] = None):
        """Initialize model comparison.

        Args:
            models: Dictionary of {name: model} pairs
        """
        self.models = models or {}
        bind(comparison="ModelComparison")

    def add_model(self, name: str, model: BaseThreeTierModel) -> None:
        """Add a model to the comparison.

        Args:
            name: Name for the model
            model: Fitted econometric model
        """
        if not model.is_fitted:
            raise ValueError(f"Model '{name}' must be fitted before comparison")

        self.models[name] = model
        info(f"Added model '{name}' to comparison")

    def compare_information_criteria(self) -> pd.DataFrame:
        """Compare information criteria across models.

        Returns:
            DataFrame with AIC, BIC, and other criteria
        """
        with timer("compare_ic"):
            info("Comparing information criteria")

            if not self.models:
                return pd.DataFrame()

            results = []
            for name, model in self.models.items():
                try:
                    ic = model.get_information_criteria()
                    ic['Model'] = name
                    results.append(ic)
                except Exception as e:
                    warning(f"Failed to get IC for {name}: {e}")
                    # Add default values
                    results.append({'Model': name, 'AIC': np.nan, 'BIC': np.nan})

            if not results:
                return pd.DataFrame()

            ic_df = pd.DataFrame(results)

            # Identify best model by each criterion
            for col in ['AIC', 'BIC']:
                if col in ic_df.columns:
                    valid_values = ic_df[col].dropna()
                    if len(valid_values) > 0:
                        # Initialize column with False
                        ic_df[f'{col}_best'] = False
                        best_idx = ic_df[col].idxmin()
                        ic_df.loc[best_idx, f'{col}_best'] = True

            return ic_df.set_index('Model')

    def compare_coefficients(self, param_name: str = 'alpha') -> Dict[str, Any]:
        """Compare specific coefficients across models.

        Args:
            param_name: Name of parameter to compare

        Returns:
            Dictionary with comparison results
        """
        with timer("compare_coefficients"):
            info(f"Comparing {param_name} coefficients")

            comparison = {}

            # Extract coefficients from each model
            for name, model in self.models.items():
                try:
                    # Try the get_coefficients method first (for mock models)
                    if hasattr(model, 'get_coefficients'):
                        coef_dict = model.get_coefficients(param_name)
                        comparison[name] = coef_dict
                    # Then try vecm_results attribute
                    elif hasattr(model, 'vecm_results') and model.vecm_results:
                        results = model.vecm_results

                        # Handle different model types
                        if hasattr(results, f'{param_name}'):
                            comparison[name] = getattr(results, param_name)
                        elif hasattr(results, 'posterior_means') and param_name in results.posterior_means:
                            comparison[name] = results.posterior_means[param_name]
                        elif hasattr(results, f'low_regime_{param_name}') and hasattr(results, f'high_regime_{param_name}'):
                            comparison[name] = {
                                'low': getattr(results, f'low_regime_{param_name}'),
                                'high': getattr(results, f'high_regime_{param_name}')
                            }
                except Exception as e:
                    warning(f"Failed to get coefficients for {name}: {e}")

            return comparison

    def compare_diagnostics(self) -> pd.DataFrame:
        """Compare diagnostic test results across models.

        Returns:
            DataFrame with diagnostic test p-values
        """
        with timer("compare_diagnostics"):
            info("Comparing diagnostic tests")

            results = []

            for name, model in self.models.items():
                try:
                    # Try the get_diagnostics method first (for mock models)
                    if hasattr(model, 'get_diagnostics'):
                        diag_dict = model.get_diagnostics()
                        diag_dict['Model'] = name
                        results.append(diag_dict)
                    # Then try diagnostics attribute
                    elif hasattr(model, 'diagnostics') and model.diagnostics:
                        diag_dict = {'Model': name}

                        for test_name, test_result in model.diagnostics.items():
                            if isinstance(test_result, dict) and 'p_value' in test_result:
                                diag_dict[test_name] = test_result['p_value']
                            else:
                                diag_dict[test_name] = test_result

                        results.append(diag_dict)
                except Exception as e:
                    warning(f"Failed to get diagnostics for {name}: {e}")

            if results:
                diag_df = pd.DataFrame(results).set_index('Model')

                # Highlight failures (p < 0.05)
                for col in diag_df.columns:
                    if col != 'Model':  # Skip the Model column
                        diag_df[f'{col}_fail'] = diag_df[col] < 0.05

                return diag_df
            else:
                warning("No diagnostic results available")
                return pd.DataFrame()

    def compare_forecasts(self, steps: int = 12,
                         test_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Compare out-of-sample forecast performance.

        Args:
            steps: Number of forecast steps
            test_data: Optional holdout data for evaluation

        Returns:
            Dictionary with forecast metrics
        """
        with timer("compare_forecasts"):
            info(f"Comparing {steps}-step ahead forecasts")

            forecasts = {}
            metrics = {}

            for name, model in self.models.items():
                try:
                    # Generate forecasts
                    pred = model.predict(steps=steps)
                    forecasts[name] = pred

                    # Calculate metrics if test data provided
                    if test_data is not None:
                        eval_metrics = model.forecast_evaluation(
                            test_data,
                            metrics=['rmse', 'mae', 'mape']
                        )
                        metrics[name] = eval_metrics

                except Exception as e:
                    warning(f"Forecast failed for {name}: {e}")

            return {
                'forecasts': forecasts,
                'metrics': pd.DataFrame(metrics).T if metrics else None
            }

    def assess_dual_track_consistency(self) -> Dict[str, Any]:
        """Assess consistency between Track 1 and Track 2 models.

        Returns:
            Dictionary with consistency metrics
        """
        # Find Track 1 and Track 2 models
        track1 = None
        track2 = None

        for name, model in self.models.items():
            if 'track1' in name.lower() or 'bayesian' in name.lower():
                track1 = (name, model)
            elif 'track2' in name.lower() or 'threshold' in name.lower():
                track2 = (name, model)

        if track1 is None or track2 is None:
            warning("Need both Track 1 and Track 2 models for consistency assessment")
            return {}

        info("Assessing dual-track consistency")

        # Compare key parameters
        consistency = {}

        # 1. Compare adjustment speeds
        coefs1 = self.compare_coefficients('alpha')
        if track1[0] in coefs1 and track2[0] in coefs1:
            # Calculate correlation
            # This is simplified - in practice you'd compare full parameter vectors
            consistency['parameter_correlation'] = self._calculate_parameter_correlation(
                coefs1[track1[0]], coefs1[track2[0]]
            )

        # 2. Compare regime classifications (if applicable)
        if hasattr(track2[1].vecm_results, 'regime_assignment'):
            consistency['regime_agreement'] = self._calculate_regime_agreement(
                track1[1], track2[1]
            )

        # 3. Compare forecast performance
        forecast_comp = self.compare_forecasts(steps=6)
        if forecast_comp['metrics'] is not None:
            consistency['forecast_metrics'] = forecast_comp['metrics']

        # Overall assessment
        if 'parameter_correlation' in consistency:
            corr = consistency['parameter_correlation']
            if corr > 0.8:
                consistency['overall'] = "Strong agreement - use Track 2 for policy"
            elif corr > 0.5:
                consistency['overall'] = "Moderate agreement - investigate differences"
            else:
                consistency['overall'] = "Weak agreement - check model specifications"

        return consistency

    def generate_comparison_table(self) -> pd.DataFrame:
        """Generate comprehensive comparison table.

        Returns:
            DataFrame with model comparison metrics
        """
        if not self.models:
            return pd.DataFrame()

        # Combine information criteria and diagnostics
        ic_df = self.compare_information_criteria()
        diag_df = self.compare_diagnostics()

        # Merge tables
        if not ic_df.empty and not diag_df.empty:
            comparison_table = ic_df.join(diag_df, how='outer')
        elif not ic_df.empty:
            comparison_table = ic_df
        elif not diag_df.empty:
            comparison_table = diag_df
        else:
            comparison_table = pd.DataFrame(index=list(self.models.keys()))

        return comparison_table

    def save_results(self, output_dir: Path) -> None:
        """Save comparison results to files.

        Args:
            output_dir: Directory to save results
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Save information criteria
        ic_df = self.compare_information_criteria()
        if not ic_df.empty:
            ic_df.to_csv(output_dir / "information_criteria.csv")

        # Save diagnostics
        diag_df = self.compare_diagnostics()
        if not diag_df.empty:
            diag_df.to_csv(output_dir / "diagnostics.csv")

        # Save comparison plot
        self.plot_comparison(str(output_dir / "comparison_plot.png"))

        info(f"Saved comparison results to {output_dir}")

    def _calculate_parameter_correlation(self, coef1: dict, coef2: dict) -> float:
        """Calculate correlation between parameter sets.

        Args:
            coef1: First set of coefficients
            coef2: Second set of coefficients

        Returns:
            Correlation coefficient
        """
        # Extract common parameters
        common_params = set(coef1.keys()) & set(coef2.keys())

        if not common_params:
            return 0.0

        # Extract values for common parameters
        values1 = []
        values2 = []

        for param in common_params:
            val1 = coef1[param]
            val2 = coef2[param]

            # Handle nested structures
            if isinstance(val1, dict) and 'value' in val1:
                val1 = val1['value']
            if isinstance(val2, dict) and 'value' in val2:
                val2 = val2['value']

            values1.append(float(val1))
            values2.append(float(val2))

        # Calculate correlation
        if len(values1) < 2:
            return 1.0 if values1[0] == values2[0] else 0.0

        correlation = np.corrcoef(values1, values2)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0

    def _calculate_regime_agreement(self, model1, model2) -> float:
        """Calculate agreement between regime assignments.

        Args:
            model1: First model with regime assignments
            model2: Second model with regime assignments

        Returns:
            Agreement ratio (0-1)
        """
        try:
            regime1 = model1.vecm_results.regime_assignment
            regime2 = model2.vecm_results.regime_assignment

            # Ensure same length
            min_len = min(len(regime1), len(regime2))
            regime1 = regime1[:min_len]
            regime2 = regime2[:min_len]

            # Calculate agreement
            agreement = np.mean(regime1 == regime2)
            return float(agreement)

        except Exception:
            return 0.0

    def _get_regime_assignment(self, model: BaseThreeTierModel) -> Optional[pd.Series]:
        """Extract regime assignment from a model.

        Handles different model types:
        1. Direct regime assignment from VECM results
        2. Bayesian posterior regime probabilities
        3. Derived from conflict intensity data

        Args:
            model: Model to extract regime from

        Returns:
            pd.Series with datetime index and binary regime values, or None
        """
        # 1. Direct regime assignment (typically Track 2/Threshold models)
        if hasattr(model, 'vecm_results') and hasattr(model.vecm_results, 'regime_assignment'):
            regime = model.vecm_results.regime_assignment
            # Try to get dates from model data
            if hasattr(model, 'data') and 'dates' in model.data:
                dates = model.data['dates'][:len(regime)]
                return pd.Series(regime, index=dates, name='regime')
            else:
                info("No dates found for regime assignment, using numeric index")
                return pd.Series(regime, name='regime')

        # 2. Bayesian posterior regime probabilities (Track 1)
        if hasattr(model, 'vecm_results'):
            results = model.vecm_results
            if hasattr(results, 'trace') and results.trace is not None:
                try:
                    # Look for regime indicators in the trace
                    if 'regime' in results.trace.posterior:
                        regime_probs = results.trace.posterior['regime'].mean(("chain", "draw")).values.squeeze()
                        regime = (regime_probs > 0.5).astype(int)
                        # Get dates if available
                        if hasattr(model, 'data') and 'dates' in model.data:
                            dates = model.data['dates'][:len(regime)]
                            return pd.Series(regime, index=dates, name='regime')
                        else:
                            return pd.Series(regime, name='regime')
                except Exception as e:
                    warning(f"Could not extract regime from Bayesian posterior: {e}")

        # 3. Derive from conflict intensity data
        if hasattr(model, 'data') and 'conflict_intensity' in model.data:
            conflict_data = model.data['conflict_intensity']

            # Ensure we have a Series with proper index
            if isinstance(conflict_data, pd.Series):
                conflict_series = conflict_data
            elif isinstance(conflict_data, np.ndarray):
                # Try to get dates
                if 'dates' in model.data:
                    dates = model.data['dates'][:len(conflict_data)]
                    conflict_series = pd.Series(conflict_data, index=dates)
                else:
                    conflict_series = pd.Series(conflict_data)
            else:
                warning(f"Unexpected conflict_intensity type: {type(conflict_data)}")
                return None

            # Get threshold (preferring model-specific threshold)
            threshold = 50  # default
            if hasattr(model, 'threshold'):
                threshold = model.threshold
            elif hasattr(model, 'threshold_value'):
                threshold = model.threshold_value

            # Create binary regime assignment
            regime = (conflict_series > threshold).astype(int)
            return regime.rename('regime')

        return None

    def _calculate_regime_agreement(self, model1: BaseThreeTierModel,
                                   model2: BaseThreeTierModel) -> float:
        """Calculate agreement in regime classification.

        For models with regime switching/thresholds, calculate the
        percentage of time periods where both models assign the same regime.

        Args:
            model1: First model (typically Bayesian/Track 1)
            model2: Second model (typically Threshold/Track 2)

        Returns:
            Float between 0 and 1 indicating agreement percentage
        """
        try:
            # Extract regime assignments using helper method
            regime1 = self._get_regime_assignment(model1)
            regime2 = self._get_regime_assignment(model2)

            if regime1 is None or regime2 is None:
                warning("Missing regime assignment for comparison")
                return np.nan

            info(f"Regime 1: {len(regime1)} observations")
            info(f"Regime 2: {len(regime2)} observations")

            # Align series based on index if they have datetime indices
            if isinstance(regime1.index, pd.DatetimeIndex) and isinstance(regime2.index, pd.DatetimeIndex):
                # Find common dates
                common_dates = regime1.index.intersection(regime2.index)
                if len(common_dates) == 0:
                    warning("No overlapping dates between regime assignments")
                    return np.nan

                # Align both series to common dates
                regime1_aligned = regime1.loc[common_dates]
                regime2_aligned = regime2.loc[common_dates]
                info(f"Aligned to {len(common_dates)} common dates")
            else:
                # Fall back to simple length-based alignment
                min_len = min(len(regime1), len(regime2))
                if min_len == 0:
                    warning("No observations for regime comparison")
                    return np.nan

                regime1_aligned = regime1.iloc[:min_len]
                regime2_aligned = regime2.iloc[:min_len]
                info(f"Aligned to {min_len} observations (no date index)")

            # Calculate agreement
            agreement = np.mean(regime1_aligned.values == regime2_aligned.values)
            n_agree = np.sum(regime1_aligned.values == regime2_aligned.values)
            n_total = len(regime1_aligned)

            info(f"Regime agreement: {n_agree}/{n_total} periods ({agreement:.1%})")

            # Additional analysis for disagreements
            if agreement < 1.0:
                disagree_mask = regime1_aligned.values != regime2_aligned.values
                n_disagree = np.sum(disagree_mask)
                info(f"Disagreement in {n_disagree} periods")

                # If we have conflict data, analyze disagreements around threshold
                if hasattr(model2, 'data') and 'conflict_intensity' in model2.data:
                    conflict_data = model2.data['conflict_intensity']

                    # Get threshold value
                    threshold = 50  # default
                    if hasattr(model2, 'threshold'):
                        threshold = model2.threshold
                    elif hasattr(model2, 'vecm_results') and hasattr(model2.vecm_results, 'threshold_value'):
                        threshold = model2.vecm_results.threshold_value

                    # Align conflict data with disagreement periods
                    if isinstance(conflict_data, pd.Series) and isinstance(regime1_aligned.index, pd.DatetimeIndex):
                        conflict_at_disagree = conflict_data.loc[regime1_aligned.index[disagree_mask]]
                    else:
                        # Numeric alignment
                        conflict_array = conflict_data.values if hasattr(conflict_data, 'values') else conflict_data
                        min_len = min(len(conflict_array), len(disagree_mask))
                        conflict_at_disagree = conflict_array[:min_len][disagree_mask[:min_len]]

                    if len(conflict_at_disagree) > 0:
                        # Calculate proximity to threshold
                        distances = np.abs(conflict_at_disagree - threshold)
                        mean_distance = np.mean(distances)
                        median_distance = np.median(distances)
                        info(f"Distance from threshold at disagreements - Mean: {mean_distance:.1f}, Median: {median_distance:.1f}")

                        # Check if disagreements are near threshold
                        near_threshold = np.sum(distances < 10)  # within 10 events
                        info(f"{near_threshold}/{n_disagree} disagreements within 10 events of threshold")

            return float(agreement)

        except Exception as e:
            error(f"Error calculating regime agreement: {e}")
            return np.nan

    def plot_comparison(self, save_path: Optional[str] = None) -> None:
        """Create comparison plots.

        Args:
            save_path: Optional path to save figure
        """
        n_models = len(self.models)
        if n_models < 2:
            warning("Need at least 2 models for comparison plots")
            return

        fig, axes = plt.subplots(2, 2, figsize=(14, 10))
        axes = axes.flatten()

        # 1. Information criteria
        ic_df = self.compare_information_criteria()
        if not ic_df.empty:
            ax = axes[0]
            ic_df[['AIC', 'BIC']].plot(kind='bar', ax=ax)
            ax.set_title('Information Criteria Comparison')
            ax.set_ylabel('Value (lower is better)')
            ax.legend()
            ax.grid(True, alpha=0.3)

        # 2. Coefficient comparison (simplified visualization)
        ax = axes[1]
        coef_data = []
        for name, model in self.models.items():
            if hasattr(model.vecm_results, 'alpha'):
                coef_data.append({
                    'Model': name,
                    'Mean α': np.mean(model.vecm_results.alpha)
                })

        if coef_data:
            coef_df = pd.DataFrame(coef_data)
            coef_df.plot(x='Model', y='Mean α', kind='bar', ax=ax, legend=False)
            ax.set_title('Mean Adjustment Speed')
            ax.set_ylabel('α')
            ax.grid(True, alpha=0.3)

        # 3. Diagnostic comparison
        diag_df = self.compare_diagnostics()
        if not diag_df.empty:
            ax = axes[2]
            # Count test failures
            failure_counts = diag_df.filter(like='_fail').sum(axis=1)
            failure_counts.plot(kind='bar', ax=ax, color='red', alpha=0.7)
            ax.set_title('Diagnostic Test Failures')
            ax.set_ylabel('Number of failed tests')
            ax.grid(True, alpha=0.3)

        # 4. Summary text
        ax = axes[3]
        ax.axis('off')

        consistency = self.assess_dual_track_consistency()
        summary_text = "Model Comparison Summary\n" + "="*30 + "\n\n"
        summary_text += f"Models compared: {', '.join(self.models.keys())}\n\n"

        if 'parameter_correlation' in consistency:
            summary_text += f"Parameter correlation: {consistency['parameter_correlation']:.3f}\n"

        if 'overall' in consistency:
            summary_text += f"\nRecommendation: {consistency['overall']}"

        ax.text(0.1, 0.9, summary_text, transform=ax.transAxes,
                verticalalignment='top', fontsize=12, family='monospace')

        plt.suptitle('Model Comparison Results', fontsize=16)
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            info(f"Saved comparison plot to {save_path}")
        else:
            plt.show()


def run_model_comparison(models: Dict[str, BaseThreeTierModel],
                        test_data: Optional[pd.DataFrame] = None,
                        output_dir: Optional[Path] = None) -> ModelComparison:
    """Convenience function to run full model comparison.

    Args:
        models: Dictionary of fitted models
        test_data: Optional test data for forecast evaluation
        output_dir: Optional directory for saving results

    Returns:
        ModelComparison object with results
    """
    comparison = ModelComparison(models)

    # Run all comparisons
    ic_df = comparison.compare_information_criteria()
    diag_df = comparison.compare_diagnostics()
    consistency = comparison.assess_dual_track_consistency()

    # Save results if output directory provided
    if output_dir:
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        ic_df.to_csv(output_dir / 'information_criteria.csv')

        if not diag_df.empty:
            diag_df.to_csv(output_dir / 'diagnostics.csv')

        comparison.plot_comparison(str(output_dir / 'comparison_plots.png'))

        # Save consistency assessment
        import json
        with open(output_dir / 'consistency_assessment.json', 'w') as f:
            # Convert numpy types for JSON serialization
            consistency_json = {}
            for k, v in consistency.items():
                if isinstance(v, np.ndarray):
                    consistency_json[k] = v.tolist()
                elif isinstance(v, (np.float32, np.float64)):
                    consistency_json[k] = float(v)
                elif isinstance(v, pd.DataFrame):
                    consistency_json[k] = v.to_dict()
                else:
                    consistency_json[k] = v

            json.dump(consistency_json, f, indent=2)

    return comparison


class ModelComparisonFramework:
    """Extended framework for comprehensive model comparison.

    This class provides additional functionality beyond basic comparisons,
    including cross-validation, ensemble methods, and policy simulations.
    """

    def __init__(self):
        """Initialize comparison framework."""
        self.comparisons = []
        self.ensemble_weights = None
        bind(framework="ModelComparisonFramework")

    def add_comparison(self, comparison: ModelComparison) -> None:
        """Add a model comparison to the framework.

        Args:
            comparison: ModelComparison object
        """
        self.comparisons.append(comparison)
        info(f"Added comparison with {len(comparison.models)} models")

    def calculate_ensemble_weights(self,
                                  metric: str = 'aic',
                                  method: str = 'inverse_ic') -> Dict[str, float]:
        """Calculate optimal ensemble weights based on model performance.

        Args:
            metric: Metric to use for weighting ('aic', 'bic', 'forecast_rmse')
            method: Weighting method ('inverse_ic', 'softmax', 'equal')

        Returns:
            Dictionary of model weights
        """
        if not self.comparisons:
            warning("No comparisons available for ensemble weighting")
            return {}

        info(f"Calculating ensemble weights using {method} on {metric}")

        # Collect metrics from all models
        all_metrics = {}
        for comp in self.comparisons:
            ic_df = comp.compare_information_criteria()

            for model_name in comp.models.keys():
                if model_name not in all_metrics:
                    all_metrics[model_name] = []

                if metric.upper() in ic_df.columns and model_name in ic_df.index:
                    all_metrics[model_name].append(ic_df.loc[model_name, metric.upper()])

        # Calculate weights based on method
        weights = {}

        if method == 'equal':
            # Equal weights for all models
            n_models = len(all_metrics)
            for model in all_metrics:
                weights[model] = 1.0 / n_models

        elif method == 'inverse_ic':
            # Weight inversely proportional to IC
            ic_values = {m: np.mean(v) for m, v in all_metrics.items() if v}

            if ic_values:
                # Convert to positive weights (lower IC = higher weight)
                min_ic = min(ic_values.values())
                inv_weights = {m: min_ic / v for m, v in ic_values.items()}

                # Normalize to sum to 1
                total = sum(inv_weights.values())
                weights = {m: w / total for m, w in inv_weights.items()}

        elif method == 'softmax':
            # Softmax transformation of negative IC values
            ic_values = {m: np.mean(v) for m, v in all_metrics.items() if v}

            if ic_values:
                # Negate IC (so lower is better) and apply softmax
                neg_ic = np.array([-v for v in ic_values.values()])
                softmax_weights = np.exp(neg_ic) / np.sum(np.exp(neg_ic))

                weights = dict(zip(ic_values.keys(), softmax_weights))

        self.ensemble_weights = weights

        # Log results
        for model, weight in weights.items():
            log_metric(f"ensemble_weight_{model}", weight)
            info(f"  {model}: {weight:.3f}")

        return weights

    def generate_ensemble_forecast(self,
                                  steps: int = 12,
                                  weights: Optional[Dict[str, float]] = None) -> pd.DataFrame:
        """Generate ensemble forecast using weighted model predictions.

        Args:
            steps: Forecast horizon
            weights: Model weights (uses calculated weights if None)

        Returns:
            DataFrame with ensemble forecasts
        """
        if weights is None:
            weights = self.ensemble_weights

        if weights is None:
            warning("No weights specified, using equal weights")
            weights = self.calculate_ensemble_weights(method='equal')

        info(f"Generating {steps}-step ensemble forecast")

        # Collect forecasts from all models
        all_forecasts = {}

        for comp in self.comparisons:
            forecast_results = comp.compare_forecasts(steps=steps)

            for model_name, forecast in forecast_results['forecasts'].items():
                if model_name in weights:
                    all_forecasts[model_name] = forecast

        if not all_forecasts:
            error("No forecasts available for ensemble")
            return pd.DataFrame()

        # Create weighted ensemble
        ensemble = None

        for model_name, forecast in all_forecasts.items():
            weight = weights.get(model_name, 0)

            if ensemble is None:
                ensemble = forecast * weight
            else:
                # Align indices before adding
                ensemble = ensemble.add(forecast * weight, fill_value=0)

        return ensemble

    def cross_validate_models(self,
                             data: pd.DataFrame,
                             n_splits: int = 5,
                             test_size: int = 12) -> Dict[str, Dict[str, float]]:
        """Perform time series cross-validation on all models.

        Uses expanding window cross-validation appropriate for time series.
        Each split uses all data up to a point for training and the next
        test_size periods for testing.

        Args:
            data: Full dataset for cross-validation
            n_splits: Number of CV splits
            test_size: Size of test set in each split (in time periods)

        Returns:
            Dictionary of CV results by model
        """
        info(f"Running {n_splits}-fold time series cross-validation")
        info(f"Test size: {test_size} periods per fold")

        cv_results = {}

        # Ensure data is sorted by date
        if 'date' in data.columns:
            data = data.sort_values('date')

        # Get total time periods
        if 'date' in data.columns:
            dates = data['date'].unique()
            n_periods = len(dates)
        else:
            n_periods = len(data)

        # Calculate split points
        min_train_size = max(60, n_periods // 3)  # At least 60 periods or 1/3 of data

        if n_periods < min_train_size + test_size:
            warning(f"Insufficient data for CV: {n_periods} periods < {min_train_size + test_size} required")
            return cv_results

        # Calculate split indices
        splits = []
        step_size = (n_periods - min_train_size - test_size) // (n_splits - 1)

        for i in range(n_splits):
            train_end = min_train_size + i * step_size
            test_start = train_end
            test_end = min(test_start + test_size, n_periods)

            if test_end > n_periods - test_size:  # Ensure we don't go beyond data
                break

            splits.append({
                'train_start': 0,
                'train_end': train_end,
                'test_start': test_start,
                'test_end': test_end
            })

        info(f"Created {len(splits)} CV splits")

        # Run CV for each model
        with progress("Cross-validation", total=len(self.comparisons) * len(splits)) as update:
            for comp in self.comparisons:
                for model_name, model in comp.models.items():
                    if model_name not in cv_results:
                        cv_results[model_name] = {
                            'rmse': [],
                            'mae': [],
                            'mape': [],
                            'directional_accuracy': []
                        }

                    model_class = type(model)

                    for split_idx, split in enumerate(splits):
                        try:
                            # Extract train and test data
                            if 'date' in data.columns:
                                train_dates = dates[split['train_start']:split['train_end']]
                                test_dates = dates[split['test_start']:split['test_end']]

                                train_data = data[data['date'].isin(train_dates)].copy()
                                test_data = data[data['date'].isin(test_dates)].copy()
                            else:
                                train_data = data.iloc[split['train_start']:split['train_end']].copy()
                                test_data = data.iloc[split['test_start']:split['test_end']].copy()

                            # Create new model instance with same parameters
                            cv_model = model_class(**model.get_params() if hasattr(model, 'get_params') else {})

                            # Fit on training data
                            cv_model.fit(train_data)

                            # Predict on test data
                            predictions = cv_model.predict(steps=len(test_data))

                            # Calculate metrics
                            if hasattr(cv_model, 'calculate_forecast_metrics'):
                                metrics = cv_model.calculate_forecast_metrics(predictions, test_data)
                            else:
                                # Simple metrics calculation
                                if 'price_usd' in test_data.columns:
                                    actual = test_data.groupby('date')['price_usd'].mean()
                                    pred = predictions.mean(axis=1) if len(predictions.shape) > 1 else predictions

                                    # Align indices
                                    common_idx = actual.index.intersection(pred.index)
                                    if len(common_idx) > 0:
                                        actual_aligned = actual.loc[common_idx]
                                        pred_aligned = pred.loc[common_idx]

                                        errors = pred_aligned - actual_aligned
                                        metrics = {
                                            'rmse': float(np.sqrt(np.mean(errors ** 2))),
                                            'mae': float(np.mean(np.abs(errors))),
                                            'mape': float(np.mean(np.abs(errors / actual_aligned)) * 100),
                                            'directional_accuracy': float(
                                                np.mean(np.sign(pred_aligned.diff()) == np.sign(actual_aligned.diff()))
                                            )
                                        }
                                    else:
                                        metrics = {}
                                else:
                                    metrics = {}

                            # Store metrics
                            for metric, value in metrics.items():
                                if metric in cv_results[model_name]:
                                    cv_results[model_name][metric].append(value)

                        except Exception as e:
                            warning(f"CV fold {split_idx} failed for {model_name}: {e}")

                        update(1)

        # Calculate summary statistics
        for model_name in cv_results:
            for metric in list(cv_results[model_name].keys()):
                values = cv_results[model_name][metric]
                if values:
                    cv_results[model_name][f'{metric}_mean'] = float(np.mean(values))
                    cv_results[model_name][f'{metric}_std'] = float(np.std(values))
                    cv_results[model_name][f'{metric}_min'] = float(np.min(values))
                    cv_results[model_name][f'{metric}_max'] = float(np.max(values))
                # Remove raw values
                del cv_results[model_name][metric]

        # Log summary
        info("\nCross-validation results:")
        for model_name, results in cv_results.items():
            if 'rmse_mean' in results:
                info(f"  {model_name}: RMSE={results['rmse_mean']:.3f}±{results['rmse_std']:.3f}")

        return cv_results

    def policy_simulation_comparison(self,
                                   scenario: str = 'conflict_reduction',
                                   **kwargs) -> Dict[str, pd.DataFrame]:
        """Compare model responses to policy scenarios.

        Args:
            scenario: Type of policy scenario to simulate
            **kwargs: Scenario-specific parameters

        Returns:
            Dictionary of simulation results by model
        """
        info(f"Running policy simulation: {scenario}")

        simulation_results = {}

        for comp in self.comparisons:
            for model_name, model in comp.models.items():
                if hasattr(model, 'simulate_policy'):
                    try:
                        result = model.simulate_policy(scenario, **kwargs)
                        simulation_results[model_name] = result
                    except Exception as e:
                        warning(f"Policy simulation failed for {model_name}: {e}")

        return simulation_results

    def generate_comparison_report(self,
                                  output_path: Optional[Path] = None) -> str:
        """Generate comprehensive comparison report.

        Args:
            output_path: Optional path to save report

        Returns:
            Report as formatted string
        """
        report = ["# Model Comparison Report", "=" * 50, ""]

        # Summary statistics
        total_models = sum(len(comp.models) for comp in self.comparisons)
        report.append(f"Total models compared: {total_models}")
        report.append(f"Number of comparisons: {len(self.comparisons)}")
        report.append("")

        # Ensemble weights
        if self.ensemble_weights:
            report.append("## Ensemble Weights")
            report.append("-" * 30)
            for model, weight in sorted(self.ensemble_weights.items(),
                                       key=lambda x: x[1], reverse=True):
                report.append(f"  {model}: {weight:.3f}")
            report.append("")

        # Individual comparison summaries
        for i, comp in enumerate(self.comparisons):
            report.append(f"## Comparison {i+1}")
            report.append("-" * 30)

            # Model list
            report.append("Models:")
            for model_name in comp.models.keys():
                report.append(f"  - {model_name}")

            # Consistency assessment
            consistency = comp.assess_dual_track_consistency()
            if 'overall' in consistency:
                report.append(f"\nConsistency: {consistency['overall']}")

            report.append("")

        report_text = "\n".join(report)

        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            output_path.write_text(report_text)
            info(f"Saved comparison report to {output_path}")

        return report_text


class ComparisonSuite:
    """Suite for managing multiple model comparisons.

    Simplified version of ModelComparisonFramework for testing compatibility.
    """

    def __init__(self):
        """Initialize comparison suite."""
        self.comparisons = []
        bind(suite="ComparisonSuite")

    def add_comparison(self, name: str, models: Dict[str, BaseThreeTierModel]) -> None:
        """Add a comparison to the suite.

        Args:
            name: Name for the comparison
            models: Dictionary of models to compare
        """
        comparison = ModelComparison(models)
        self.comparisons.append(comparison)
        info(f"Added comparison '{name}' with {len(models)} models")

    def generate_report(self, output_path: Optional[Path] = None) -> str:
        """Generate comparison report.

        Args:
            output_path: Optional path to save report

        Returns:
            Report text
        """
        report = []
        report.append("Model Comparison Report")
        report.append("=" * 50)
        report.append("")

        # Summary
        total_models = sum(len(comp.models) for comp in self.comparisons)
        report.append(f"Total comparisons: {len(self.comparisons)}")
        report.append(f"Total models: {total_models}")
        report.append("")

        # Individual comparison summaries
        for i, comp in enumerate(self.comparisons):
            report.append(f"## Comparison {i+1}")
            report.append("-" * 30)

            # Model list
            report.append("Models:")
            for model_name in comp.models.keys():
                report.append(f"  - {model_name}")

            # Consistency assessment
            try:
                consistency = comp.assess_dual_track_consistency()
                if 'overall' in consistency:
                    report.append(f"\nConsistency: {consistency['overall']}")
            except Exception:
                report.append("\nConsistency: Unable to assess")

            report.append("")

        report_text = "\n".join(report)

        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            output_path.write_text(report_text)
            info(f"Saved comparison report to {output_path}")

        return report_text
