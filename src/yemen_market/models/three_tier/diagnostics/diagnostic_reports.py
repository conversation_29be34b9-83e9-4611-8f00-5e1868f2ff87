"""
Diagnostic Report Generation for Three-Tier Models.

This module defines the `DiagnosticReport` class, responsible for
aggregating, formatting, and presenting diagnostic test results.
"""

from typing import List, Dict, Any, Optional, Union
import pandas as pd
from dataclasses import dataclass, field

from yemen_market.utils.logging import info, warning


@dataclass
class TestResult:
    """Represents the result of a single diagnostic test."""
    test_name: str
    statistic: Optional[float] = None
    p_value: Optional[float] = None
    passed: Optional[bool] = None
    interpretation: str = ""
    recommendation: str = ""
    details: Dict[str, Any] = field(default_factory=dict) # For additional info
    error_message: Optional[str] = None # If test failed to run


class DiagnosticReport:
    """
    Aggregates and formats diagnostic test results for a specific tier.
    """

    def __init__(self, tier: int):
        """
        Initialize a new diagnostic report.

        Parameters
        ----------
        tier : int
            The tier number (1, 2, or 3) for which this report is generated.
        """
        self.tier = tier
        self.test_results: List[TestResult] = []
        self.critical_errors: List[Dict[str,str]] = [] # For errors preventing diagnostics
        info(f"DiagnosticReport initialized for Tier {tier}.")

    def add_test_result(
        self,
        test_name: str,
        statistic: Optional[float],
        p_value: Optional[float],
        interpretation: str,
        passed: Optional[bool],
        recommendation: str = "",
        details: Optional[Dict[str, Any]] = None
    ):
        """Add the result of a single diagnostic test."""
        result = TestResult(
            test_name=test_name,
            statistic=statistic,
            p_value=p_value,
            interpretation=interpretation,
            passed=passed,
            recommendation=recommendation,
            details=details or {}
        )
        self.test_results.append(result)
        status = "PASSED" if passed else ("FAILED" if passed is not None else "INFO")
        info(f"Test Added: {test_name} - Stat: {statistic:.4f}, P-val: {p_value:.4f} ({status})")

    def add_test_error(self, test_name: str, error_message: str):
        """Record an error that occurred while trying to run a test."""
        result = TestResult(
            test_name=test_name,
            error_message=error_message,
            passed=False, # Mark as failed if error occurred
            interpretation=f"Test execution failed: {error_message}"
        )
        self.test_results.append(result)
        warning(f"Test Error: {test_name} - {error_message}")

    def add_critical_error(self, error_type: str, message: str):
        """Record a critical error that prevented diagnostics from running properly."""
        self.critical_errors.append({"type": error_type, "message": message})
        warning(f"Critical Diagnostic Error: {error_type} - {message}")

    def has_critical_failures(self) -> bool:
        """
        Check if any critical diagnostic tests have failed.
        
        A "critical failure" might be defined by:
        - Specific important tests failing (e.g., Wooldridge, Pesaran CD).
        - A certain percentage of tests failing.
        - Tests failing below a more stringent p-value threshold.
        
        This method needs to be customized based on project requirements.
        For now, let's consider a failure if any test explicitly marked as 'passed=False'.
        """
        if any(err for err in self.critical_errors):
            return True # Critical error in setup/data retrieval

        for result in self.test_results:
            if result.passed is False: # Explicitly failed
                 # Define which tests are "critical"
                critical_tests = [
                    "Wooldridge Test for Serial Correlation",
                    "Pesaran CD Test for Cross-Sectional Dependence"
                ]
                if result.test_name in critical_tests:
                    return True
        return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert the report to a dictionary."""
        return {
            "tier": self.tier,
            "critical_errors": self.critical_errors,
            "test_results": [vars(tr) for tr in self.test_results],
            "summary": {
                "total_tests": len(self.test_results),
                "tests_passed": sum(1 for tr in self.test_results if tr.passed is True),
                "tests_failed": sum(1 for tr in self.test_results if tr.passed is False),
                "tests_with_errors": sum(1 for tr in self.test_results if tr.error_message is not None),
                "has_critical_failures": self.has_critical_failures(),
            }
        }

    def to_dataframe(self) -> pd.DataFrame:
        """Convert test results to a pandas DataFrame for easy viewing."""
        if not self.test_results:
            return pd.DataFrame()
        
        df_data = []
        for res in self.test_results:
            row = {
                "Test Name": res.test_name,
                "Statistic": f"{res.statistic:.4f}" if res.statistic is not None else "N/A",
                "P-Value": f"{res.p_value:.4f}" if res.p_value is not None else "N/A",
                "Passed": "Yes" if res.passed is True else ("No" if res.passed is False else "N/A"),
                "Interpretation": res.interpretation,
                "Recommendation": res.recommendation,
                "Error": res.error_message or ""
            }
            df_data.append(row)
        return pd.DataFrame(df_data)

    def generate_text_summary(self) -> str:
        """Generate a human-readable text summary of the report."""
        summary_lines = [f"Diagnostic Report for Tier {self.tier}\n" + "="*30]

        if self.critical_errors:
            summary_lines.append("\nCRITICAL ERRORS ENCOUNTERED:")
            for err in self.critical_errors:
                summary_lines.append(f"  - Type: {err['type']}, Message: {err['message']}")
            summary_lines.append("")

        df_report = self.to_dataframe()
        if not df_report.empty:
            summary_lines.append(df_report.to_string(index=False))
        else:
            summary_lines.append("No diagnostic tests were run or recorded.")

        report_summary_dict = self.to_dict()['summary']
        summary_lines.append("\n" + "="*30 + "\nSUMMARY:")
        summary_lines.append(f"  Total Tests Attempted: {report_summary_dict['total_tests']}")
        summary_lines.append(f"  Tests Passed: {report_summary_dict['tests_passed']}")
        summary_lines.append(f"  Tests Failed: {report_summary_dict['tests_failed']}")
        if report_summary_dict['tests_with_errors'] > 0:
            summary_lines.append(f"  Tests with Execution Errors: {report_summary_dict['tests_with_errors']}")
        
        if report_summary_dict['has_critical_failures']:
            summary_lines.append("\n  WARNING: Critical diagnostic failures detected!")
        elif report_summary_dict['tests_failed'] > 0 :
             summary_lines.append("\n  Note: Some non-critical tests failed.")
        elif report_summary_dict['total_tests'] > 0 and report_summary_dict['tests_passed'] == report_summary_dict['total_tests']:
             summary_lines.append("\n  All executed tests passed.")


        return "\n".join(summary_lines)

    def generate_latex_table(self) -> str:
        """
        Generate a LaTeX formatted table of the diagnostic results.
        (Placeholder - requires more sophisticated formatting)
        """
        if not self.test_results:
            return "% No diagnostic results to report.\n"

        df_report = self.to_dataframe()
        # Basic LaTeX table, can be improved with `to_latex()` options
        # and by selecting key columns.
        
        # Select and rename columns for a more concise LaTeX table
        latex_df = df_report[["Test Name", "Statistic", "P-Value", "Passed"]].copy()
        latex_df.rename(columns={"Test Name": "Test", "P-Value": "p-value"}, inplace=True)
        
        try:
            latex_str = latex_df.to_latex(index=False, escape=True, column_format="lccc")
            return (
                "% LaTeX table of diagnostic results\n"
                "\\begin{table}[htbp]\n"
                "\\centering\n"
                "\\caption{Diagnostic Test Results for Tier " + str(self.tier) + "}\n"
                + latex_str +
                "\\label{tab:diag_tier" + str(self.tier) + "}\n"
                "\\end{table}\n"
            )
        except Exception as e:
            error(f"Failed to generate LaTeX table: {e}")
            return f"% Error generating LaTeX table: {e}\n"
