"""
Adapters for Extracting Data from ResultsContainer for Diagnostic Tests.

This module provides the `DiagnosticAdapter` class, which serves as an
intermediary between the `ResultsContainer` and the diagnostic test
implementations. It handles the extraction and formatting of data
(residuals, panel structure, design matrices) needed by the tests.
"""

from typing import Any, Dict, Optional, List
import pandas as pd
import numpy as np

from yemen_market.models.three_tier.core.results_container import ResultsContainer
from yemen_market.utils.logging import info, warning, error


class DiagnosticAdapter:
    """
    Adapts `ResultsContainer` output for use in diagnostic tests.

    This class provides a standardized way to access necessary data
    (residuals, panel structure, exogenous variables) from potentially
    varied `ResultsContainer` formats across different tiers or models.
    """

    def __init__(self, results_container: ResultsContainer, original_data: pd.DataFrame, tier: int):
        """
        Initialize the adapter with a ResultsContainer and original data.

        Parameters
        ----------
        results_container : ResultsContainer
            The container holding model estimation results.
        original_data : pd.DataFrame
            The original panel data used for estimation. This is crucial for
            reconstructing panel structure if not directly available in results.
        tier : int
            The tier number (1, 2, or 3) from which results originate.
        """
        self.results_container = results_container
        self.original_data = original_data
        self.tier = tier
        
        # Standardized panel identifiers
        # These should ideally come from a global config or be inferred
        self.entity_id_col = "governorate" # Example, might vary
        self.time_id_col = "date"          # Example, might vary
        self.value_col = "price_usd"       # Example, primary dependent variable

        info(f"DiagnosticAdapter initialized for Tier {tier}.")

    def get_residuals(self) -> Optional[pd.Series]:
        """
        Extract residuals from the ResultsContainer.

        Attempts to find residuals using common attribute names or methods.
        The returned Series should be indexed by entity and time.

        Returns
        -------
        Optional[pd.Series]
            A Series of residuals, indexed by (entity_id, time_id), or None if not found.
        """
        residuals = None
        if hasattr(self.results_container, 'get_residuals') and callable(self.results_container.get_residuals):
            try:
                residuals = self.results_container.get_residuals()
                info("Retrieved residuals using get_residuals()")
            except Exception as e:
                warning(f"Error calling get_residuals(): {e}")
        
        if residuals is None and hasattr(self.results_container, 'residuals'):
            residuals = self.results_container.residuals
            info("Retrieved residuals using 'residuals' attribute.")
            
        if residuals is None and hasattr(self.results_container, 'resid'): # Common in statsmodels
            residuals = self.results_container.resid
            info("Retrieved residuals using 'resid' attribute.")

        if residuals is None:
            # Try to get from tier_specific if it's a dictionary
            if hasattr(self.results_container, 'tier_specific') and isinstance(self.results_container.tier_specific, dict):
                if 'residuals' in self.results_container.tier_specific:
                    residuals = self.results_container.tier_specific['residuals']
                    info("Retrieved residuals from tier_specific['residuals']")
                elif 'resid' in self.results_container.tier_specific:
                    residuals = self.results_container.tier_specific['resid']
                    info("Retrieved residuals from tier_specific['resid']")


        if residuals is None:
            error("Residuals not found in ResultsContainer.")
            return None

        if not isinstance(residuals, pd.Series):
            try:
                residuals = pd.Series(residuals) # Convert if it's a numpy array
            except Exception as e:
                error(f"Could not convert residuals to pd.Series: {e}")
                return None
        
        # Ensure residuals have a MultiIndex (entity_id, time_id)
        # This part is crucial and might require using original_data if residuals are flat.
        if not isinstance(residuals.index, pd.MultiIndex):
            warning("Residuals do not have a MultiIndex. Attempting to align with original data.")
            if len(residuals) == len(self.original_data):
                try:
                    # Assuming original_data is sorted or can be indexed appropriately
                    # This requires original_data to have entity_id_col and time_id_col
                    if self.entity_id_col in self.original_data.columns and \
                       self.time_id_col in self.original_data.columns:
                        
                        index_df = self.original_data.set_index([self.entity_id_col, self.time_id_col])
                        if len(residuals) == len(index_df):
                             residuals.index = index_df.index
                             info("Aligned residuals with MultiIndex from original_data.")
                        else:
                            warning("Length mismatch between residuals and original_data index.")
                            # Fallback: if residuals are short, maybe they are from a subset?
                            # This part is tricky and model-dependent.
                            # For now, we'll proceed, but tests might fail.
                    else:
                        warning(f"Entity/Time ID columns ('{self.entity_id_col}', '{self.time_id_col}') not in original_data.")
                except Exception as e:
                    error(f"Failed to set MultiIndex for residuals: {e}")
                    return None # Cannot proceed if indexing fails
            else:
                warning(f"Length of residuals ({len(residuals)}) does not match original_data ({len(self.original_data)}). Cannot align index automatically.")
                # This could happen if residuals are from a model on differenced data, etc.
                # Tests needing MultiIndex might fail.

        # Name the series if it's not named, for easier pivoting in tests
        if residuals.name is None:
            residuals.name = 'resid'
            
        return residuals

    def get_panel_structure(self) -> Optional[Dict[str, Any]]:
        """
        Extract panel structure information (N, T, entity/time IDs, etc.).

        Returns
        -------
        Optional[Dict[str, Any]]
            A dictionary containing panel structure details, or None if not determinable.
            Expected keys: 'entity_id', 'time_id', 'entities', 'time_periods',
                           'is_balanced', 'N', 'T_max', 'T_min', 'T_avg', 'nobs'.
        """
        if not all(col in self.original_data.columns for col in [self.entity_id_col, self.time_id_col]):
            error(f"Entity ('{self.entity_id_col}') or Time ('{self.time_id_col}') ID not found in original_data.")
            return None

        entities = self.original_data[self.entity_id_col].unique().tolist()
        time_periods = self.original_data[self.time_id_col].unique().tolist()
        
        N = len(entities)
        
        # Calculate T stats
        counts_per_entity = self.original_data.groupby(self.entity_id_col).size()
        T_max = counts_per_entity.max() if not counts_per_entity.empty else 0
        T_min = counts_per_entity.min() if not counts_per_entity.empty else 0
        T_avg = counts_per_entity.mean() if not counts_per_entity.empty else 0
        
        is_balanced = (N > 0 and T_min == T_max and 
                       len(self.original_data) == N * T_max)
        
        nobs = len(self.original_data)

        panel_info = {
            "entity_id": self.entity_id_col,
            "time_id": self.time_id_col,
            "entities": entities,
            "time_periods": sorted(time_periods), # Sort time periods
            "is_balanced": is_balanced,
            "N": N,
            "T_max": T_max,
            "T_min": T_min,
            "T_avg": T_avg,
            "nobs": nobs,
            # Optionally, include exogenous variables if tests need them
            # "exog_data": self.get_exogenous_variables() 
        }
        info(f"Panel structure extracted: N={N}, T_avg={T_avg:.2f}, Balanced={is_balanced}")
        return panel_info

    def get_exogenous_variables(self) -> Optional[pd.DataFrame]:
        """
        Extract exogenous variables (design matrix X) if available.

        Returns
        -------
        Optional[pd.DataFrame]
            DataFrame of exogenous variables, or None.
            Should be indexed consistently with residuals if possible.
        """
        exog = None
        if hasattr(self.results_container, 'model') and hasattr(self.results_container.model, 'exog'):
            exog_data = self.results_container.model.exog
            info("Retrieved exog_data from results_container.model.exog")
            if isinstance(exog_data, np.ndarray):
                # Try to get feature names if possible
                feature_names = None
                if hasattr(self.results_container.model, 'exog_names'):
                    feature_names = self.results_container.model.exog_names
                exog = pd.DataFrame(exog_data, columns=feature_names)
            elif isinstance(exog_data, pd.DataFrame):
                exog = exog_data
            else:
                warning("Exog data is not a recognized type (numpy.ndarray or pd.DataFrame).")

        if exog is None and hasattr(self.results_container, 'exog'):
             exog_data = self.results_container.exog
             info("Retrieved exog_data from results_container.exog")
             if isinstance(exog_data, pd.DataFrame):
                 exog = exog_data
             # Add more handling if needed

        if exog is None:
            warning("Exogenous variables not found in ResultsContainer.")
            # As a fallback, could try to reconstruct from original_data if config specifies var names
            # This is complex and model-dependent.
            return None
        
        # Attempt to align index similar to residuals
        if not isinstance(exog.index, pd.MultiIndex) and \
           self.entity_id_col in self.original_data.columns and \
           self.time_id_col in self.original_data.columns:
            if len(exog) == len(self.original_data):
                exog.index = self.original_data.set_index([self.entity_id_col, self.time_id_col]).index
                info("Aligned exog_data with MultiIndex from original_data.")
            else:
                warning(f"Length of exog_data ({len(exog)}) does not match original_data ({len(self.original_data)}).")

        return exog

    def get_series(self, series_name: str) -> Optional[pd.Series]:
        """
        Extract a specific series from the original data, indexed by entity and time.

        Parameters
        ----------
        series_name : str
            The name of the column in the original_data to extract.

        Returns
        -------
        Optional[pd.Series]
            A Series indexed by (entity_id, time_id), or None if not found.
        """
        if series_name not in self.original_data.columns:
            error(f"Series '{series_name}' not found in original data.")
            return None
        
        if not all(col in self.original_data.columns for col in [self.entity_id_col, self.time_id_col]):
            error(f"Entity ('{self.entity_id_col}') or Time ('{self.time_id_col}') ID not found for series extraction.")
            return None
            
        try:
            series_data = self.original_data.set_index([self.entity_id_col, self.time_id_col])[series_name]
            info(f"Retrieved series '{series_name}' with MultiIndex.")
            return series_data
        except Exception as e:
            error(f"Failed to extract series '{series_name}' with MultiIndex: {e}")
            return None

    def get_fitted_values(self) -> Optional[pd.Series]:
        """
        Extract fitted values from the ResultsContainer.
        
        Returns
        -------
        Optional[pd.Series]
            Fitted values indexed by (entity_id, time_id), or None if not found.
        """
        # Try different attributes where fitted values might be stored
        fitted = None
        
        # Check for fitted_values attribute
        if hasattr(self.results_container, 'fitted_values'):
            fitted = self.results_container.fitted_values
            info("Retrieved fitted values from results_container.fitted_values")
        
        # Check in results dict
        elif 'fitted_values' in self.results_container.results:
            fitted = self.results_container.results['fitted_values']
            info("Retrieved fitted values from results_container.results['fitted_values']")
        
        # Check for fittedvalues (no underscore)
        elif hasattr(self.results_container, 'fittedvalues'):
            fitted = self.results_container.fittedvalues
            info("Retrieved fitted values from results_container.fittedvalues")
        
        # Check if model has predict method
        elif hasattr(self.results_container, 'model') and hasattr(self.results_container.model, 'fittedvalues'):
            fitted = self.results_container.model.fittedvalues
            info("Retrieved fitted values from results_container.model.fittedvalues")
        
        if fitted is None:
            warning("Fitted values not found in ResultsContainer.")
            return None
        
        # Convert to Series if needed
        if isinstance(fitted, np.ndarray):
            fitted = pd.Series(fitted)
            info("Converted fitted values from numpy array to pandas Series")
        
        # Align index if needed
        if not isinstance(fitted.index, pd.MultiIndex) and self.original_data is not None:
            if self.entity_id_col in self.original_data.columns and self.time_id_col in self.original_data.columns:
                if len(fitted) == len(self.original_data):
                    fitted.index = self.original_data.set_index([self.entity_id_col, self.time_id_col]).index
                    info("Aligned fitted values with MultiIndex from original_data.")
                else:
                    warning(f"Length of fitted values ({len(fitted)}) does not match original_data ({len(self.original_data)}).")
        
        return fitted
