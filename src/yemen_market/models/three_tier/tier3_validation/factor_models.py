"""Factor models for Tier 3 validation analysis.

This module implements static and dynamic factor models to extract common
components from the multi-market, multi-commodity price panel. These factors
help validate market integration patterns found in Tiers 1 and 2.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from sklearn.decomposition import PCA, FactorAnalysis
from sklearn.preprocessing import StandardScaler
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller
from scipy import stats

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_data_shape, bind, log_metric
)
from yemen_market.models.three_tier.core.base_model import BaseThreeTierModel, TierType
from yemen_market.models.three_tier.core.results_container import ResultsContainer
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler

# Set module context
bind(module=__name__)


class StaticFactorModel(BaseThreeTierModel):
    """Static factor model using PCA for dimension reduction.
    
    This model extracts time-invariant factors from the price panel to
    identify common drivers of price movements across markets and commodities.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize static factor model.
        
        Parameters
        ----------
        config : dict, optional
            Configuration with keys:
            - n_factors: Number of factors to extract (default: auto-select)
            - standardize: Whether to standardize data (default: True)
            - min_variance_explained: Min cumulative variance for auto-selection (default: 0.8)
            - max_factors: Maximum factors for auto-selection (default: 10)
        """
        super().__init__(config)
        self.tier = TierType.TIER3_VALIDATION
        self.tier_type = TierType.TIER3_VALIDATION
        
        # Model configuration
        self.n_factors = config.get('n_factors', None)
        self.standardize = config.get('standardize', True)
        self.min_variance_explained = config.get('min_variance_explained', 0.8)
        self.max_factors = config.get('max_factors', 10)
        
        # Model components
        self.scaler = StandardScaler() if self.standardize else None
        self.pca = None
        self.factor_scores = None
        self.loadings = None
        
        # Configure panel handler with correct column names
        from yemen_market.models.three_tier.core.panel_data_handler import PanelDataConfig
        panel_config = PanelDataConfig(
            required_columns=['date', 'governorate', 'commodity', 'usd_price'],
            market_col='governorate',
            commodity_col='commodity',
            time_col='date',
            price_col='usd_price'
        )
        self.panel_handler = PanelDataHandler(panel_config)
        
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data for factor analysis.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input panel data
            
        Returns
        -------
        bool
            True if valid, False otherwise
        """
        with timer("validate_factor_data"):
            # Check 3D structure
            if not self.panel_handler.validate_3d_structure(data):
                return False
                
            # Convert to wide matrix
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            
            # Check dimensions
            n_periods, n_series = wide_matrix.shape
            if n_periods < n_series:
                warning(f"More series ({n_series}) than time periods ({n_periods}). "
                       "Consider using fewer factors.")
            
            # Check missing data
            missing_pct = wide_matrix.isna().sum().sum() / (n_periods * n_series) * 100
            if missing_pct > 50:
                error(f"Too many missing values: {missing_pct:.1f}%")
                return False
            
            info(f"Data validated for factor analysis: {n_periods} periods × {n_series} series")
            return True
    
    def fit(self, data: pd.DataFrame, **kwargs) -> 'StaticFactorModel':
        """Fit static factor model using PCA.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data with columns: governorate, commodity, date, usd_price
        **kwargs
            Additional parameters
            
        Returns
        -------
        self
            Fitted model
        """
        with timer("fit_static_factor_model"):
            self._log_estimation_start(data)
            
            # Validate data
            if not self.validate_data(data):
                raise ValueError("Data validation failed")
            
            # Convert to wide matrix
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            log_data_shape("wide_matrix", wide_matrix)
            
            # Handle missing data
            info("Handling missing values with forward fill and interpolation")
            wide_matrix = wide_matrix.ffill(limit=2).bfill(limit=2)
            wide_matrix = wide_matrix.interpolate(method='linear', limit=5)
            
            # Drop series with too many missing values
            missing_threshold = 0.3
            series_to_keep = wide_matrix.columns[
                wide_matrix.isna().sum() < len(wide_matrix) * missing_threshold
            ]
            wide_matrix = wide_matrix[series_to_keep]
            info(f"Kept {len(series_to_keep)} out of {len(wide_matrix.columns)} series")
            
            # Fill remaining missing values
            wide_matrix = wide_matrix.fillna(wide_matrix.mean())
            
            # Standardize if requested
            if self.standardize:
                data_scaled = self.scaler.fit_transform(wide_matrix)
            else:
                data_scaled = wide_matrix.values
                
            # Determine number of factors
            if self.n_factors is None:
                self.n_factors = self._select_n_factors(data_scaled)
                info(f"Auto-selected {self.n_factors} factors")
            
            # Fit PCA
            info(f"Fitting PCA with {self.n_factors} factors")
            self.pca = PCA(n_components=self.n_factors, random_state=42)
            self.factor_scores = self.pca.fit_transform(data_scaled)
            
            # Store loadings
            self.loadings = pd.DataFrame(
                self.pca.components_.T,
                index=wide_matrix.columns,
                columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
            )
            
            # Create results container
            self.results = self._create_results(wide_matrix)
            self.data = data
            self.is_fitted = True
            
            self._log_estimation_complete(0.0)  # Timer context manager handles timing
            
            return self
    
    def _select_n_factors(self, data: np.ndarray) -> int:
        """Auto-select number of factors based on variance explained.
        
        Parameters
        ----------
        data : np.ndarray
            Standardized data matrix
            
        Returns
        -------
        int
            Optimal number of factors
        """
        with timer("select_n_factors"):
            # Fit PCA with max components
            pca_full = PCA(n_components=min(self.max_factors, data.shape[1]))
            pca_full.fit(data)
            
            # Find number of factors for target variance
            cumsum_var = np.cumsum(pca_full.explained_variance_ratio_)
            n_factors = np.argmax(cumsum_var >= self.min_variance_explained) + 1
            
            # Apply Kaiser criterion (eigenvalue > 1)
            if self.standardize:
                eigenvalues = pca_full.explained_variance_
                kaiser_n = np.sum(eigenvalues > 1)
                n_factors = min(n_factors, kaiser_n)
                info(f"Kaiser criterion suggests {kaiser_n} factors")
            
            return max(1, min(n_factors, self.max_factors))
    
    def _create_results(self, wide_matrix: pd.DataFrame) -> ResultsContainer:
        """Create results container with factor analysis outputs.
        
        Parameters
        ----------
        wide_matrix : pd.DataFrame
            Wide format data used for analysis
            
        Returns
        -------
        ResultsContainer
            Populated results
        """
        results = ResultsContainer(
            commodity="all",  # Factor models analyze all commodities together
            model_type="static_factor_model",
            results={},
            diagnostics={},
            metadata={}
        )
        
        # Basic metadata
        results.metadata['n_observations'] = len(wide_matrix)
        results.metadata['n_series'] = len(wide_matrix.columns)
        results.metadata['n_factors'] = self.n_factors
        
        # Variance explained
        variance_explained = self.pca.explained_variance_ratio_
        cumulative_variance = np.cumsum(variance_explained)
        
        # Initialize tier_specific results
        results.results['tier_specific'] = {
            'variance_explained': variance_explained.tolist(),
            'cumulative_variance': cumulative_variance.tolist(),
            'factor_scores': self.factor_scores,
            'loadings': self.loadings,
            'eigenvalues': self.pca.explained_variance_.tolist()
        }
        
        # Add factor statistics as "coefficients"
        for i in range(self.n_factors):
            results.coefficients[f"Factor_{i+1}_variance"] = variance_explained[i]
            results.standard_errors[f"Factor_{i+1}_variance"] = 0.0  # No standard error for variance explained
        
        # Set fit statistics
        results.fit_statistics = {
            'n_observations': len(wide_matrix),
            'n_parameters': self.n_factors,
            'log_likelihood': 0.0,  # Not applicable for PCA
            'r_squared': cumulative_variance[-1]  # Use total variance explained as R2 proxy
        }
        
        # Log key metrics
        log_metric("n_factors", self.n_factors)
        log_metric("total_variance_explained", cumulative_variance[-1])
        log_metric("first_factor_variance", variance_explained[0])
        
        # Interpretation of loadings
        self._interpret_loadings(results)
        
        return results
    
    def _interpret_loadings(self, results: ResultsContainer) -> None:
        """Interpret factor loadings to identify patterns.
        
        Parameters
        ----------
        results : ResultsContainer
            Results container to update
        """
        # Find dominant loadings for each factor
        interpretations = []
        
        for factor in self.loadings.columns:
            # Get top positive and negative loadings
            factor_loads = self.loadings[factor]
            top_positive = factor_loads.nlargest(5)
            top_negative = factor_loads.nsmallest(5)
            
            # Extract patterns (market or commodity)
            positive_entities = [idx.split('_') for idx in top_positive.index]
            negative_entities = [idx.split('_') for idx in top_negative.index]
            
            # Check if factor represents specific commodity
            positive_commodities = [e[1] for e in positive_entities]
            if len(set(positive_commodities)) == 1:
                interpretations.append(f"{factor}: {positive_commodities[0]} price factor")
            
            # Check if factor represents specific region
            positive_markets = [e[0] for e in positive_entities]
            if len(set(positive_markets)) <= 2:
                interpretations.append(f"{factor}: Regional factor ({', '.join(set(positive_markets))})")
        
        results.results['tier_specific']['factor_interpretations'] = interpretations
        
        # Add warnings if factors are hard to interpret
        if not interpretations:
            if 'warnings' not in results.metadata:
                results.metadata['warnings'] = []
            results.metadata['warnings'].append(
                "Factor loadings do not show clear commodity or regional patterns"
            )
    
    def predict(self, data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Transform new data using fitted factors.
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            New data to transform
            
        Returns
        -------
        pd.Series
            Factor scores for new data
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
            
        # For factor models, we return factor scores rather than predictions
        if data is None:
            # Return training factor scores
            return pd.DataFrame(
                self.factor_scores,
                columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
            )
        
        # Transform new data
        wide_matrix = self.panel_handler.create_wide_matrix(data)
        
        # Ensure same columns as training
        train_cols = self.loadings.index
        wide_matrix = wide_matrix.reindex(columns=train_cols, fill_value=0)
        
        # Handle missing values
        wide_matrix = wide_matrix.fillna(wide_matrix.mean())
        
        # Standardize if needed
        if self.standardize:
            data_scaled = self.scaler.transform(wide_matrix)
        else:
            data_scaled = wide_matrix.values
            
        # Transform to factor space
        factor_scores = self.pca.transform(data_scaled)
        
        return pd.DataFrame(
            factor_scores,
            index=wide_matrix.index,
            columns=[f'Factor_{i+1}' for i in range(self.n_factors)]
        )
    
    def get_factor_contributions(self, entity: str) -> pd.Series:
        """Get factor contributions for a specific market-commodity pair.
        
        Parameters
        ----------
        entity : str
            Market-commodity identifier (e.g., "Sana'a_wheat")
            
        Returns
        -------
        pd.Series
            Factor loadings for the entity
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        if entity not in self.loadings.index:
            raise ValueError(f"Entity {entity} not found in model")
            
        return self.loadings.loc[entity]


class DynamicFactorModel(BaseThreeTierModel):
    """Dynamic factor model capturing time-varying factors.
    
    This model extends static factor analysis by allowing factors to evolve
    over time, capturing structural changes in market integration.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize dynamic factor model.
        
        Parameters
        ----------
        config : dict, optional
            Configuration with keys:
            - n_factors: Number of dynamic factors (default: 3)
            - ar_lags: AR lags for factor dynamics (default: 1)
            - allow_structural_breaks: Test for breaks (default: True)
        """
        super().__init__(config)
        self.tier = TierType.TIER3_VALIDATION
        self.tier_type = TierType.TIER3_VALIDATION
        
        # Model configuration
        self.n_factors = config.get('n_factors', 3)
        self.ar_lags = config.get('ar_lags', 1)
        self.allow_structural_breaks = config.get('allow_structural_breaks', True)
        
        # Model components
        # Configure panel handler with correct column names
        from yemen_market.models.three_tier.core.panel_data_handler import PanelDataConfig
        panel_config = PanelDataConfig(
            required_columns=['date', 'governorate', 'commodity', 'usd_price'],
            market_col='governorate',
            commodity_col='commodity',
            time_col='date',
            price_col='usd_price'
        )
        self.panel_handler = PanelDataHandler(panel_config)
        self.static_model = None  # For initialization
        self.state_space_model = None
        self.filtered_states = None
        self.smoothed_states = None
        
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate data for dynamic factor model.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input panel data
            
        Returns
        -------
        bool
            True if valid
        """
        # Use same validation as static model
        static_validator = StaticFactorModel(self.config)
        is_valid = static_validator.validate_data(data)
        
        if is_valid:
            # Additional checks for time series
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            
            # Check for sufficient time periods
            n_periods = len(wide_matrix)
            min_periods = 50  # Need enough for dynamics
            
            if n_periods < min_periods:
                warning(f"Only {n_periods} time periods. Recommend at least {min_periods} for dynamic factors.")
                
        return is_valid
    
    def fit(self, data: pd.DataFrame, **kwargs) -> 'DynamicFactorModel':
        """Fit dynamic factor model using state space methods.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
        **kwargs
            Additional parameters
            
        Returns
        -------
        self
            Fitted model
        """
        with timer("fit_dynamic_factor_model"):
            self._log_estimation_start(data)
            
            # Validate data
            if not self.validate_data(data):
                raise ValueError("Data validation failed")
                
            # Get wide matrix
            wide_matrix = self.panel_handler.create_wide_matrix(data)
            
            # Initialize with static factors
            info("Initializing with static factor model")
            self.static_model = StaticFactorModel({
                'n_factors': self.n_factors,
                'standardize': True
            })
            self.static_model.fit(data)
            
            # Set up state space model
            self._setup_state_space(wide_matrix)
            
            # Estimate model
            info("Estimating dynamic factor model")
            with progress("Fitting state space model", total=1) as update:
                results = self.state_space_model.fit(disp=False)
                update(1)
                
            # Extract states
            self.filtered_states = results.filtered_state
            self.smoothed_states = results.smoothed_state
            
            # Test for structural breaks if requested
            if self.allow_structural_breaks:
                self._test_structural_breaks()
            
            # Create results
            self.results = self._create_dynamic_results(results, wide_matrix)
            self.data = data
            self.is_fitted = True
            
            self._log_estimation_complete(0.0)  # Timer context manager handles timing
            
            return self
    
    def _setup_state_space(self, wide_matrix: pd.DataFrame) -> None:
        """Set up state space representation.
        
        Parameters
        ----------
        wide_matrix : pd.DataFrame
            Wide format data
        """
        # Simplified DFM using statsmodels
        # In practice, might use more sophisticated packages
        from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor
        
        # Handle missing values
        wide_matrix_filled = wide_matrix.ffill().bfill().fillna(0)
        
        # Create dynamic factor model
        self.state_space_model = DynamicFactor(
            wide_matrix_filled,
            k_factors=self.n_factors,
            factor_order=self.ar_lags,
            error_order=0  # No MA component for simplicity
        )
        
        info(f"Set up dynamic factor model with {self.n_factors} factors and AR({self.ar_lags})")
    
    def _test_structural_breaks(self) -> None:
        """Test for structural breaks in factor dynamics."""
        if self.smoothed_states is None:
            return
            
        # Simple Chow test on factor means
        factors = self.smoothed_states[:self.n_factors].T
        n_obs = len(factors)
        
        # Test at 1/3 and 2/3 points
        break_points = [n_obs // 3, 2 * n_obs // 3]
        
        for bp in break_points:
            for i in range(self.n_factors):
                factor_series = factors[:, i]
                
                # Split series
                series1 = factor_series[:bp]
                series2 = factor_series[bp:]
                
                # Simple t-test for mean difference
                t_stat, p_value = stats.ttest_ind(series1, series2)
                
                if p_value < 0.05:
                    warning(f"Potential structural break in Factor {i+1} at observation {bp}")
    
    def _create_dynamic_results(self, ss_results: Any, 
                               wide_matrix: pd.DataFrame) -> ResultsContainer:
        """Create results for dynamic factor model.
        
        Parameters
        ----------
        ss_results : statsmodels results
            State space model results
        wide_matrix : pd.DataFrame
            Data used for estimation
            
        Returns
        -------
        ResultsContainer
            Populated results
        """
        results = ResultsContainer(
            commodity="all",
            model_type="dynamic_factor_model",
            results={},
            diagnostics={},
            metadata={}
        )
        
        # Basic info
        results.metadata['n_observations'] = len(wide_matrix)
        results.metadata['n_series'] = len(wide_matrix.columns)
        results.metadata['n_factors'] = self.n_factors
        results.metadata['convergence'] = ss_results.mle_retvals['converged']
        
        # Model fit statistics - handle potential missing attributes
        n_params = getattr(ss_results, 'nobs_effective', self.n_factors * self.ar_lags)
        log_lik = getattr(ss_results, 'llf', -1000.0)
        
        # Store fit statistics
        results.fit_statistics = {
            'n_observations': len(wide_matrix),
            'n_parameters': n_params,
            'log_likelihood': log_lik,
            'r_squared': 0.0  # Placeholder - could compute pseudo R2
        }
        
        # Store factor dynamics
        results.results['tier_specific'] = {
            'filtered_factors': self.filtered_states[:self.n_factors].T,
            'smoothed_factors': self.smoothed_states[:self.n_factors].T,
            'factor_loadings': getattr(ss_results, 'coefficients_of_determination', None),
            'transition_matrix': getattr(ss_results, 'transition', np.eye(self.n_factors))[:self.n_factors, :self.n_factors]
        }
        
        # Add AR coefficients as main results
        transition = results.results['tier_specific']['transition_matrix']
        for i in range(self.n_factors):
            for j in range(self.n_factors):
                if i == j:  # Diagonal elements are AR coefficients
                    results.coefficients[f"Factor_{i+1}_AR1"] = transition[i, j]
                    results.standard_errors[f"Factor_{i+1}_AR1"] = 0.1  # Placeholder - would need to extract from results
        
        return results
    
    def predict(self, data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Generate dynamic factor predictions.
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            New data for prediction
            
        Returns
        -------
        pd.Series
            Factor predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        # Return smoothed factor estimates
        factors_df = pd.DataFrame(
            self.smoothed_states[:self.n_factors].T,
            columns=[f'DynamicFactor_{i+1}' for i in range(self.n_factors)]
        )
        
        return factors_df
    
    def get_time_varying_loadings(self, period: int) -> pd.DataFrame:
        """Get factor loadings at a specific time period.
        
        Parameters
        ----------
        period : int
            Time period index
            
        Returns
        -------
        pd.DataFrame
            Loadings at specified period
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
            
        # For now, return static loadings
        # Full implementation would have time-varying loadings
        return self.static_model.loadings