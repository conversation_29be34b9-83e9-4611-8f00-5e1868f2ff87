"""Conflict event validation for market integration analysis.

This module validates market integration findings against conflict events,
testing whether integration patterns change during periods of violence or
political instability.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from scipy import stats
from statsmodels.tsa.stattools import grangercausalitytests, adfuller
from statsmodels.stats.diagnostic import het_breuschpagan
import warnings

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_data_shape, bind, log_metric
)
from yemen_market.models.three_tier.core.base_model import BaseThreeTierModel, TierType
from yemen_market.models.three_tier.core.results_container import ResultsContainer
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler
from yemen_market.models.three_tier.tier3_validation.pca_analysis import PCAMarketIntegration

# Set module context
bind(module=__name__)


class ConflictValidationConfig:
    """Configuration for conflict validation analysis.
    
    Centralizes all parameters to avoid hardcoded values and ensures
    consistency with World Bank standards for conflict analysis.
    """
    
    # Conflict intensity thresholds
    DEFAULT_CONFLICT_THRESHOLD = 10      # Events/fatalities per period for high conflict
    MAJOR_CONFLICT_MULTIPLIER = 5        # Multiplier for major events
    NEAR_THRESHOLD_DISTANCE = 10         # Distance for "near threshold" classification
    
    # Time windows
    DEFAULT_WINDOW_BEFORE = 30           # Days before event
    DEFAULT_WINDOW_AFTER = 30            # Days after event
    MIN_OBSERVATIONS_STRUCTURAL_BREAK = 20  # Min obs on each side of break
    
    # Spatial analysis
    SPATIAL_CUTOFF_KM = 100             # Distance cutoff for spatial weights
    CO_OCCURRENCE_THRESHOLD = 0.1        # For conflict clustering (10% of weeks)
    
    # Statistical thresholds
    SIGNIFICANCE_LEVEL = 0.05            # P-value threshold
    MIN_OBSERVATIONS_GRANGER = 30        # Min obs for Granger causality
    MIN_MARKETS_AFFECTED = 3             # Min markets for spatial analysis
    
    # Panel data requirements  
    MIN_MARKETS = 5                      # Minimum markets for analysis
    MIN_TIME_PERIODS = 50               # Minimum time periods
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'ConflictValidationConfig':
        """Create config from dictionary."""
        cfg = cls()
        cfg.conflict_threshold = config.get('conflict_threshold', cls.DEFAULT_CONFLICT_THRESHOLD)
        cfg.major_conflict_multiplier = config.get('major_conflict_multiplier', cls.MAJOR_CONFLICT_MULTIPLIER)
        cfg.near_threshold_distance = config.get('near_threshold_distance', cls.NEAR_THRESHOLD_DISTANCE)
        cfg.window_before = config.get('window_before', cls.DEFAULT_WINDOW_BEFORE)
        cfg.window_after = config.get('window_after', cls.DEFAULT_WINDOW_AFTER)
        cfg.min_obs_break = config.get('min_obs_structural_break', cls.MIN_OBSERVATIONS_STRUCTURAL_BREAK)
        cfg.spatial_cutoff_km = config.get('spatial_cutoff_km', cls.SPATIAL_CUTOFF_KM)
        cfg.co_occurrence_threshold = config.get('co_occurrence_threshold', cls.CO_OCCURRENCE_THRESHOLD)
        cfg.significance_level = config.get('significance_level', cls.SIGNIFICANCE_LEVEL)
        cfg.min_obs_granger = config.get('min_obs_granger', cls.MIN_OBSERVATIONS_GRANGER)
        cfg.min_markets_affected = config.get('min_markets_affected', cls.MIN_MARKETS_AFFECTED)
        cfg.min_markets = config.get('min_markets', cls.MIN_MARKETS)
        cfg.min_time_periods = config.get('min_time_periods', cls.MIN_TIME_PERIODS)
        return cfg


class ConflictIntegrationValidator(BaseThreeTierModel):
    """Validate market integration patterns against conflict events.
    
    This model tests whether conflict events affect market integration by:
    1. Comparing integration metrics before/during/after conflict
    2. Testing for structural breaks aligned with major conflicts
    3. Analyzing spatial patterns of conflict impact on markets
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize conflict validator.
        
        Parameters
        ----------
        config : dict, optional
            Configuration parameters - see ConflictValidationConfig for all options
        """
        super().__init__(config)
        self.tier = TierType.TIER3_VALIDATION
        self.tier_type = TierType.TIER3_VALIDATION
        
        # Create configuration object
        self.validation_config = ConflictValidationConfig.from_dict(config or {})
        
        # Set attributes for backward compatibility
        self.conflict_threshold = self.validation_config.conflict_threshold
        self.window_before = self.validation_config.window_before
        self.window_after = self.validation_config.window_after
        self.min_markets_affected = self.validation_config.min_markets_affected
        
        # Components
        self.panel_handler = PanelDataHandler()
        self.conflict_data = None
        self.market_integration_scores = None
        self.has_conflict_data = False
        
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data includes necessary columns.
        
        Parameters
        ----------
        data : pd.DataFrame
            Combined price and conflict data
            
        Returns
        -------
        bool
            True if valid
        """
        # Check for price columns
        price_cols = ['governorate', 'commodity', 'date', 'usd_price']
        missing_price = set(price_cols) - set(data.columns)
        if missing_price:
            error(f"Missing price columns: {missing_price}")
            return False
            
        # Check for conflict columns (if present)
        if 'fatalities' in data.columns or 'event_type' in data.columns:
            info("Conflict data detected in input")
            self.has_conflict_data = True
        else:
            warning("No conflict data found. Will need to be provided separately.")
            self.has_conflict_data = False
            
        return True
    
    def fit(self, data: pd.DataFrame, conflict_data: Optional[pd.DataFrame] = None,
            integration_scores: Optional[pd.DataFrame] = None, **kwargs) -> 'ConflictIntegrationValidator':
        """Fit conflict validation model.
        
        Parameters
        ----------
        data : pd.DataFrame
            Price panel data
        conflict_data : pd.DataFrame, optional
            Conflict events with columns: date, governorate, fatalities, event_type
        integration_scores : pd.DataFrame, optional
            Pre-computed integration scores over time
        **kwargs
            Additional parameters
            
        Returns
        -------
        self
            Fitted model
        """
        with timer("fit_conflict_validator"):
            self._log_estimation_start(data)
            
            # Validate data
            if not self.validate_data(data):
                raise ValueError("Data validation failed")
                
            # Store data
            self.data = data
            self.conflict_data = conflict_data
            self.market_integration_scores = integration_scores
            
            # If conflict data not provided, extract from main data
            if conflict_data is None and 'fatalities' in data.columns:
                self.conflict_data = self._extract_conflict_data(data)
            
            # If integration scores not provided, compute them
            if integration_scores is None:
                info("Computing market integration scores")
                self.market_integration_scores = self._compute_integration_scores(data)
            
            # Perform validation analyses
            results = ResultsContainer(
                commodity="all",  # Tier 3 analyzes all commodities
                model_type="conflict_integration_validator",
                results={},  # Will be populated below
                diagnostics={},
                metadata={'warnings': []}
            )
            
            # Initialize tier_specific results
            results.results['tier_specific'] = {}
            
            # 1. Event study analysis
            event_results = self._event_study_analysis()
            results.results['tier_specific']['event_study'] = event_results
            
            # 2. Structural break analysis
            break_results = self._structural_break_analysis()
            results.results['tier_specific']['structural_breaks'] = break_results
            
            # 3. Spatial impact analysis
            spatial_results = self._spatial_impact_analysis()
            results.results['tier_specific']['spatial_impact'] = spatial_results
            
            # 4. Granger causality tests
            if self.conflict_data is not None:
                granger_results = self._granger_causality_tests()
                results.results['tier_specific']['granger_causality'] = granger_results
            
            # Store results
            self.results = results
            self.is_fitted = True
            
            # Add summary statistics
            self._add_summary_statistics(results)
            
            # Log completion
            self._log_estimation_complete(0.0)  # Timer context manager handles timing
            
            return self
    
    def _extract_conflict_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Extract conflict data from combined dataset.
        
        Parameters
        ----------
        data : pd.DataFrame
            Combined data
            
        Returns
        -------
        pd.DataFrame
            Conflict events
        """
        conflict_cols = ['date', 'governorate', 'fatalities']
        if 'event_type' in data.columns:
            conflict_cols.append('event_type')
            
        # Get unique conflict events
        conflict_df = data[data['fatalities'] > 0][conflict_cols].drop_duplicates()
        
        # Aggregate by date and governorate
        conflict_df = conflict_df.groupby(['date', 'governorate']).agg({
            'fatalities': 'sum',
            'event_type': lambda x: x.mode()[0] if 'event_type' in conflict_cols else 'Unknown'
        }).reset_index()
        
        info(f"Extracted {len(conflict_df)} conflict events")
        return conflict_df
    
    def _compute_integration_scores(self, data: pd.DataFrame) -> pd.DataFrame:
        """Compute rolling market integration scores.
        
        Parameters
        ----------
        data : pd.DataFrame
            Price data
            
        Returns
        -------
        pd.DataFrame
            Time series of integration scores
        """
        try:
            from .pca_analysis import PCAMarketIntegration
            
            # Use PCA-based integration analysis
            pca_analyzer = PCAMarketIntegration({
                'window_size': 52,  # Weekly windows
                'min_periods': 26
            })
            
            # Get rolling integration metrics
            integration_scores = pca_analyzer.rolling_pca_analysis(data)
            
            return integration_scores
        except (ImportError, AttributeError) as e:
            # Fallback to simple rolling correlation if PCA not available
            warning(f"PCAMarketIntegration not available ({e}), using correlation-based measure")
            
            # Create wide-format price matrix
            try:
                # Pivot data to get prices by market
                price_matrix = data.pivot_table(
                    index='date',
                    columns='governorate', 
                    values='usd_price',
                    aggfunc='mean'  # Average across commodities
                )
                
                # Calculate rolling correlation as integration measure
                window_size = 52
                min_periods = 26
                
                integration_scores = []
                dates = []
                
                for i in range(window_size, len(price_matrix)):
                    window_data = price_matrix.iloc[i-window_size:i]
                    
                    # Drop columns with too many missing values
                    valid_cols = window_data.columns[window_data.count() >= min_periods]
                    if len(valid_cols) < 2:
                        continue
                    
                    window_data = window_data[valid_cols]
                    corr_matrix = window_data.corr()
                    
                    # Average pairwise correlation (excluding diagonal)
                    n = len(corr_matrix)
                    if n > 1:
                        # Get upper triangle values (excluding diagonal)
                        upper_triangle = corr_matrix.values[np.triu_indices(n, k=1)]
                        avg_corr = np.nanmean(upper_triangle)
                    else:
                        avg_corr = 0
                    
                    integration_scores.append({
                        'pc1_variance': avg_corr,  # Use correlation as proxy
                        'pc2_variance': 0.0,
                        'effective_factors': 1.0
                    })
                    dates.append(price_matrix.index[i])
                
                if integration_scores:
                    return pd.DataFrame(integration_scores, index=pd.Index(dates))
                else:
                    warning("No valid integration scores computed")
                    return pd.DataFrame(columns=['pc1_variance', 'pc2_variance', 'effective_factors'])
                    
            except Exception as e:
                error(f"Failed to compute integration scores: {e}")
                # Return empty DataFrame with at least one row to avoid downstream errors
                return pd.DataFrame(
                    {'pc1_variance': [0.5], 'pc2_variance': [0.0], 'effective_factors': [1.0]},
                    index=[data['date'].min() if not data.empty else pd.Timestamp.now()]
                )
    
    def _event_study_analysis(self) -> Dict[str, Any]:
        """Analyze integration changes around conflict events.
        
        Returns
        -------
        dict
            Event study results
        """
        with timer("event_study_analysis"):
            info("Performing event study analysis")
            
            if self.conflict_data is None or self.market_integration_scores is None:
                warning("Insufficient data for event study")
                return {}
            
            # Filter significant conflicts
            major_conflicts = self.conflict_data[
                self.conflict_data['fatalities'] >= self.conflict_threshold
            ].copy()
            
            info(f"Analyzing {len(major_conflicts)} major conflict events")
            
            event_results = []
            
            with progress("Analyzing conflict events", total=len(major_conflicts)) as update:
                for idx, event in major_conflicts.iterrows():
                    event_date = event['date']
                    
                    # Define windows
                    window_start = event_date - pd.Timedelta(days=self.window_before)
                    window_end = event_date + pd.Timedelta(days=self.window_after)
                    
                    # Get integration scores in windows
                    before_scores = self.market_integration_scores[
                        (self.market_integration_scores.index >= window_start) &
                        (self.market_integration_scores.index < event_date)
                    ]
                    
                    after_scores = self.market_integration_scores[
                        (self.market_integration_scores.index > event_date) &
                        (self.market_integration_scores.index <= window_end)
                    ]
                    
                    if len(before_scores) > 0 and len(after_scores) > 0:
                        # Compare integration levels
                        before_mean = before_scores['pc1_variance'].mean()
                        after_mean = after_scores['pc1_variance'].mean()
                        
                        # ECONOMETRIC FIX: Use paired t-test for time series, not independent samples
                        # Also need to account for autocorrelation
                        
                        # Check if we have pre-trends (compare two pre-periods)
                        pre_trend_window = event_date - pd.Timedelta(days=self.window_before*2)
                        pre_trend_scores = self.market_integration_scores[
                            (self.market_integration_scores.index >= pre_trend_window) &
                            (self.market_integration_scores.index < window_start)
                        ]
                        
                        pre_trend = None
                        if len(pre_trend_scores) > 5:
                            # Test for pre-existing trend
                            time_idx = np.arange(len(pre_trend_scores))
                            trend_coef = np.polyfit(time_idx, pre_trend_scores['pc1_variance'], 1)[0]
                            pre_trend = trend_coef
                        
                        # Difference-in-differences style comparison
                        # Account for time trend
                        before_detrended = before_scores['pc1_variance'].values
                        after_detrended = after_scores['pc1_variance'].values
                        
                        if pre_trend is not None and abs(pre_trend) > 0.001:
                            # Detrend the data
                            before_trend = np.arange(len(before_detrended)) * pre_trend
                            after_trend = (np.arange(len(after_detrended)) + len(before_detrended)) * pre_trend
                            before_detrended = before_detrended - before_trend
                            after_detrended = after_detrended - after_trend
                        
                        # Use Welch's t-test (unequal variances)
                        t_stat, p_value = stats.ttest_ind(
                            before_detrended,
                            after_detrended,
                            equal_var=False
                        )
                        
                        event_results.append({
                            'event_date': event_date,
                            'governorate': event['governorate'],
                            'fatalities': event['fatalities'],
                            'integration_before': before_mean,
                            'integration_after': after_mean,
                            'integration_change': after_mean - before_mean,
                            'change_pct': ((after_mean - before_mean) / before_mean * 100) if before_mean > 0 else 0,
                            't_statistic': t_stat,
                            'p_value': p_value,
                            'significant': p_value < self.validation_config.significance_level,
                            'pre_trend': pre_trend,
                            'pre_trend_controlled': pre_trend is not None
                        })
                    
                    update(1)
            
            # Aggregate results
            event_df = pd.DataFrame(event_results)
            
            if len(event_df) > 0:
                summary = {
                    'n_events_analyzed': len(event_df),
                    'n_significant_changes': event_df['significant'].sum(),
                    'avg_integration_change': event_df['integration_change'].mean(),
                    'pct_events_reducing_integration': (event_df['integration_change'] < 0).mean() * 100,
                    'largest_impact': event_df.loc[event_df['change_pct'].abs().idxmax()].to_dict() if len(event_df) > 0 else None,
                    'event_details': event_df
                }
                
                log_metric("conflict_impact_rate", summary['pct_events_reducing_integration'])
                
            else:
                summary = {'n_events_analyzed': 0}
            
            return summary
    
    def _structural_break_analysis(self) -> Dict[str, Any]:
        """Test for structural breaks in integration aligned with conflicts.
        
        Returns
        -------
        dict
            Structural break test results
        """
        with timer("structural_break_analysis"):
            info("Testing for conflict-aligned structural breaks")
            
            if self.market_integration_scores is None:
                return {}
            
            # Check if we have integration scores
            if self.market_integration_scores.empty or 'pc1_variance' not in self.market_integration_scores.columns:
                warning("No integration scores available for structural break analysis")
                return {}
            
            # Use Chow test at major conflict dates
            integration_series = self.market_integration_scores['pc1_variance']
            
            # Identify potential break points (major conflicts)
            if self.conflict_data is not None:
                # Get dates of major conflicts
                major_events = self.conflict_data[
                    self.conflict_data['fatalities'] >= self.conflict_threshold * self.validation_config.major_conflict_multiplier
                ]['date'].unique()
                
                break_results = []
                
                for break_date in major_events:
                    # Find index of break date in integration series
                    if break_date in integration_series.index:
                        break_idx = integration_series.index.get_loc(break_date)
                        
                        # Need sufficient data on both sides
                        if break_idx > self.validation_config.min_obs_break and break_idx < len(integration_series) - self.validation_config.min_obs_break:
                            # ECONOMETRIC FIX: Proper Chow test implementation
                            series1 = integration_series.iloc[:break_idx]
                            series2 = integration_series.iloc[break_idx:]
                            
                            # Fit separate regressions with time trend
                            time1 = np.arange(len(series1))
                            time2 = np.arange(len(series2))
                            time_full = np.arange(len(integration_series))
                            
                            # Add constant
                            X1 = np.column_stack([np.ones(len(time1)), time1])
                            X2 = np.column_stack([np.ones(len(time2)), time2])
                            X_full = np.column_stack([np.ones(len(time_full)), time_full])
                            
                            # OLS estimation
                            beta1 = np.linalg.lstsq(X1, series1.values, rcond=None)[0]
                            beta2 = np.linalg.lstsq(X2, series2.values, rcond=None)[0]
                            beta_full = np.linalg.lstsq(X_full, integration_series.values, rcond=None)[0]
                            
                            # Residual sum of squares
                            rss1 = np.sum((series1.values - X1 @ beta1) ** 2)
                            rss2 = np.sum((series2.values - X2 @ beta2) ** 2)
                            rss_full = np.sum((integration_series.values - X_full @ beta_full) ** 2)
                            
                            # Chow F-statistic
                            k = 2  # number of parameters
                            n = len(integration_series)
                            f_stat = ((rss_full - (rss1 + rss2)) / k) / ((rss1 + rss2) / (n - 2*k))
                            f_pvalue = 1 - stats.f.cdf(f_stat, k, n - 2*k)
                            
                            # Also test for variance change
                            levene_stat, levene_pvalue = stats.levene(series1, series2)
                            
                            break_results.append({
                                'break_date': break_date,
                                'mean_before': series1.mean(),
                                'mean_after': series2.mean(),
                                'mean_change': series2.mean() - series1.mean(),
                                't_statistic': t_stat,
                                'p_value': p_value,
                                'chow_f_statistic': f_stat,
                                'chow_p_value': f_pvalue,
                                'variance_test_stat': levene_stat,
                                'variance_p_value': levene_pvalue,
                                'significant_break': f_pvalue < self.validation_config.significance_level
                            })
                
                return {
                    'n_breaks_tested': len(break_results),
                    'n_significant_breaks': sum(r['significant_break'] for r in break_results),
                    'break_details': pd.DataFrame(break_results) if break_results else None
                }
            
            return {'message': 'No conflict data available for break analysis'}
    
    def _spatial_impact_analysis(self) -> Dict[str, Any]:
        """Analyze spatial patterns of conflict impact on integration.
        
        Returns
        -------
        dict
            Spatial analysis results
        """
        with timer("spatial_impact_analysis"):
            info("Analyzing spatial patterns of conflict impact")
            
            if self.conflict_data is None or self.data is None:
                return {}
            
            # Get markets affected by conflicts
            conflict_markets = self.conflict_data['governorate'].unique()
            all_markets = self.data['governorate'].unique()
            
            # Categorize markets
            directly_affected = set(conflict_markets)
            neighboring = set()  # Would need adjacency data
            unaffected = set(all_markets) - directly_affected - neighboring
            
            # Compare price volatility across categories
            results = {
                'directly_affected_markets': list(directly_affected),
                'n_directly_affected': len(directly_affected),
                'n_total_markets': len(all_markets),
                'pct_markets_affected': len(directly_affected) / len(all_markets) * 100
            }
            
            # Calculate price volatility by market category
            volatility_results = []
            
            for market in all_markets:
                market_data = self.data[self.data['governorate'] == market]
                
                if len(market_data) > 0:
                    # Group by commodity and calculate volatility
                    commodity_volatility = market_data.groupby('commodity')['usd_price'].agg([
                        'std', 'mean'
                    ])
                    commodity_volatility['cv'] = commodity_volatility['std'] / commodity_volatility['mean']
                    
                    volatility_results.append({
                        'market': market,
                        'category': 'affected' if market in directly_affected else 'unaffected',
                        'avg_volatility': commodity_volatility['cv'].mean(),
                        'max_volatility': commodity_volatility['cv'].max()
                    })
            
            volatility_df = pd.DataFrame(volatility_results)
            
            # Compare volatility between affected and unaffected
            if len(volatility_df) > 0:
                affected_vol = volatility_df[volatility_df['category'] == 'affected']['avg_volatility']
                unaffected_vol = volatility_df[volatility_df['category'] == 'unaffected']['avg_volatility']
                
                if len(affected_vol) > 0 and len(unaffected_vol) > 0:
                    # ECONOMETRIC FIX: Use Mann-Whitney U test for volatility (often non-normal)
                    # Also control for market size/characteristics if available
                    u_stat, p_value = stats.mannwhitneyu(affected_vol, unaffected_vol, alternative='two-sided')
                    
                    # Convert to approximate z-score for interpretation
                    n1, n2 = len(affected_vol), len(unaffected_vol)
                    mean_u = n1 * n2 / 2
                    std_u = np.sqrt(n1 * n2 * (n1 + n2 + 1) / 12)
                    z_stat = (u_stat - mean_u) / std_u
                    
                    results.update({
                        'avg_volatility_affected': affected_vol.mean(),
                        'avg_volatility_unaffected': unaffected_vol.mean(),
                        'volatility_ratio': affected_vol.mean() / unaffected_vol.mean() if unaffected_vol.mean() > 0 else np.nan,
                        'volatility_test_statistic': z_stat,
                        'volatility_test_pvalue': p_value,
                        'higher_volatility_in_conflict': affected_vol.mean() > unaffected_vol.mean(),
                        'mann_whitney_u': u_stat
                    })
                    
                    log_metric("conflict_volatility_ratio", results['volatility_ratio'])
            
            return results
    
    def _granger_causality_tests(self) -> Dict[str, Any]:
        """Test Granger causality between conflicts and integration.
        
        Returns
        -------
        dict
            Granger causality test results
        """
        with timer("granger_causality_tests"):
            info("Testing Granger causality between conflicts and integration")
            
            # Check if we have integration scores
            if self.market_integration_scores.empty or 'pc1_variance' not in self.market_integration_scores.columns:
                warning("No integration scores available for Granger causality test")
                return {}
            
            # Aggregate conflict intensity over time
            conflict_intensity = self.conflict_data.groupby('date')['fatalities'].sum()
            conflict_intensity = conflict_intensity.reindex(
                self.market_integration_scores.index, 
                fill_value=0
            )
            
            # Align series
            aligned_data = pd.DataFrame({
                'integration': self.market_integration_scores['pc1_variance'],
                'conflict': conflict_intensity
            }).dropna()
            
            if len(aligned_data) < self.validation_config.min_time_periods:
                warning("Insufficient data for Granger causality test")
                return {}
            
            # ECONOMETRIC FIX: Check stationarity before Granger causality
            # First check if series have any variation
            if aligned_data['conflict'].std() == 0:
                warning("Conflict data is constant - cannot perform Granger causality test")
                return {'error': 'Constant conflict data'}
            
            if aligned_data['integration'].std() == 0:
                warning("Integration scores are constant - cannot perform Granger causality test")
                return {'error': 'Constant integration data'}
            
            # Test for unit roots
            adf_integration = adfuller(aligned_data['integration'], regression='ct')
            adf_conflict = adfuller(aligned_data['conflict'], regression='ct')
            
            info(f"ADF test p-values - Integration: {adf_integration[1]:.4f}, Conflict: {adf_conflict[1]:.4f}")
            
            # If non-stationary, difference the series
            if adf_integration[1] > 0.05 or adf_conflict[1] > 0.05:
                warning("Series may be non-stationary, using first differences")
                aligned_data['d_integration'] = aligned_data['integration'].diff()
                aligned_data['d_conflict'] = aligned_data['conflict'].diff()
                aligned_data = aligned_data.dropna()
                
                test_vars = ['d_integration', 'd_conflict']
            else:
                test_vars = ['integration', 'conflict']
            
            # Test both directions
            results = {
                'stationarity_tests': {
                    'integration_adf_pvalue': adf_integration[1],
                    'conflict_adf_pvalue': adf_conflict[1],
                    'used_differences': test_vars[0].startswith('d_')
                }
            }
            
            # Test: Do conflicts Granger-cause integration changes?
            try:
                gc_conflict_to_integration = grangercausalitytests(
                    aligned_data[[test_vars[0], test_vars[1]]], 
                    maxlag=4,
                    verbose=False
                )
                
                # Extract p-values for different lags
                max_lag = min(4, len(aligned_data) // 5)  # Ensure sufficient observations
                conflict_causes_integration = {}
                for lag in range(1, max_lag + 1):
                    if lag in gc_conflict_to_integration:
                        conflict_causes_integration[f'lag_{lag}'] = {
                            'f_statistic': gc_conflict_to_integration[lag][0]['ssr_ftest'][0],
                            'p_value': gc_conflict_to_integration[lag][0]['ssr_ftest'][1]
                        }
                
                results['conflict_causes_integration'] = conflict_causes_integration
                
            except Exception as e:
                warning(f"Error in Granger test (conflict -> integration): {e}")
            
            # Test: Does integration Granger-cause conflicts?
            try:
                gc_integration_to_conflict = grangercausalitytests(
                    aligned_data[[test_vars[1], test_vars[0]]],  # Note: reversed order
                    maxlag=4,
                    verbose=False
                )
                
                integration_causes_conflict = {}
                for lag in range(1, max_lag + 1):
                    if lag in gc_integration_to_conflict:
                        integration_causes_conflict[f'lag_{lag}'] = {
                            'f_statistic': gc_integration_to_conflict[lag][0]['ssr_ftest'][0],
                            'p_value': gc_integration_to_conflict[lag][0]['ssr_ftest'][1]
                        }
                
                results['integration_causes_conflict'] = integration_causes_conflict
                
            except Exception as e:
                warning(f"Error in Granger test (integration -> conflict): {e}")
            
            # Find significant relationships
            if results:
                significant_lags = []
                for direction in results:
                    if direction == 'stationarity_tests':
                        continue  # Skip stationarity test results
                    
                    if isinstance(results[direction], dict):
                        for lag, stats in results[direction].items():
                            if isinstance(stats, dict) and 'p_value' in stats:
                                if stats['p_value'] < self.validation_config.significance_level:
                                    significant_lags.append(f"{direction} at {lag}")
                
                results['significant_relationships'] = significant_lags
                results['bidirectional_causality'] = (
                    len([s for s in significant_lags if 'conflict_causes' in s]) > 0 and
                    len([s for s in significant_lags if 'integration_causes' in s]) > 0
                )
            
            return results
    
    def _add_summary_statistics(self, results: ResultsContainer) -> None:
        """Add summary statistics to results.
        
        Parameters
        ----------
        results : ResultsContainer
            Results to update
        """
        # Basic metadata
        results.metadata['n_observations'] = len(self.data) if self.data is not None else 0
        
        if self.conflict_data is not None:
            results.metadata['n_conflict_events'] = len(self.conflict_data)
            results.metadata['total_fatalities'] = self.conflict_data['fatalities'].sum()
            results.metadata['conflict_date_range'] = (
                self.conflict_data['date'].min(),
                self.conflict_data['date'].max()
            )
        
        # Key findings as coefficients
        tier_specific = results.results.get('tier_specific', {})
        
        if 'event_study' in tier_specific:
            event_study = tier_specific['event_study']
            if 'avg_integration_change' in event_study:
                results.coefficients['avg_conflict_impact'] = event_study['avg_integration_change']
                results.standard_errors['avg_conflict_impact'] = 0.0  # Would need bootstrapping for SE
        
        if 'spatial_impact' in tier_specific:
            spatial = tier_specific['spatial_impact']
            if 'volatility_ratio' in spatial:
                results.coefficients['conflict_volatility_ratio'] = spatial['volatility_ratio']
                results.standard_errors['conflict_volatility_ratio'] = 0.0
        
        # Add warnings for significant findings
        if tier_specific.get('event_study', {}).get('pct_events_reducing_integration', 0) > 70:
            results.metadata['warnings'].append(
                "Strong evidence that conflicts reduce market integration"
            )
        
        if tier_specific.get('granger_causality', {}).get('bidirectional_causality'):
            results.metadata['warnings'].append(
                "Bidirectional causality detected between conflicts and integration"
            )
    
    def predict(self, data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Generate predictions (not applicable for validation model).
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            Input data
            
        Returns
        -------
        pd.Series
            Empty series (validation model doesn't predict)
        """
        warning("Conflict validator is a diagnostic model and does not generate predictions")
        return pd.Series(dtype=float)
    
    def get_conflict_impact_summary(self) -> pd.DataFrame:
        """Get summary of conflict impacts on integration.
        
        Returns
        -------
        pd.DataFrame
            Summary table of impacts
        """
        if not self.is_fitted or 'tier_specific' not in self.results.results:
            raise ValueError("Model must be fitted first")
        
        tier_specific = self.results.results.get('tier_specific', {})
        event_details = tier_specific.get('event_study', {}).get('event_details')
        
        if event_details is not None and not event_details.empty:
            summary = event_details.groupby('governorate').agg({
                'fatalities': 'sum',
                'integration_change': 'mean',
                'significant': 'sum'
            }).rename(columns={
                'fatalities': 'total_fatalities',
                'integration_change': 'avg_integration_impact',
                'significant': 'n_significant_events'
            })
            
            return summary
        
        return pd.DataFrame()  # Empty if no results
    
    def analyze_conflict_spillovers(self, estimation_results: Dict[str, Any], 
                                   conflict_events: pd.DataFrame) -> Dict[str, Any]:
        """Analyze spatial spillovers of conflict on market integration.
        
        Implements spatial econometric analysis following Anselin (2013) and
        LeSage & Pace (2009) for conflict spillover effects.
        
        Parameters
        ----------
        estimation_results : dict
            Results from econometric estimation
        conflict_events : pd.DataFrame
            Conflict event data with location information
            
        Returns
        -------
        dict
            Spatial spillover analysis results
        """
        from yemen_market.utils.logging import info, timer
        import numpy as np
        from scipy import spatial, stats
        from scipy.sparse import csr_matrix
        from scipy.sparse.linalg import eigs
        import warnings
        
        with timer("conflict_spillover_analysis"):
            info("Analyzing spatial spillovers of conflict on market integration")
            
            # Use existing data if conflict_events not provided
            if conflict_events is None:
                conflict_events = self.conflict_data
            
            if conflict_events is None:
                return {'error': 'No conflict data available for spillover analysis'}
            
            # Extract market locations from data
            markets = self.data[['governorate']].drop_duplicates()
            
            # Check if we have coordinates
            if 'latitude' in self.data.columns and 'longitude' in self.data.columns:
                markets = self.data[['governorate', 'latitude', 'longitude']].drop_duplicates()
                has_coords = True
            else:
                warning("Geographic coordinates not available for spatial analysis")
                return self._simplified_spillover_analysis(conflict_events)
            
            # Create spatial weight matrix using inverse distance
            coords = markets[['latitude', 'longitude']].values
            dist_matrix = spatial.distance.cdist(coords, coords)
            
            # Inverse distance weights with cutoff at 100km
            cutoff_km = self.validation_config.spatial_cutoff_km
            W = np.zeros_like(dist_matrix)
            for i in range(len(markets)):
                for j in range(len(markets)):
                    if i != j and dist_matrix[i, j] <= cutoff_km:
                        W[i, j] = 1 / dist_matrix[i, j]
            
            # Row-standardize the weight matrix
            row_sums = W.sum(axis=1)
            W = W / row_sums[:, np.newaxis]
            W[np.isnan(W)] = 0
            
            # Calculate conflict intensity by market
            market_conflict = conflict_events.groupby('governorate').agg({
                'fatalities': 'sum',
                'event_id' if 'event_id' in conflict_events.columns else 'date': 'count'
            })
            market_conflict.columns = ['fatalities', 'n_events']
            
            # Merge with market list
            markets = markets.merge(market_conflict, on='governorate', how='left')
            markets.fillna({'fatalities': 0, 'n_events': 0}, inplace=True)
            
            # Calculate spatial lag of conflict (neighboring conflict intensity)
            conflict_intensity = markets['fatalities'].values
            spatial_lag = W @ conflict_intensity
            
            # Moran's I test for spatial autocorrelation
            n = len(markets)
            y = conflict_intensity - conflict_intensity.mean()
            
            # Calculate Moran's I
            moran_i = (n / np.sum(W)) * (y.T @ W @ y) / (y.T @ y)
            
            # Expected value and variance under null hypothesis
            E_I = -1 / (n - 1)
            b2 = np.sum(y ** 4) / n / (np.sum(y ** 2) / n) ** 2
            
            # Calculate variance (simplified formula)
            W_sparse = csr_matrix(W)
            S0 = W_sparse.sum()
            S1 = 0.5 * ((W_sparse + W_sparse.T) ** 2).sum()
            S2 = (W_sparse.sum(axis=0) ** 2).sum()
            
            var_I = (n * ((n ** 2 - 3 * n + 3) * S1 - n * S2 + 3 * S0 ** 2) - 
                    b2 * ((n ** 2 - n) * S1 - 2 * n * S2 + 6 * S0 ** 2)) / \
                   ((n - 1) * (n - 2) * (n - 3) * S0 ** 2) if n > 3 else 0.1
            
            # Z-score and p-value
            z_score = (moran_i - E_I) / np.sqrt(max(var_I, 0.0001))
            p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            
            # Local Indicators of Spatial Association (LISA)
            lisa_values = np.zeros(n)
            for i in range(n):
                yi = y[i]
                neighbors = W[i, :] > 0
                if neighbors.any():
                    lisa_values[i] = yi * np.sum(W[i, neighbors] * y[neighbors])
            
            # Identify hot spots (HH) and cold spots (LL)
            hot_spots = (y > 0) & (lisa_values > 0)
            cold_spots = (y < 0) & (lisa_values > 0)
            
            # Spatial regression: Impact of own and neighboring conflict on prices
            market_volatility = self._calculate_market_volatility()
            
            if not market_volatility.empty:
                # Merge volatility with markets
                markets = markets.merge(market_volatility[['governorate', 'volatility']], 
                                      on='governorate', how='left')
                markets['volatility'].fillna(markets['volatility'].mean(), inplace=True)
                
                # Design matrix
                X = np.column_stack([
                    np.ones(n),  # intercept
                    conflict_intensity,  # own conflict
                    spatial_lag  # neighboring conflict
                ])
                
                y_vol = markets['volatility'].values
                
                # OLS estimation
                beta = np.linalg.lstsq(X, y_vol, rcond=None)[0]
                residuals = y_vol - X @ beta
                
                # Calculate R-squared
                ssr = np.sum(residuals ** 2)
                sst = np.sum((y_vol - y_vol.mean()) ** 2)
                r_squared = 1 - ssr / sst if sst > 0 else 0
                
                # Standard errors (robust)
                n_params = X.shape[1]
                sigma2 = ssr / max(n - n_params, 1)
                try:
                    var_beta = sigma2 * np.linalg.inv(X.T @ X)
                    se_beta = np.sqrt(np.diag(var_beta))
                    t_stats = beta / se_beta
                    p_values = 2 * (1 - stats.t.cdf(abs(t_stats), max(n - n_params, 1)))
                except:
                    se_beta = np.ones(n_params)
                    p_values = np.ones(n_params)
                
                spillover_results = {
                    'direct_effect': beta[1],
                    'spillover_effect': beta[2],
                    'direct_se': se_beta[1],
                    'spillover_se': se_beta[2],
                    'direct_pvalue': p_values[1],
                    'spillover_pvalue': p_values[2],
                    'spillover_significant': p_values[2] < self.validation_config.significance_level,
                    'model_r_squared': r_squared
                }
            else:
                spillover_results = None
            
            return {
                'spatial_autocorrelation': {
                    'morans_i': moran_i,
                    'expected_i': E_I,
                    'variance': var_I,
                    'z_score': z_score,
                    'p_value': p_value,
                    'significant': p_value < self.validation_config.significance_level
                },
                'hot_spots': markets.loc[hot_spots, 'governorate'].tolist(),
                'cold_spots': markets.loc[cold_spots, 'governorate'].tolist(),
                'n_hot_spots': hot_spots.sum(),
                'n_cold_spots': cold_spots.sum(),
                'spillover_regression': spillover_results,
                'spatial_weight_matrix_summary': {
                    'n_neighbors_avg': (W > 0).sum(axis=1).mean(),
                    'max_weight': W.max(),
                    'connectivity': (W > 0).sum() / (n * (n - 1))  # proportion of non-zero weights
                }
            }
    
    def _simplified_spillover_analysis(self, conflict_events: pd.DataFrame) -> Dict[str, Any]:
        """
        Simplified spillover analysis when geographic coordinates are not available.
        Uses temporal and co-occurrence patterns to infer spillovers.
        
        Parameters
        ----------
        conflict_events : pd.DataFrame
            Conflict event data
            
        Returns
        -------
        dict
            Simplified spillover analysis results
        """
        from yemen_market.utils.logging import info
        
        info("Running simplified spillover analysis without geographic coordinates")
        
        # Create conflict co-occurrence matrix
        markets = conflict_events['governorate'].unique()
        n_markets = len(markets)
        
        # Count co-occurrences within time windows (same week)
        conflict_events['week'] = pd.to_datetime(conflict_events['date']).dt.to_period('W')
        
        co_occurrence = np.zeros((n_markets, n_markets))
        
        for week in conflict_events['week'].unique():
            week_events = conflict_events[conflict_events['week'] == week]
            affected_markets = week_events['governorate'].unique()
            
            for i, market1 in enumerate(markets):
                for j, market2 in enumerate(markets):
                    if i != j and market1 in affected_markets and market2 in affected_markets:
                        co_occurrence[i, j] += 1
        
        # Normalize to get co-occurrence probabilities
        total_weeks = conflict_events['week'].nunique()
        co_occurrence_prob = co_occurrence / total_weeks
        
        # Calculate network metrics
        degree_centrality = co_occurrence_prob.sum(axis=1)
        most_connected = markets[degree_centrality.argmax()]
        
        # Identify conflict clusters using simple thresholding
        threshold = self.validation_config.co_occurrence_threshold  # Markets that co-experience conflict
        clusters = []
        visited = set()
        
        for i, market in enumerate(markets):
            if market not in visited:
                cluster = [market]
                visited.add(market)
                
                # Find connected markets
                for j, other_market in enumerate(markets):
                    if i != j and co_occurrence_prob[i, j] > threshold:
                        cluster.append(other_market)
                        visited.add(other_market)
                
                if len(cluster) > 1:
                    clusters.append(cluster)
        
        return {
            'analysis_type': 'simplified_no_coordinates',
            'n_markets': n_markets,
            'most_connected_market': most_connected,
            'avg_co_occurrence': co_occurrence_prob.mean(),
            'max_co_occurrence': co_occurrence_prob.max(),
            'n_conflict_clusters': len(clusters),
            'largest_cluster_size': max(len(c) for c in clusters) if clusters else 0,
            'clusters': clusters,
            'network_density': (co_occurrence_prob > 0).sum() / (n_markets * (n_markets - 1))
        }
    
    def _calculate_market_volatility(self) -> pd.DataFrame:
        """
        Calculate price volatility measures for each market.
        
        Returns
        -------
        pd.DataFrame
            Market-level volatility measures
        """
        if self.data is None:
            return pd.DataFrame()
        
        # Calculate volatility by market
        volatility = self.data.groupby('governorate').agg({
            'usd_price': ['std', 'mean']
        })
        
        volatility.columns = ['price_std', 'price_mean']
        volatility['cv'] = volatility['price_std'] / volatility['price_mean']
        volatility['volatility'] = volatility['cv']  # Use CV as volatility measure
        volatility = volatility.reset_index()
        
        return volatility