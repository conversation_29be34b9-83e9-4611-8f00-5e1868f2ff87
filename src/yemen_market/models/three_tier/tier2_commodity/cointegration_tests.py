"""Cointegration testing suite for commodity-specific analysis.

This module provides comprehensive cointegration tests for Tier 2 analysis,
including <PERSON><PERSON> tests, Engle-Granger tests, and panel cointegration tests.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import warnings
from statsmodels.tsa.stattools import adfuller, kpss, coint
from statsmodels.tsa.vector_ar.vecm import coint_johansen, select_coint_rank
import statsmodels.api as sm
from scipy import stats

from yemen_market.utils.logging import (
    info, warning, error, timer, log_metric, log_data_shape, bind
)


# Set module context
bind(module=__name__)


@dataclass
class CointegrationTestConfig:
    """Configuration for cointegration tests.
    
    Attributes:
        test_types: List of test types to perform
        significance_level: Significance level for tests
        max_lags: Maximum lags to consider in tests
        trend: Trend specification ('nc', 'c', 'ct', 'ctt')
        autolag: Automatic lag selection criterion
    """
    test_types: List[str] = None
    significance_level: float = 0.05
    max_lags: Optional[int] = None
    trend: str = 'c'
    autolag: str = 'AIC'
    
    def __post_init__(self):
        if self.test_types is None:
            self.test_types = ['johansen', 'engle_granger', 'phillips_ouliaris']
        
        valid_trends = ['nc', 'c', 'ct', 'ctt']
        if self.trend not in valid_trends:
            raise ValueError(f"trend must be one of {valid_trends}")
        
        if self.significance_level <= 0 or self.significance_level >= 1:
            raise ValueError(f"significance_level must be between 0 and 1")


class CointegrationTestSuite:
    """Comprehensive cointegration testing for commodity market pairs.
    
    This class provides multiple cointegration tests to verify long-run
    relationships between market prices for a specific commodity.
    """
    
    def __init__(self, config: Optional[CointegrationTestConfig] = None):
        """Initialize cointegration test suite.
        
        Args:
            config: Test configuration. If None, uses defaults
        """
        self.config = config or CointegrationTestConfig()
        self.test_results = {}
        
        info(f"Initialized CointegrationTestSuite with tests: {self.config.test_types}")
    
    def run_all_tests(self, price_data: pd.DataFrame,
                     commodity: str) -> Dict[str, Any]:
        """Run all configured cointegration tests.
        
        Args:
            price_data: Wide-format price data (dates × markets)
            commodity: Name of the commodity being tested
            
        Returns:
            Dictionary containing all test results
        """
        with timer(f"cointegration_tests_{commodity}"):
            info(f"Running cointegration tests for {commodity}")
            
            # Take logs
            log_prices = np.log(price_data.dropna())
            log_data_shape(f"{commodity}_log_prices", log_prices)
            
            # Initialize results
            results = {
                'commodity': commodity,
                'n_markets': len(log_prices.columns),
                'n_observations': len(log_prices),
                'date_range': [str(log_prices.index.min()), str(log_prices.index.max())],
                'tests': {}
            }
            
            # Test for unit roots first
            info("Testing for unit roots in individual series")
            unit_root_results = self._test_unit_roots(log_prices)
            results['unit_root_tests'] = unit_root_results
            
            # Count non-stationary series
            n_nonstationary = sum(
                not res['is_stationary'] 
                for res in unit_root_results.values()
            )
            results['n_nonstationary'] = n_nonstationary
            
            if n_nonstationary < 2:
                warning(f"Only {n_nonstationary} non-stationary series found. "
                       "Cointegration requires at least 2 I(1) series.")
            
            # Run configured tests
            if 'johansen' in self.config.test_types:
                results['tests']['johansen'] = self._johansen_test(log_prices)
            
            if 'engle_granger' in self.config.test_types:
                results['tests']['engle_granger'] = self._engle_granger_test(log_prices)
            
            if 'phillips_ouliaris' in self.config.test_types:
                results['tests']['phillips_ouliaris'] = self._phillips_ouliaris_test(log_prices)
            
            # Summary conclusion
            results['summary'] = self._summarize_results(results['tests'])
            
            # Log key metrics
            if 'johansen' in results['tests']:
                log_metric(f"{commodity}_coint_rank", 
                          results['tests']['johansen']['selected_rank'])
            
            self.test_results[commodity] = results
            
            return results
    
    def _test_unit_roots(self, log_prices: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """Test each series for unit roots.
        
        Args:
            log_prices: Log price data
            
        Returns:
            Dictionary of unit root test results by market
        """
        results = {}
        
        for market in log_prices.columns:
            series = log_prices[market].dropna()
            
            if len(series) < 20:
                warning(f"Insufficient data for {market}, skipping unit root tests")
                continue
            
            # ADF test
            try:
                adf_result = adfuller(series, regression='c', autolag=self.config.autolag)
                adf_stat, adf_pval = adf_result[0], adf_result[1]
            except Exception as e:
                warning(f"ADF test failed for {market}: {str(e)}")
                adf_stat, adf_pval = np.nan, np.nan
            
            # KPSS test
            try:
                kpss_result = kpss(series, regression='c', nlags='auto')
                kpss_stat, kpss_pval = kpss_result[0], kpss_result[1]
            except Exception as e:
                warning(f"KPSS test failed for {market}: {str(e)}")
                kpss_stat, kpss_pval = np.nan, np.nan
            
            # Determine stationarity
            # Series is stationary if ADF rejects null AND KPSS fails to reject null
            is_stationary = (adf_pval < self.config.significance_level and 
                           kpss_pval > self.config.significance_level)
            
            results[market] = {
                'adf_statistic': adf_stat,
                'adf_pvalue': adf_pval,
                'adf_rejects_unit_root': adf_pval < self.config.significance_level,
                'kpss_statistic': kpss_stat,
                'kpss_pvalue': kpss_pval,
                'kpss_rejects_stationarity': kpss_pval < self.config.significance_level,
                'is_stationary': is_stationary,
                'conclusion': 'I(0)' if is_stationary else 'I(1)'
            }
        
        return results
    
    def _johansen_test(self, log_prices: pd.DataFrame) -> Dict[str, Any]:
        """Perform Johansen cointegration test.
        
        Args:
            log_prices: Log price data
            
        Returns:
            Dictionary with Johansen test results
        """
        info("Performing Johansen cointegration test")
        
        try:
            # Determine lag order if not specified
            if self.config.max_lags is None:
                max_lags = min(int(np.sqrt(len(log_prices))), 12)
            else:
                max_lags = self.config.max_lags
            
            # Select cointegration rank
            det_order_map = {'nc': -1, 'c': 0, 'ct': 1, 'ctt': 2}
            det_order = det_order_map.get(self.config.trend, 0)
            
            rank_test = select_coint_rank(
                log_prices,
                det_order=det_order,
                k_ar_diff=max_lags,
                method='trace',
                signif=self.config.significance_level
            )
            
            selected_rank = rank_test.rank
            
            # Get full test results
            johansen_result = coint_johansen(
                log_prices.values,
                det_order=det_order,
                k_ar_diff=max_lags
            )
            
            # Extract key results
            results = {
                'selected_rank': selected_rank,
                'max_rank': len(log_prices.columns) - 1,
                'trace_statistics': johansen_result.lr1.tolist(),
                'trace_critical_values': johansen_result.cvt[:, 1].tolist(),  # 5% level
                'max_eigenvalue_statistics': johansen_result.lr2.tolist(),
                'max_eigenvalue_critical_values': johansen_result.cvm[:, 1].tolist(),
                'eigenvalues': johansen_result.eig.tolist(),
                'eigenvectors': johansen_result.evec.tolist(),
                'det_order': det_order,
                'k_ar_diff': max_lags,
                'test_conclusion': f"Found {selected_rank} cointegrating relationship(s)"
            }
            
            info(f"Johansen test: {results['test_conclusion']}")
            
            return results
            
        except Exception as e:
            error(f"Johansen test failed: {str(e)}")
            return {
                'error': str(e),
                'selected_rank': 0,
                'test_conclusion': "Test failed"
            }
    
    def _engle_granger_test(self, log_prices: pd.DataFrame) -> Dict[str, Any]:
        """Perform Engle-Granger two-step cointegration test.
        
        Args:
            log_prices: Log price data
            
        Returns:
            Dictionary with Engle-Granger test results
        """
        info("Performing Engle-Granger cointegration tests")
        
        markets = list(log_prices.columns)
        n_markets = len(markets)
        
        if n_markets < 2:
            return {'error': 'Need at least 2 markets for pairwise tests'}
        
        results = {
            'pairwise_tests': {},
            'significant_pairs': [],
            'n_pairs_tested': 0,
            'n_cointegrated': 0
        }
        
        # Test all market pairs
        for i in range(n_markets):
            for j in range(i + 1, n_markets):
                market1, market2 = markets[i], markets[j]
                pair_name = f"{market1}-{market2}"
                
                try:
                    # Get series
                    y1 = log_prices[market1].dropna()
                    y2 = log_prices[market2].dropna()
                    
                    # Align series
                    aligned = pd.DataFrame({'y1': y1, 'y2': y2}).dropna()
                    
                    if len(aligned) < 50:
                        continue
                    
                    # Perform cointegration test
                    coint_t, p_value, crit_values = coint(
                        aligned['y1'], 
                        aligned['y2'],
                        trend='c',
                        autolag=self.config.autolag
                    )
                    
                    is_cointegrated = p_value < self.config.significance_level
                    
                    results['pairwise_tests'][pair_name] = {
                        'test_statistic': float(coint_t),
                        'p_value': float(p_value),
                        'critical_values': {
                            '1%': float(crit_values[0]),
                            '5%': float(crit_values[1]),
                            '10%': float(crit_values[2])
                        },
                        'is_cointegrated': is_cointegrated,
                        'n_obs': len(aligned)
                    }
                    
                    if is_cointegrated:
                        results['significant_pairs'].append(pair_name)
                        results['n_cointegrated'] += 1
                    
                    results['n_pairs_tested'] += 1
                    
                except Exception as e:
                    warning(f"Engle-Granger test failed for {pair_name}: {str(e)}")
        
        results['proportion_cointegrated'] = (
            results['n_cointegrated'] / results['n_pairs_tested'] 
            if results['n_pairs_tested'] > 0 else 0
        )
        
        info(f"Engle-Granger: {results['n_cointegrated']}/{results['n_pairs_tested']} "
             f"pairs cointegrated")
        
        return results
    
    def _phillips_ouliaris_test(self, log_prices: pd.DataFrame) -> Dict[str, Any]:
        """Perform Phillips-Ouliaris cointegration test.
        
        This is a residual-based test similar to Engle-Granger but with
        different critical values.
        
        Args:
            log_prices: Log price data
            
        Returns:
            Dictionary with Phillips-Ouliaris test results
        """
        info("Performing Phillips-Ouliaris cointegration test")
        
        # For multivariate case, test if residuals from regressing first variable
        # on others are stationary
        try:
            if len(log_prices.columns) < 2:
                return {'error': 'Need at least 2 series for cointegration test'}
            
            # Use first market as dependent variable
            y = log_prices.iloc[:, 0]
            X = log_prices.iloc[:, 1:]
            
            # Add constant
            X = sm.add_constant(X)
            
            # Run regression
            model = sm.OLS(y, X, missing='drop')
            results_ols = model.fit()
            
            # Test residuals for stationarity
            residuals = results_ols.resid
            
            # ADF test on residuals (Phillips-Ouliaris uses different critical values)
            adf_result = adfuller(residuals, regression='nc', autolag=self.config.autolag)
            
            # Phillips-Ouliaris critical values (approximate)
            po_crit_values = {
                '1%': -3.96,
                '5%': -3.37,
                '10%': -3.03
            }
            
            test_stat = adf_result[0]
            is_cointegrated = test_stat < po_crit_values['5%']
            
            results = {
                'test_statistic': float(test_stat),
                'critical_values': po_crit_values,
                'is_cointegrated': is_cointegrated,
                'n_series': len(log_prices.columns),
                'n_obs': len(residuals),
                'r_squared': results_ols.rsquared,
                'test_conclusion': 'Cointegrated' if is_cointegrated else 'Not cointegrated'
            }
            
            info(f"Phillips-Ouliaris test: {results['test_conclusion']}")
            
            return results
            
        except Exception as e:
            error(f"Phillips-Ouliaris test failed: {str(e)}")
            return {
                'error': str(e),
                'test_conclusion': 'Test failed'
            }
    
    def _summarize_results(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize cointegration test results.
        
        Args:
            test_results: Dictionary of individual test results
            
        Returns:
            Summary dictionary
        """
        summary = {
            'tests_performed': list(test_results.keys()),
            'consensus': None,
            'johansen_rank': None,
            'proportion_pairs_cointegrated': None,
            'recommendation': None
        }
        
        # Extract key findings
        if 'johansen' in test_results and 'selected_rank' in test_results['johansen']:
            summary['johansen_rank'] = test_results['johansen']['selected_rank']
        
        if 'engle_granger' in test_results:
            summary['proportion_pairs_cointegrated'] = test_results['engle_granger'].get(
                'proportion_cointegrated', 0
            )
        
        # Determine consensus
        evidence_for_coint = 0
        evidence_against_coint = 0
        
        if summary['johansen_rank'] and summary['johansen_rank'] > 0:
            evidence_for_coint += 1
        else:
            evidence_against_coint += 1
        
        if summary['proportion_pairs_cointegrated'] and summary['proportion_pairs_cointegrated'] > 0.5:
            evidence_for_coint += 1
        elif summary['proportion_pairs_cointegrated'] is not None:
            evidence_against_coint += 1
        
        if 'phillips_ouliaris' in test_results:
            if test_results['phillips_ouliaris'].get('is_cointegrated', False):
                evidence_for_coint += 1
            else:
                evidence_against_coint += 1
        
        # Set consensus
        if evidence_for_coint > evidence_against_coint:
            summary['consensus'] = 'Evidence for cointegration'
            summary['recommendation'] = 'Use VECM or cointegration-based models'
        elif evidence_against_coint > evidence_for_coint:
            summary['consensus'] = 'Little evidence for cointegration'
            summary['recommendation'] = 'Use VAR in differences or univariate models'
        else:
            summary['consensus'] = 'Mixed evidence'
            summary['recommendation'] = 'Consider both VECM and VAR specifications'
        
        return summary
    
    def test_parameter_stability(self, price_data: pd.DataFrame,
                               commodity: str,
                               window_size: int = 52) -> Dict[str, Any]:
        """Test stability of cointegrating relationships over time.
        
        Args:
            price_data: Wide-format price data
            commodity: Name of the commodity
            window_size: Rolling window size (in weeks)
            
        Returns:
            Dictionary with stability test results
        """
        with timer(f"parameter_stability_{commodity}"):
            info(f"Testing parameter stability for {commodity}")
            
            log_prices = np.log(price_data.dropna())
            n_obs = len(log_prices)
            
            if n_obs < window_size * 2:
                warning("Insufficient data for stability testing")
                return {'error': 'Insufficient data'}
            
            # Rolling cointegration tests
            rolling_ranks = []
            rolling_dates = []
            
            for i in range(window_size, n_obs):
                window_data = log_prices.iloc[i-window_size:i]
                
                try:
                    # Quick Johansen test
                    rank_test = select_coint_rank(
                        window_data,
                        det_order=0,
                        k_ar_diff=1,
                        method='trace',
                        signif=0.05
                    )
                    rolling_ranks.append(rank_test.rank)
                    rolling_dates.append(window_data.index[-1])
                except:
                    rolling_ranks.append(np.nan)
                    rolling_dates.append(window_data.index[-1])
            
            # Calculate stability metrics
            ranks_series = pd.Series(rolling_ranks, index=rolling_dates)
            rank_changes = ranks_series.diff().abs().sum()
            rank_std = ranks_series.std()
            
            results = {
                'rolling_tests': {  # Wrap in 'rolling_tests' key as expected by test
                    'window_size': window_size,
                    'n_windows': len(rolling_ranks),
                    'rank_changes': int(rank_changes),
                    'rank_std': float(rank_std),
                    'min_rank': int(np.nanmin(rolling_ranks)),
                    'max_rank': int(np.nanmax(rolling_ranks)),
                    'mode_rank': int(stats.mode(rolling_ranks, nan_policy='omit')[0]),
                    'is_stable': bool(rank_std < 0.5),  # Arbitrary threshold
                    'rolling_ranks': rolling_ranks,
                    'rolling_dates': [str(d) for d in rolling_dates]
                },
                # Also keep these at top level for backward compatibility
                'window_size': window_size,
                'n_windows': len(rolling_ranks),
                'rank_changes': int(rank_changes),
                'rank_std': float(rank_std),
                'min_rank': int(np.nanmin(rolling_ranks)),
                'max_rank': int(np.nanmax(rolling_ranks)),
                'mode_rank': int(stats.mode(rolling_ranks, nan_policy='omit')[0]),
                'is_stable': bool(rank_std < 0.5),
                'rolling_ranks': rolling_ranks,
                'rolling_dates': [str(d) for d in rolling_dates]
            }
            
            info(f"Stability test: rank_std={rank_std:.3f}, "
                 f"is_stable={results['is_stable']}")
            
            return results