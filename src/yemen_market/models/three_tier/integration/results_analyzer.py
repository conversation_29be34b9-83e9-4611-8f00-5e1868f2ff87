"""Results analysis utilities for three-tier econometric models.

This module provides functions for extracting, analyzing, and interpreting
results from the three-tier analysis, including:
- Coefficient extraction from model results
- Market integration analysis
- Conflict impact assessment
- Policy insights generation
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json
from scipy import stats

from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, log_metric, bind
)
from yemen_market.models.three_tier.common import ResultsContainer

# Set module context
bind(module=__name__)


class ResultsAnalyzer:
    """Analyzer for three-tier econometric model results."""
    
    def __init__(self, results_dir: Path):
        """Initialize the results analyzer.
        
        Parameters
        ----------
        results_dir : Path
            Directory containing three-tier analysis results
        """
        self.results_dir = Path(results_dir)
        self.tier1_results = None
        self.tier2_results = {}
        self.tier3_results = None
        
        bind(analyzer="ResultsAnalyzer")
    
    def load_results(self) -> None:
        """Load all results from the analysis directory."""
        with timer("load_results"):
            info(f"Loading results from {self.results_dir}")
            
            # Load Tier 1 results
            tier1_path = self.results_dir / "tier1" / "tier1_results.json"
            if tier1_path.exists():
                try:
                    with open(tier1_path, 'r') as f:
                        self.tier1_results = json.load(f)
                    info("Loaded Tier 1 results")
                except Exception as e:
                    warning(f"Could not load Tier 1 results: {e}")
            
            # Load Tier 2 results
            tier2_dir = self.results_dir / "tier2"
            if tier2_dir.exists():
                for result_file in tier2_dir.glob("*_results.json"):
                    commodity = result_file.stem.replace("_results", "")
                    try:
                        with open(result_file, 'r') as f:
                            self.tier2_results[commodity] = json.load(f)
                    except Exception as e:
                        warning(f"Could not load {commodity} results: {e}")
                info(f"Loaded {len(self.tier2_results)} Tier 2 commodity results")
            
            # Load Tier 3 results
            tier3_path = self.results_dir / "tier3" / "tier3_results.json"
            if tier3_path.exists():
                try:
                    with open(tier3_path, 'r') as f:
                        self.tier3_results = json.load(f)
                    info("Loaded Tier 3 results")
                except Exception as e:
                    warning(f"Could not load Tier 3 results: {e}")
    
    def extract_tier1_coefficients(self, model_object: Optional[Any] = None) -> Dict[str, Any]:
        """Extract coefficients from Tier 1 pooled panel results.
        
        Parameters
        ----------
        model_object : Any, optional
            Fitted model object if available
            
        Returns
        -------
        dict
            Dictionary containing:
            - coefficients: Parameter estimates
            - standard_errors: Standard errors
            - statistics: Model statistics (R-squared, observations, etc.)
            - p_values: P-values for coefficients
            - significance: Significance levels
        """
        with timer("extract_tier1_coefficients"):
            info("Extracting Tier 1 coefficients...")
            
            results = {
                'coefficients': {},
                'standard_errors': {},
                'statistics': {},
                'p_values': {},
                'significance': {}
            }
            
            # If we have a model object, extract from it
            if model_object is not None:
                if hasattr(model_object, 'results') and model_object.results:
                    container = model_object.results
                    if isinstance(container, ResultsContainer):
                        results['coefficients'] = container.parameters
                        results['standard_errors'] = container.standard_errors
                        results['statistics'] = container.statistics
                        
                        # Calculate p-values and significance
                        for var, coef in results['coefficients'].items():
                            se = results['standard_errors'].get(var)
                            if se and se != 0:
                                t_stat = coef / se
                                n_obs = results['statistics'].get('n_observations', 100)
                                n_params = len(results['coefficients'])
                                df = n_obs - n_params
                                p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df))
                                results['p_values'][var] = p_value
                                
                                # Significance stars
                                if p_value < 0.01:
                                    results['significance'][var] = "***"
                                elif p_value < 0.05:
                                    results['significance'][var] = "**"
                                elif p_value < 0.10:
                                    results['significance'][var] = "*"
                                else:
                                    results['significance'][var] = ""
            
            # Otherwise try to extract from saved results
            elif self.tier1_results and isinstance(self.tier1_results, dict):
                if 'coefficients' in self.tier1_results:
                    results = self.tier1_results
            
            # Log extracted coefficients
            if results['coefficients']:
                info(f"Extracted {len(results['coefficients'])} coefficients")
                for var, coef in results['coefficients'].items():
                    sig = results['significance'].get(var, '')
                    info(f"  {var}: {coef:.4f} {sig}")
            else:
                warning("No coefficients extracted from Tier 1")
            
            return results
    
    def analyze_conflict_impact(self, coefficients: Dict[str, Any]) -> Dict[str, float]:
        """Analyze the impact of conflict on prices from regression results.
        
        Parameters
        ----------
        coefficients : dict
            Dictionary with 'coefficients' key containing parameter estimates
            
        Returns
        -------
        dict
            Analysis results including:
            - impact_per_event: Percentage price change per conflict event
            - monthly_impact: Average monthly conflict impact
            - high_conflict_premium: Price premium in high conflict areas
            - control_zone_effect: Price differential by control zone
        """
        with timer("analyze_conflict_impact"):
            info("Analyzing conflict impact on prices...")
            
            analysis = {}
            coef = coefficients.get('coefficients', {})
            
            # Conflict event impact
            if 'events_total' in coef:
                # Since dependent variable is log_price, coefficient is semi-elasticity
                impact_per_event = coef['events_total'] * 100  # Percentage impact
                analysis['impact_per_event'] = impact_per_event
                
                # Monthly impact (average ~24 events per month from data)
                analysis['monthly_impact'] = impact_per_event * 24
                
                info(f"Each conflict event changes prices by: {impact_per_event:.3f}%")
                info(f"Average monthly conflict impact: {analysis['monthly_impact']:.1f}%")
            
            # High conflict regime impact
            if 'high_conflict' in coef:
                # For dummy variable with log dependent var: (exp(β) - 1) * 100
                high_conflict_premium = (np.exp(coef['high_conflict']) - 1) * 100
                analysis['high_conflict_premium'] = high_conflict_premium
                info(f"High conflict price premium: {high_conflict_premium:.1f}%")
            
            # Control zone impact
            if 'control_zone_DFA' in coef:
                dfa_zone_effect = (np.exp(coef['control_zone_DFA']) - 1) * 100
                analysis['control_zone_effect'] = dfa_zone_effect
                info(f"DFA-controlled areas price differential: {dfa_zone_effect:.1f}%")
            
            # Time trend
            if 'time_trend' in coef:
                monthly_trend = coef['time_trend'] * 100
                analysis['monthly_trend'] = monthly_trend
                analysis['annual_trend'] = monthly_trend * 12
                info(f"Monthly price trend (conflict-adjusted): {monthly_trend:.2f}%")
                info(f"Annual price trend: {analysis['annual_trend']:.1f}%")
            
            return analysis
    
    def extract_tier2_results(self) -> Dict[str, Dict[str, Any]]:
        """Extract and summarize Tier 2 commodity-specific results.
        
        Returns
        -------
        dict
            Dictionary keyed by commodity with results for each
        """
        with timer("extract_tier2_results"):
            info("Extracting Tier 2 commodity-specific results...")
            
            summary = {}
            
            # Categorize commodities by analysis status
            completed = []
            errors = []
            
            for commodity, results in self.tier2_results.items():
                if 'error' in results:
                    errors.append((commodity, results['error']))
                else:
                    completed.append(commodity)
                    
                    # Extract key metrics if available
                    commodity_summary = {
                        'status': 'completed',
                        'has_threshold': 'threshold_value' in results,
                        'threshold_value': results.get('threshold_value'),
                        'integration_level': results.get('integration_level'),
                        'n_observations': results.get('n_observations'),
                        'r_squared': results.get('r_squared')
                    }
                    summary[commodity] = commodity_summary
            
            # Add error commodities to summary
            for commodity, error_msg in errors:
                summary[commodity] = {
                    'status': 'error',
                    'error_message': error_msg
                }
            
            # Log summary statistics
            info(f"Tier 2 results summary:")
            info(f"  Total commodities: {len(self.tier2_results)}")
            info(f"  Successfully analyzed: {len(completed)}")
            info(f"  Errors: {len(errors)}")
            
            if errors:
                info("Commodities with errors:")
                for commodity, msg in errors[:5]:  # Show first 5
                    info(f"    {commodity}: {msg[:50]}...")
            
            return summary
    
    def analyze_market_integration(self, panel_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market integration patterns from panel data.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Panel data with price information
            
        Returns
        -------
        dict
            Integration analysis including:
            - correlation_matrix: Market price correlations
            - high_integration_pairs: Market pairs with correlation > 0.8
            - zone_correlations: Cross-zone price correlations
            - integration_summary: Summary statistics
        """
        with timer("analyze_market_integration"):
            info("Analyzing market integration patterns...")
            
            analysis = {}
            
            # Calculate price correlations between markets
            if 'log_price' in panel_data.columns:
                price_col = 'log_price'
            elif 'price' in panel_data.columns:
                price_col = 'price'
            else:
                warning("No price column found for integration analysis")
                return analysis
            
            # Pivot to get prices by market
            price_wide = panel_data.pivot_table(
                index='date',
                columns='market',
                values=price_col,
                aggfunc='mean'
            )
            
            # Calculate correlation matrix
            corr_matrix = price_wide.corr()
            analysis['correlation_matrix'] = corr_matrix.to_dict()
            
            # Find highly integrated market pairs
            high_integration = []
            for i in range(len(corr_matrix)):
                for j in range(i+1, len(corr_matrix)):
                    corr = corr_matrix.iloc[i, j]
                    if corr > 0.8 and not np.isnan(corr):
                        high_integration.append({
                            'market1': corr_matrix.index[i],
                            'market2': corr_matrix.index[j],
                            'correlation': float(corr)
                        })
            
            analysis['high_integration_pairs'] = sorted(
                high_integration, 
                key=lambda x: x['correlation'], 
                reverse=True
            )
            
            info(f"Found {len(high_integration)} highly integrated market pairs (corr > 0.8)")
            
            # Analyze by conflict zones if available
            if 'zone_DFA' in panel_data.columns:
                zone_prices = panel_data.groupby(['date', 'zone_DFA'])[price_col].mean().reset_index()
                zone_wide = zone_prices.pivot(index='date', columns='zone_DFA', values=price_col)
                
                if len(zone_wide.columns) > 1:
                    zone_corr = zone_wide.corr()
                    analysis['zone_correlations'] = zone_corr.to_dict()
                    
                    # Log cross-zone correlations
                    info("Cross-zone price correlations:")
                    for i in range(len(zone_corr)):
                        for j in range(i+1, len(zone_corr)):
                            corr_val = zone_corr.iloc[i, j]
                            if not np.isnan(corr_val):
                                info(f"  {zone_corr.index[i]} <-> {zone_corr.index[j]}: {corr_val:.3f}")
            
            # Summary statistics
            analysis['integration_summary'] = {
                'n_markets': len(corr_matrix),
                'mean_correlation': float(corr_matrix.values[np.triu_indices_from(corr_matrix.values, k=1)].mean()),
                'n_high_integration_pairs': len(high_integration),
                'most_integrated_markets': analysis['high_integration_pairs'][:10] if analysis['high_integration_pairs'] else []
            }
            
            log_metric("market_integration_mean_corr", analysis['integration_summary']['mean_correlation'])
            
            return analysis
    
    def generate_policy_insights(self, panel_data: pd.DataFrame) -> Dict[str, Any]:
        """Generate policy-relevant insights from the analysis.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Panel data with price and conflict information
            
        Returns
        -------
        dict
            Policy insights including:
            - price_volatility_by_conflict
            - market_accessibility
            - essential_commodities_trends
            - geographic_disparities
            - key_recommendations
        """
        with timer("generate_policy_insights"):
            info("Generating policy insights...")
            
            insights = {}
            
            # 1. Price volatility by conflict intensity
            if 'conflict_regime' in panel_data.columns and 'price_change_pct' in panel_data.columns:
                volatility = panel_data.groupby(['commodity', 'conflict_regime'])['price_change_pct'].agg(['std', 'count'])
                
                insights['price_volatility_by_conflict'] = {}
                for commodity in ['Wheat', 'Rice (Imported)', 'Fuel (Diesel)']:
                    if commodity in volatility.index.get_level_values(0):
                        comm_vol = volatility.loc[commodity]
                        insights['price_volatility_by_conflict'][commodity] = {
                            regime: {'volatility_pct': float(row['std'] * 100), 'n_obs': int(row['count'])}
                            for regime, row in comm_vol.iterrows()
                            if row['count'] > 10
                        }
            
            # 2. Market accessibility
            market_coverage = panel_data.groupby('market')['price'].apply(lambda x: x.notna().mean() * 100)
            
            insights['market_accessibility'] = {
                'most_accessible': market_coverage.nlargest(5).to_dict(),
                'least_accessible': market_coverage.nsmallest(5).to_dict(),
                'mean_accessibility': float(market_coverage.mean())
            }
            
            # 3. Essential commodities analysis
            essential = ['Wheat', 'Rice (Imported)', 'Oil (Vegetable)', 'Sugar', 'Fuel (Diesel)']
            insights['essential_commodities_trends'] = {}
            
            for commodity in essential:
                if commodity in panel_data['commodity'].unique():
                    comm_data = panel_data[panel_data['commodity'] == commodity]
                    
                    # Year-over-year price change
                    if 'year' in comm_data.columns:
                        yearly_prices = comm_data.groupby('year')['price'].mean()
                        if len(yearly_prices) > 1:
                            total_change = (yearly_prices.iloc[-1] - yearly_prices.iloc[0]) / yearly_prices.iloc[0] * 100
                            
                            comm_insights = {
                                'total_price_change_pct': float(total_change),
                                'start_year': int(yearly_prices.index[0]),
                                'end_year': int(yearly_prices.index[-1])
                            }
                            
                            # Conflict impact
                            if 'high_conflict' in comm_data.columns:
                                high_conflict_price = comm_data[comm_data['high_conflict'] == 1]['price'].mean()
                                low_conflict_price = comm_data[comm_data['high_conflict'] == 0]['price'].mean()
                                if high_conflict_price > 0 and low_conflict_price > 0:
                                    conflict_premium = (high_conflict_price / low_conflict_price - 1) * 100
                                    comm_insights['conflict_premium_pct'] = float(conflict_premium)
                            
                            insights['essential_commodities_trends'][commodity] = comm_insights
            
            # 4. Geographic disparities
            insights['geographic_disparities'] = {}
            recent_data = panel_data[panel_data['date'] >= panel_data['date'].max() - pd.Timedelta(days=365)]
            
            for commodity in ['Wheat', 'Fuel (Diesel)']:
                if commodity in recent_data['commodity'].unique():
                    comm_recent = recent_data[recent_data['commodity'] == commodity]
                    
                    # Coefficient of variation across markets
                    monthly_cv = comm_recent.groupby('date')['price'].agg(
                        lambda x: x.std() / x.mean() * 100 if x.mean() > 0 else np.nan
                    )
                    
                    avg_cv = monthly_cv.mean()
                    if not np.isnan(avg_cv):
                        insights['geographic_disparities'][commodity] = {
                            'avg_price_dispersion_cv': float(avg_cv),
                            'interpretation': 'high' if avg_cv > 20 else 'moderate' if avg_cv > 10 else 'low'
                        }
            
            # 5. Key policy recommendations
            insights['key_recommendations'] = self._generate_recommendations(insights)
            
            return insights
    
    def _generate_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """Generate policy recommendations based on insights.
        
        Parameters
        ----------
        insights : dict
            Dictionary of policy insights
            
        Returns
        -------
        list
            List of policy recommendations
        """
        recommendations = []
        
        # Based on volatility
        if 'price_volatility_by_conflict' in insights:
            high_volatility_commodities = []
            for commodity, regimes in insights['price_volatility_by_conflict'].items():
                if any(data['volatility_pct'] > 20 for data in regimes.values()):
                    high_volatility_commodities.append(commodity)
            
            if high_volatility_commodities:
                recommendations.append(
                    f"Implement price stabilization mechanisms for highly volatile commodities: "
                    f"{', '.join(high_volatility_commodities)}"
                )
        
        # Based on accessibility
        if 'market_accessibility' in insights:
            if insights['market_accessibility']['mean_accessibility'] < 90:
                recommendations.append(
                    "Improve market monitoring and data collection in low-accessibility areas"
                )
        
        # Based on essential commodities
        if 'essential_commodities_trends' in insights:
            high_inflation = [
                comm for comm, data in insights['essential_commodities_trends'].items()
                if data.get('total_price_change_pct', 0) > 100
            ]
            if high_inflation:
                recommendations.append(
                    f"Priority intervention needed for hyperinflation in: {', '.join(high_inflation)}"
                )
        
        # Based on geographic disparities
        if 'geographic_disparities' in insights:
            high_disparity = [
                comm for comm, data in insights['geographic_disparities'].items()
                if data.get('interpretation') == 'high'
            ]
            if high_disparity:
                recommendations.append(
                    "Strengthen cross-market trade corridors to reduce geographic price disparities"
                )
        
        # Always include conflict-related recommendation
        recommendations.append(
            "Monitor conflict events as early warning indicators for market disruptions"
        )
        
        return recommendations
    
    def save_analysis_report(self, output_path: Path) -> None:
        """Save comprehensive analysis report.
        
        Parameters
        ----------
        output_path : Path
            Path for output file (JSON format)
        """
        with timer("save_analysis_report"):
            report = {
                'analysis_date': pd.Timestamp.now().isoformat(),
                'results_directory': str(self.results_dir),
                'tier1_available': self.tier1_results is not None,
                'tier2_commodities_analyzed': len(self.tier2_results),
                'tier3_available': self.tier3_results is not None
            }
            
            with open(output_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            info(f"Analysis report saved to {output_path}")