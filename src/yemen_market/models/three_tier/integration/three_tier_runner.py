"""Three-tier analysis orchestrator.

This module coordinates the execution of all three tiers of the market
integration methodology, ensuring proper data flow and result aggregation.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from pathlib import Path
import json
import warnings

from yemen_market.utils.logging import (
    info, warning, error, timer, progress, log_data_shape, bind, log_metric
)
from yemen_market.models.three_tier.core.panel_data_handler import PanelDataHandler
from yemen_market.models.three_tier.core.results_container import ResultsContainer

# Import tier models
from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel
from yemen_market.models.three_tier.tier2_commodity import CommodityExtractor
from yemen_market.models.three_tier.diagnostics import ThreeTierPanelDiagnostics # NEW
from yemen_market.models.three_tier.tier3_validation import (
    StaticFactorModel, PCAMarketIntegration, ConflictIntegrationValidator
)

# Set module context
bind(module=__name__)


class ThreeTierAnalysis:
    """Master coordinator for three-tier market integration analysis.
    
    This class orchestrates the full three-tier methodology:
    1. Tier 1: Pooled panel analysis with fixed effects
    2. Tier 2: Commodity-specific threshold models
    3. Tier 3: Factor analysis and validation
    
    It handles data flow between tiers, error handling, and result aggregation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize three-tier analysis.
        
        Parameters
        ----------
        config : dict, optional
            Configuration for all tiers:
            - tier1_config: Config for pooled panel model
            - tier2_config: Config for commodity models
            - tier3_config: Config for validation models
            - output_dir: Directory for saving results
            - run_parallel: Whether to run tiers in parallel (default: False)
        """
        self.config = config or {}
        
        # Extract tier-specific configs
        self.tier1_config = self.config.get('tier1_config', {})
        self.tier2_config = self.config.get('tier2_config', {})
        self.tier3_config = self.config.get('tier3_config', {})
        self.diagnostic_config = self.config.get('diagnostic_config', {}) # NEW
        self.run_diagnostics = self.config.get('run_diagnostics', True) # NEW
        
        # General settings
        self.output_dir = Path(self.config.get('output_dir', 'results/three_tier'))
        self.run_parallel = self.config.get('run_parallel', False)
        
        # Initialize components
        # Check if panel_config was provided
        panel_config = self.config.get('panel_config', None)
        self.panel_handler = PanelDataHandler(config=panel_config)
        self.tier1_model = None
        self.tier2_models = {}
        self.tier3_models = {}
        
        # Results storage
        self.results = {
            'tier1': None,
            'tier2': {},
            'tier3': {},
            'summary': {}
        }
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        info(f"Initialized ThreeTierAnalysis with output to {self.output_dir}")
    
    def run_full_analysis(self, data: pd.DataFrame, 
                         conflict_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Run complete three-tier analysis.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data with columns: governorate, commodity, date, usd_price
        conflict_data : pd.DataFrame, optional
            Conflict events for Tier 3 validation
            
        Returns
        -------
        dict
            Complete results from all tiers
        """
        with timer("full_three_tier_analysis"):
            info("Starting three-tier market integration analysis")
            
            # Validate input data
            if not self.panel_handler.validate_3d_structure(data):
                raise ValueError("Invalid panel data structure")
            
            # Store data for access by all tiers
            self.data = data
            self.conflict_data = conflict_data
            
            try:
                # Run Tier 1: Pooled panel analysis
                info("=" * 60)
                info("TIER 1: Pooled Panel Analysis")
                info("=" * 60)
                self._run_tier1()
                
                # Run Tier 2: Commodity-specific analysis
                info("=" * 60)
                info("TIER 2: Commodity-Specific Analysis")
                info("=" * 60)
                self._run_tier2()
                
                # Run Tier 3: Validation and factor analysis
                info("=" * 60)
                info("TIER 3: Validation Analysis")
                info("=" * 60)
                self._run_tier3()
                
                # Cross-tier validation
                info("=" * 60)
                info("CROSS-TIER VALIDATION")
                info("=" * 60)
                self._cross_tier_validation()
                
                # Generate summary
                self._generate_summary()
                
                # Save all results
                self._save_results()
                
                info("Three-tier analysis completed successfully")
                
            except Exception as e:
                error(f"Error in three-tier analysis: {str(e)}")
                raise
            
            return self.results
    
    def _run_tier1(self) -> None:
        """Run Tier 1 pooled panel analysis."""
        with timer("tier1_analysis"):
            try:
                # Initialize model
                self.tier1_model = PooledPanelModel(self.tier1_config)
                
                # Fit model
                self.tier1_model.fit(self.data)
                
                # Store results initially
                tier1_raw_results = self.tier1_model.get_results()
                
                # Run diagnostics automatically
                diag_report_obj = None
                if self.run_diagnostics:
                    info("Running Tier 1 diagnostics...")
                    diagnostics_runner = ThreeTierPanelDiagnostics(tier=1, config=self.diagnostic_config)
                    diag_report_obj = diagnostics_runner.run_diagnostics(
                        tier1_raw_results,
                        self.data,
                        self.diagnostic_config
                    )
                    
                    # Check for critical failures and apply corrections
                    if diag_report_obj and diag_report_obj.has_critical_failures():
                        warning("Critical diagnostic failures detected in Tier 1")
                        
                        # Get recommended corrections
                        corrections = diagnostics_runner._apply_diagnostic_corrections(
                            self.tier1_model, diag_report_obj
                        )
                        
                        # Apply corrections to model configuration
                        if corrections:
                            info(f"Applying corrections: {corrections}")
                            
                            # Update model configuration based on corrections
                            if 'standard_errors' in corrections:
                                self.tier1_model.config['standard_errors'] = corrections['standard_errors']
                            
                            if 'transformation' in corrections and corrections['transformation'] == 'first_difference':
                                warning("Unit roots detected - first differencing recommended")
                                # Note: Actual transformation would require data preprocessing
                            
                            # Re-run model with corrected configuration
                            info("Re-running Tier 1 model with corrections...")
                            self.tier1_model.fit(self.data)
                            tier1_raw_results = self.tier1_model.get_results()
                            
                            # Re-run diagnostics to verify improvements
                            info("Re-running diagnostics after corrections...")
                            diag_report_obj_corrected = diagnostics_runner.run_diagnostics(
                                tier1_raw_results,
                                self.data,
                                self.diagnostic_config
                            )
                            diag_report_obj = diag_report_obj_corrected
                
                # Store final results including diagnostics
                self.results['tier1'] = {
                    'model_results': tier1_raw_results,
                    'diagnostics': diag_report_obj.to_dict() if diag_report_obj else None,
                    'corrections_applied': corrections if 'corrections' in locals() else None
                }
                
                # Log key metrics from the model_results part
                if hasattr(tier1_raw_results, 'comparison_metrics') and tier1_raw_results.comparison_metrics:
                    log_metric("tier1_r_squared", tier1_raw_results.comparison_metrics.r_squared)
                
                info("Tier 1 analysis completed successfully")
                
            except Exception as e:
                error(f"Error in Tier 1: {str(e)}")
                self.results['tier1'] = {'error': str(e)}
    
    def _run_tier2(self) -> None:
        """Run Tier 2 commodity-specific analysis."""
        with timer("tier2_analysis"):
            # Get unique commodities
            commodities = self.data['commodity'].unique()
            info(f"Running Tier 2 for {len(commodities)} commodities")
            
            # Initialize commodity extractor with correct column names
            from ..tier2_commodity.commodity_extractor import CommodityExtractorConfig
            if isinstance(self.tier2_config, dict):
                # Update config to use correct column names
                tier2_config_dict = self.tier2_config.copy()
                tier2_config_dict['required_columns'] = ['market', 'commodity', 'date', 'usd_price']
                tier2_config = CommodityExtractorConfig(**tier2_config_dict)
            else:
                tier2_config = self.tier2_config
                # Update required columns if it's already a config object
                tier2_config.required_columns = ['market', 'commodity', 'date', 'usd_price']
            extractor = CommodityExtractor(tier2_config)
            
            with progress("Analyzing commodities", total=len(commodities)) as update:
                for commodity in commodities:
                    try:
                        # Extract and analyze commodity
                        commodity_results = extractor.analyze_commodity(
                            self.data, 
                            commodity
                        )
                        
                        # Run diagnostics for commodity-specific model if enabled
                        commodity_diag_report = None
                        if self.run_diagnostics and commodity_results and 'results' in commodity_results:
                            commodity_diagnostics = ThreeTierPanelDiagnostics(
                                tier=2, 
                                config={**self.diagnostic_config, 'commodity': commodity}
                            )
                            commodity_diag_report = commodity_diagnostics.run_diagnostics(
                                commodity_results['results'],
                                self.data[self.data['commodity'] == commodity],
                                self.diagnostic_config
                            )
                        
                        # Store results with diagnostics
                        self.tier2_models[commodity] = commodity_results
                        self.results['tier2'][commodity] = {
                            'model_results': commodity_results.get('results'),
                            'diagnostics': commodity_diag_report.to_dict() if commodity_diag_report else None
                        }
                        
                        info(f"Completed analysis for {commodity}")
                        
                    except Exception as e:
                        warning(f"Error analyzing {commodity}: {str(e)}")
                        self.results['tier2'][commodity] = {'error': str(e)}
                    
                    update(1)
            
            info(f"Tier 2 completed: {len(self.tier2_models)} commodities analyzed")
    
    def _run_tier3(self) -> None:
        """Run Tier 3 validation analysis."""
        with timer("tier3_analysis"):
            # 1. Static factor analysis
            try:
                info("Running static factor analysis")
                static_model = StaticFactorModel(self.tier3_config)
                static_model.fit(self.data)
                
                # Run diagnostics for static factor model
                static_diag_report = None
                if self.run_diagnostics:
                    static_diagnostics = ThreeTierPanelDiagnostics(
                        tier=3,
                        config={**self.diagnostic_config, 'model_type': 'static_factors'}
                    )
                    static_results = static_model.get_results()
                    if static_results:
                        static_diag_report = static_diagnostics.run_diagnostics(
                            static_results,
                            self.data,
                            self.diagnostic_config
                        )
                
                self.tier3_models['static_factors'] = static_model
                self.results['tier3']['static_factors'] = {
                    'model_results': static_model.get_results(),
                    'diagnostics': static_diag_report.to_dict() if static_diag_report else None
                }
            except Exception as e:
                warning(f"Error in static factor analysis: {str(e)}")
                self.results['tier3']['static_factors'] = {'error': str(e)}
            
            # 2. PCA integration analysis
            try:
                info("Running PCA integration analysis")
                pca_analyzer = PCAMarketIntegration(self.tier3_config)
                integration_report = pca_analyzer.generate_integration_report(self.data)
                self.tier3_models['pca_integration'] = pca_analyzer
                self.results['tier3']['pca_integration'] = integration_report
            except Exception as e:
                warning(f"Error in PCA integration analysis: {str(e)}")
                self.results['tier3']['pca_integration'] = {'error': str(e)}
            
            # 3. Conflict validation (if data available)
            if self.conflict_data is not None:
                try:
                    info("Running conflict validation analysis")
                    conflict_validator = ConflictIntegrationValidator(self.tier3_config)
                    
                    # Get integration scores from PCA analysis
                    integration_scores = None
                    if 'pca_integration' in self.tier3_models:
                        integration_scores = self.tier3_models['pca_integration'].rolling_pca_analysis(self.data)
                    
                    conflict_validator.fit(
                        self.data,
                        conflict_data=self.conflict_data,
                        integration_scores=integration_scores
                    )
                    
                    self.tier3_models['conflict_validation'] = conflict_validator
                    self.results['tier3']['conflict_validation'] = conflict_validator.get_results()
                    
                except Exception as e:
                    warning(f"Error in conflict validation: {str(e)}")
                    self.results['tier3']['conflict_validation'] = {'error': str(e)}
            else:
                info("Skipping conflict validation (no conflict data provided)")
            
            info("Tier 3 validation completed")
    
    def _cross_tier_validation(self) -> None:
        """Perform cross-tier validation and consistency checks."""
        with timer("cross_tier_validation"):
            validation_results = {}
            
            # 1. Compare Tier 1 and Tier 2 results
            if self.results['tier1'] and self.results['tier2']:
                validation_results['tier1_vs_tier2'] = self._compare_tier1_tier2()
            
            # 2. Validate Tier 2 patterns with Tier 3 factors
            if self.results['tier2'] and self.results['tier3'].get('static_factors'):
                validation_results['tier2_vs_tier3'] = self._validate_tier2_with_factors()
            
            # 3. Check consistency of integration measures
            validation_results['integration_consistency'] = self._check_integration_consistency()
            
            # Store validation results
            self.results['cross_validation'] = validation_results
            
            # Log any inconsistencies
            for check, result in validation_results.items():
                if result.get('inconsistent'):
                    warning(f"Inconsistency found in {check}: {result.get('message')}")
    
    def _compare_tier1_tier2(self) -> Dict[str, Any]:
        """Compare results between Tier 1 and Tier 2."""
        comparison = {
            'consistent': True,
            'details': {}
        }
        
        # Extract Tier 1 coefficients if available
        tier1_results = self.results['tier1']
        if hasattr(tier1_results, 'coefficients'):
            tier1_effects = tier1_results.coefficients
            
            # Compare with average effects across commodities in Tier 2
            commodity_effects = []
            for commodity, results in self.results['tier2'].items():
                if isinstance(results, dict) and 'coefficients' in results:
                    commodity_effects.append(results['coefficients'])
            
            if commodity_effects:
                # Check if signs are consistent
                # This is a simplified check - real implementation would be more sophisticated
                comparison['details']['n_commodities_analyzed'] = len(commodity_effects)
                comparison['details']['tier1_has_results'] = bool(tier1_effects)
            
        return comparison
    
    def _validate_tier2_with_factors(self) -> Dict[str, Any]:
        """Validate Tier 2 patterns using Tier 3 factor analysis."""
        validation = {
            'factor_alignment': {},
            'commodity_clustering': {}
        }
        
        # Get factor loadings from Tier 3
        tier3_results = self.results['tier3'].get('static_factors')
        if hasattr(tier3_results, 'tier_specific') and 'loadings' in tier3_results.tier_specific:
            loadings = tier3_results.tier_specific['loadings']
            
            # Check which commodities load heavily on each factor
            # This helps validate commodity-specific patterns from Tier 2
            for commodity in self.results['tier2']:
                # Extract commodity-specific series from loadings
                commodity_loadings = loadings[loadings.index.str.contains(commodity)]
                
                if not commodity_loadings.empty:
                    # Find dominant factor for this commodity
                    dominant_factor = commodity_loadings.abs().mean().idxmax()
                    validation['factor_alignment'][commodity] = {
                        'dominant_factor': dominant_factor,
                        'loading_strength': commodity_loadings[dominant_factor].mean()
                    }
        
        return validation
    
    def _check_integration_consistency(self) -> Dict[str, Any]:
        """Check consistency of integration measures across tiers."""
        consistency = {
            'measures': {},
            'consistent': True
        }
        
        # Collect integration measures from different sources
        
        # From Tier 1 - R-squared as integration proxy
        if self.results['tier1'] and hasattr(self.results['tier1'], 'comparison_metrics'):
            consistency['measures']['tier1_r_squared'] = (
                self.results['tier1'].comparison_metrics.r_squared
            )
        
        # From Tier 3 - PCA first component variance
        tier3_pca = self.results['tier3'].get('pca_integration')
        if tier3_pca and hasattr(tier3_pca, 'tier_specific'):
            overall_integration = tier3_pca.tier_specific.get('overall_integration', {})
            if 'pc1_variance_explained' in overall_integration:
                consistency['measures']['tier3_pc1_variance'] = (
                    overall_integration['pc1_variance_explained']
                )
        
        # Check if measures are aligned (both high or both low)
        if len(consistency['measures']) >= 2:
            values = list(consistency['measures'].values())
            # Simple consistency check - all above 0.5 or all below 0.5
            if not (all(v > 0.5 for v in values) or all(v < 0.5 for v in values)):
                consistency['consistent'] = False
                consistency['message'] = "Integration measures show conflicting signals"
        
        return consistency
    
    def _generate_summary(self) -> None:
        """Generate executive summary of findings."""
        summary = {
            'overview': {},
            'key_findings': [],
            'recommendations': []
        }
        
        # Data overview
        summary['overview'] = {
            'n_observations': len(self.data),
            'n_markets': self.data['governorate'].nunique(),
            'n_commodities': self.data['commodity'].nunique(),
            'date_range': (
                self.data['date'].min().strftime('%Y-%m-%d'),
                self.data['date'].max().strftime('%Y-%m-%d')
            ),
            'tiers_completed': sum(
                1 for tier_results in [self.results['tier1'], self.results['tier2'], self.results['tier3']]
                if tier_results
            )
        }
        
        # Extract key findings from each tier
        
        # Tier 1 findings
        tier1_results = self.results.get('tier1', {})
        if isinstance(tier1_results, dict):
            # Add diagnostic findings
            if tier1_results.get('diagnostics'):
                diag_summary = tier1_results['diagnostics'].get('summary', {})
                if diag_summary.get('failed_tests', 0) > 0:
                    summary['key_findings'].append(
                        f"Tier 1: {diag_summary['failed_tests']} diagnostic tests failed"
                    )
                    
                # Add specific test failures
                test_results = tier1_results['diagnostics'].get('test_results', {})
                for test_name, result in test_results.items():
                    if not result.get('passed', True):
                        summary['key_findings'].append(
                            f"Tier 1 Diagnostic: {test_name} failed (p={result.get('p_value', 'N/A'):.4f})"
                        )
            
            # Add model warnings if available
            model_results = tier1_results.get('model_results')
            if model_results and hasattr(model_results, 'metadata'):
                if hasattr(model_results.metadata, 'warnings') and model_results.metadata.warnings:
                    summary['key_findings'].extend([
                        f"Tier 1: {w}" for w in model_results.metadata.warnings
                    ])
        
        # Tier 2 findings
        high_integration_commodities = []
        commodities_with_diagnostic_issues = []
        
        for commodity, results in self.results['tier2'].items():
            if isinstance(results, dict):
                # Check integration level
                model_results = results.get('model_results', results)
                if isinstance(model_results, dict) and model_results.get('integration_level') == 'High':
                    high_integration_commodities.append(commodity)
                
                # Check diagnostic issues
                if results.get('diagnostics'):
                    diag_summary = results['diagnostics'].get('summary', {})
                    if diag_summary.get('failed_tests', 0) > 0:
                        commodities_with_diagnostic_issues.append(commodity)
        
        if high_integration_commodities:
            summary['key_findings'].append(
                f"Tier 2: High integration found for: {', '.join(high_integration_commodities)}"
            )
        
        if commodities_with_diagnostic_issues:
            summary['key_findings'].append(
                f"Tier 2: Diagnostic issues found for {len(commodities_with_diagnostic_issues)} commodities"
            )
        
        # Tier 3 findings
        tier3_pca = self.results['tier3'].get('pca_integration')
        if tier3_pca and hasattr(tier3_pca, 'tier_specific'):
            overall = tier3_pca.tier_specific.get('overall_integration', {})
            if 'integration_level' in overall:
                summary['key_findings'].append(
                    f"Tier 3: Overall market integration level is {overall['integration_level']}"
                )
        
        # Conflict impact
        conflict_results = self.results['tier3'].get('conflict_validation')
        if conflict_results and hasattr(conflict_results, 'tier_specific'):
            event_study = conflict_results.tier_specific.get('event_study', {})
            if 'pct_events_reducing_integration' in event_study:
                summary['key_findings'].append(
                    f"Tier 3: {event_study['pct_events_reducing_integration']:.1f}% of conflict "
                    "events reduce market integration"
                )
        
        # Generate recommendations based on findings
        if summary['overview'].get('tiers_completed', 0) == 3:
            summary['recommendations'].append(
                "Complete three-tier analysis provides robust evidence for policy decisions"
            )
        
        # Store summary
        self.results['summary'] = summary
    
    def _save_results(self) -> None:
        """Save all results to disk."""
        with timer("save_results"):
            info(f"Saving results to {self.output_dir}")
            
            # Save individual tier results
            for tier in ['tier1', 'tier2', 'tier3']:
                tier_dir = self.output_dir / tier
                tier_dir.mkdir(exist_ok=True)
                
                if tier == 'tier2':
                    # Save each commodity separately
                    for commodity, results in self.results[tier].items():
                        if isinstance(results, (ResultsContainer, dict)):
                            output_file = tier_dir / f"{commodity}_results.json"
                            self._save_tier_results(results, output_file)
                            
                            # Save diagnostic report separately if available
                            if isinstance(results, dict) and results.get('diagnostics'):
                                diag_file = tier_dir / f"{commodity}_diagnostics.json"
                                with open(diag_file, 'w') as f:
                                    json.dump(results['diagnostics'], f, indent=2, default=str)
                else:
                    # Save tier results
                    if self.results[tier]:
                        output_file = tier_dir / f"{tier}_results.json"
                        self._save_tier_results(self.results[tier], output_file)
                        
                        # Save diagnostic report separately if available
                        tier_data = self.results[tier]
                        if isinstance(tier_data, dict) and tier_data.get('diagnostics'):
                            diag_file = tier_dir / f"{tier}_diagnostics.json"
                            with open(diag_file, 'w') as f:
                                json.dump(tier_data['diagnostics'], f, indent=2, default=str)
            
            # Save cross-validation results
            if 'cross_validation' in self.results:
                cv_file = self.output_dir / "cross_validation_results.json"
                with open(cv_file, 'w') as f:
                    json.dump(self.results['cross_validation'], f, indent=2, default=str)
            
            # Save summary
            summary_file = self.output_dir / "analysis_summary.json"
            with open(summary_file, 'w') as f:
                json.dump(self.results['summary'], f, indent=2, default=str)
            
            # Create summary report
            self._create_summary_report()
            
            info("All results saved successfully")
    
    def _save_tier_results(self, results: Union[ResultsContainer, Dict], 
                          output_file: Path) -> None:
        """Save tier-specific results."""
        if isinstance(results, ResultsContainer):
            # Use export_results method which properly serializes the container
            results.export_results(str(output_file))
        else:
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
    
    def _create_summary_report(self) -> None:
        """Create a markdown summary report."""
        report_file = self.output_dir / "three_tier_analysis_report.md"
        
        lines = [
            "# Three-Tier Market Integration Analysis Report",
            f"\nGenerated: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "\n## Executive Summary\n"
        ]
        
        # Add overview
        summary = self.results['summary']
        lines.append(f"- **Markets analyzed**: {summary['overview']['n_markets']}")
        lines.append(f"- **Commodities**: {summary['overview']['n_commodities']}")
        lines.append(f"- **Time period**: {summary['overview']['date_range'][0]} to {summary['overview']['date_range'][1]}")
        lines.append(f"- **Total observations**: {summary['overview']['n_observations']:,}")
        
        # Key findings
        if summary['key_findings']:
            lines.append("\n## Key Findings\n")
            for finding in summary['key_findings']:
                lines.append(f"- {finding}")
        
        # Tier summaries
        lines.append("\n## Tier Results\n")
        
        # Tier 1
        lines.append("### Tier 1: Pooled Panel Analysis")
        tier1_data = self.results.get('tier1', {})
        if isinstance(tier1_data, dict):
            model_results = tier1_data.get('model_results')
            if model_results and hasattr(model_results, 'comparison_metrics'):
                metrics = model_results.comparison_metrics
                lines.append(f"- R-squared: {metrics.r_squared:.4f}")
                lines.append(f"- Observations: {model_results.metadata.get('n_observations', 'N/A')}")
            
            # Add diagnostic summary
            if tier1_data.get('diagnostics'):
                diag_summary = tier1_data['diagnostics'].get('summary', {})
                lines.append(f"- Diagnostic tests: {diag_summary.get('total_tests', 0)} run, "
                           f"{diag_summary.get('passed_tests', 0)} passed")
                
                if tier1_data.get('corrections_applied'):
                    lines.append(f"- Corrections applied: {', '.join(tier1_data['corrections_applied'].keys())}")
        
        # Tier 2
        lines.append("\n### Tier 2: Commodity-Specific Analysis")
        lines.append(f"- Commodities analyzed: {len(self.results['tier2'])}")
        
        # Tier 3
        lines.append("\n### Tier 3: Validation Analysis")
        if self.results['tier3']:
            lines.append(f"- Validation methods: {', '.join(self.results['tier3'].keys())}")
        
        # Recommendations
        if summary['recommendations']:
            lines.append("\n## Recommendations\n")
            for rec in summary['recommendations']:
                lines.append(f"- {rec}")
        
        # Write report
        with open(report_file, 'w') as f:
            f.write('\n'.join(lines))
        
        info(f"Summary report saved to {report_file}")
    
    def get_commodity_comparison(self) -> pd.DataFrame:
        """Get comparison of results across commodities.
        
        Returns
        -------
        pd.DataFrame
            Comparison table
        """
        if not self.results['tier2']:
            return pd.DataFrame()
        
        comparison_data = []
        
        for commodity, results in self.results['tier2'].items():
            if isinstance(results, dict) and 'error' not in results:
                row = {
                    'commodity': commodity,
                    'integration_level': results.get('integration_level', 'Unknown'),
                    'n_observations': results.get('n_observations', 0),
                    'r_squared': results.get('r_squared', np.nan)
                }
                
                # Add threshold information if available
                if 'threshold_value' in results:
                    row['has_threshold'] = True
                    row['threshold_value'] = results['threshold_value']
                else:
                    row['has_threshold'] = False
                    
                comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
    
    def visualize_results(self) -> None:
        """Create visualizations of results (placeholder for future implementation)."""
        warning("Visualization not yet implemented. Results saved to JSON files.")
        # Future: Add matplotlib/seaborn visualizations

    def _apply_diagnostic_corrections(self, model: Any, diag_report: 'DiagnosticReport') -> None:
        """
        Apply econometric corrections based on diagnostic test failures.
        
        Implements World Bank standard corrections for panel data issues:
        - Serial correlation → Newey-West HAC standard errors
        - Cross-sectional dependence → Driscoll-Kraay standard errors
        - Both issues → Driscoll-Kraay (which handles both)
        - Heteroskedasticity → Cluster-robust standard errors
        - Unit roots → First-differencing transformation
        
        Parameters
        ----------
        model : Any
            The model instance that requires correction.
        diag_report : DiagnosticReport
            Diagnostic report containing test results.
        """
        from yemen_market.utils.logging import info, warning, timer
        
        info(f"Applying diagnostic corrections for {model.name if hasattr(model, 'name') else 'model'}")
        
        if not diag_report or not diag_report.has_critical_failures():
            info("No critical diagnostic failures requiring correction")
            return
        
        # Track which corrections to apply
        corrections_needed = {
            'serial_correlation': False,
            'cross_sectional_dependence': False,
            'heteroskedasticity': False,
            'unit_roots': False
        }
        
        # Analyze test failures
        for test_result in diag_report.test_results:
            if not test_result.passed:
                if "Wooldridge" in test_result.test_name:
                    corrections_needed['serial_correlation'] = True
                elif "Pesaran CD" in test_result.test_name:
                    corrections_needed['cross_sectional_dependence'] = True
                elif "Modified Wald" in test_result.test_name:
                    corrections_needed['heteroskedasticity'] = True
                elif "Im-Pesaran-Shin" in test_result.test_name:
                    corrections_needed['unit_roots'] = True
        
        # Apply corrections based on World Bank standards
        with timer("Applying econometric corrections"):
            
            # Handle unit roots first (data transformation)
            if corrections_needed['unit_roots']:
                warning("Unit roots detected - applying first-difference transformation")
                self._apply_first_differencing(model)
            
            # Determine appropriate standard error correction
            if corrections_needed['serial_correlation'] and corrections_needed['cross_sectional_dependence']:
                # Both issues present - use Driscoll-Kraay
                info("Applying Driscoll-Kraay standard errors (handles both serial and cross-sectional correlation)")
                self._apply_driscoll_kraay_correction(model)
                
            elif corrections_needed['serial_correlation']:
                # Only serial correlation - use Newey-West
                info("Applying Newey-West HAC standard errors for serial correlation")
                self._apply_newey_west_correction(model)
                
            elif corrections_needed['cross_sectional_dependence']:
                # Only cross-sectional dependence - use Driscoll-Kraay
                info("Applying Driscoll-Kraay standard errors for cross-sectional dependence")
                self._apply_driscoll_kraay_correction(model)
                
            elif corrections_needed['heteroskedasticity']:
                # Only heteroskedasticity - use cluster-robust
                info("Applying cluster-robust standard errors for heteroskedasticity")
                self._apply_cluster_robust_correction(model)
            
            # Re-estimate model with corrections if needed
            if any(corrections_needed.values()):
                info("Re-estimating model with applied corrections")
                
                # Store original results for comparison
                if hasattr(model, 'results') and model.results is not None:
                    model.results.pre_correction_results = {
                        'coefficients': model.results.params.copy() if hasattr(model.results, 'params') else None,
                        'std_errors': model.results.bse.copy() if hasattr(model.results, 'bse') else None,
                        'corrections_applied': list(k for k, v in corrections_needed.items() if v)
                    }
                
                # Re-fit with corrections
                model.fit_with_corrections()
                info("Model re-estimation complete with diagnostic corrections")
    
    def _apply_driscoll_kraay_correction(self, model: Any) -> None:
        """Apply Driscoll-Kraay standard errors following Driscoll & Kraay (1998)."""
        if hasattr(model, 'config'):
            model.config['standard_errors'] = 'driscoll_kraay'
            model.config['bandwidth'] = 'auto'  # Automatic bandwidth selection
            model.config['kernel'] = 'bartlett'  # Bartlett kernel for HAC estimation
    
    def _apply_newey_west_correction(self, model: Any) -> None:
        """Apply Newey-West HAC standard errors following Newey & West (1987)."""
        if hasattr(model, 'config'):
            model.config['standard_errors'] = 'newey_west'
            model.config['lags'] = 'auto'  # Automatic lag selection
            model.config['kernel'] = 'bartlett'
    
    def _apply_cluster_robust_correction(self, model: Any) -> None:
        """Apply cluster-robust standard errors at the entity level."""
        if hasattr(model, 'config'):
            model.config['standard_errors'] = 'cluster_robust'
            model.config['cluster_var'] = 'entity'  # Cluster at market level
    
    def _apply_first_differencing(self, model: Any) -> None:
        """Apply first-difference transformation for unit root issues."""
        if hasattr(model, 'config'):
            model.config['transformation'] = 'first_difference'
            model.config['include_constant'] = False  # No constant in FD models
