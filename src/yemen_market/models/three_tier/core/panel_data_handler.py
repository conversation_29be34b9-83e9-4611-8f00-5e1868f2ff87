"""Panel data handler for 3D market × commodity × time structures.

This module provides utilities to transform 3D panel data into formats
suitable for different tiers of analysis.
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Optional, Dict, Union, Any
from dataclasses import dataclass, field
from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, bind
)

# Set module context
bind(module=__name__)


@dataclass
class PanelDataConfig:
    """Configuration for panel data handling."""

    # Required columns
    required_columns: List[str] = field(default_factory=lambda: ['date', 'market', 'commodity', 'price'])

    # Column mappings
    market_col: str = 'market'
    commodity_col: str = 'commodity'
    time_col: str = 'date'
    price_col: str = 'price'

    # Validation thresholds
    min_entities: int = 5
    min_periods: int = 10
    max_missing_pct: float = 0.3

    # Data handling options
    handle_duplicates: str = 'warn'  # 'warn', 'drop', 'error'
    missing_data_strategy: str = 'interpolate'  # 'drop', 'ffill', 'interpolate'
    max_interpolation_gap: int = 2

    # Feature engineering
    add_time_features: bool = True
    add_lags: bool = False
    lag_periods: List[int] = field(default_factory=lambda: [1, 4])
    add_differences: bool = False
    diff_periods: List[int] = field(default_factory=lambda: [1])

    # Balance requirements
    require_balanced: bool = False
    balance_method: str = 'drop'  # 'drop', 'fill'


class PanelDataHandler:
    """Central handler for 3D panel data operations.

    This class manages the transformation of 3D panel data (market × commodity × time)
    into appropriate formats for each tier of analysis:
    - Tier 1: Entity-time panels (market-commodity pairs)
    - Tier 2: 2D panels for each commodity
    - Tier 3: Wide matrices for factor analysis
    """

    def __init__(self, config: Optional[PanelDataConfig] = None):
        """Initialize the panel data handler.

        Parameters
        ----------
        config : PanelDataConfig, optional
            Configuration for panel data handling. If None, uses default config.
        """
        self.config = config or PanelDataConfig()

        # Set column mappings from config
        self.required_columns = self.config.required_columns
        self.entity_col = 'entity'  # For Tier 1 - the combined market_commodity string
        self.market_col = self.config.market_col
        self.commodity_col = self.config.commodity_col
        self.time_col = self.config.time_col
        self.price_col = self.config.price_col

        bind(class_name=self.__class__.__name__)

    def validate_3d_structure(self, df: pd.DataFrame) -> bool:
        """Validate that dataframe has proper 3D panel structure.

        Parameters
        ----------
        df : pd.DataFrame
            Input dataframe to validate

        Returns
        -------
        bool
            True if valid 3D structure, False otherwise
        """
        with timer("validate_3d_structure"):
            # Check required columns
            missing_cols = set(self.required_columns) - set(df.columns)
            if missing_cols:
                error(f"Missing required columns: {missing_cols}")
                return False

            # Check data types
            if not pd.api.types.is_datetime64_any_dtype(df[self.time_col]):
                warning(f"{self.time_col} is not datetime, attempting conversion")
                try:
                    df[self.time_col] = pd.to_datetime(df[self.time_col])
                except Exception as e:
                    error(f"Failed to convert {self.time_col} to datetime: {e}")
                    return False

            # Log structure info
            n_markets = df[self.market_col].nunique()
            n_commodities = df[self.commodity_col].nunique()
            n_periods = df[self.time_col].nunique()
            n_obs = len(df)

            info(f"3D Panel Structure: {n_markets} markets × {n_commodities} commodities × {n_periods} periods = {n_obs} observations")

            # Check for duplicates
            duplicates = df.duplicated(subset=[self.market_col, self.commodity_col, self.time_col])
            if duplicates.any():
                n_dups = duplicates.sum()
                warning(f"Found {n_dups} duplicate observations")

            # Calculate coverage
            theoretical_obs = n_markets * n_commodities * n_periods
            coverage = (n_obs / theoretical_obs) * 100 if theoretical_obs > 0 else 0
            info(f"Panel coverage: {coverage:.1f}% ({n_obs}/{theoretical_obs} possible observations)")

            return True

    def create_entity_panel(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create entity-time panel for Tier 1 analysis.

        Transforms 3D data into 2D by creating market-commodity entities.

        Parameters
        ----------
        df : pd.DataFrame
            Input 3D panel data

        Returns
        -------
        pd.DataFrame
            2D panel with entity-time structure
        """
        with timer("create_entity_panel"):
            # Validate input
            if not self.validate_3d_structure(df):
                raise ValueError("Invalid 3D panel structure")

            # Create copy to avoid modifying original
            panel_df = df.copy()

            # Create entity identifier
            panel_df[self.entity_col] = (
                panel_df[self.market_col].astype(str) + "_" +
                panel_df[self.commodity_col].astype(str)
            )

            # Sort by entity and time
            panel_df = panel_df.sort_values([self.entity_col, self.time_col])

            # Add entity and time indices
            panel_df['entity_id'] = pd.Categorical(panel_df[self.entity_col]).codes
            panel_df['time_id'] = pd.factorize(panel_df[self.time_col])[0]

            # Log transformation info
            n_entities = panel_df[self.entity_col].nunique()
            n_periods = panel_df[self.time_col].nunique()
            log_data_shape("entity_panel", panel_df)
            info(f"Created entity panel: {n_entities} entities × {n_periods} periods")

            # Check balance
            entity_counts = panel_df.groupby(self.entity_col).size()
            if entity_counts.min() < entity_counts.max():
                info(f"Unbalanced panel: entity observations range from {entity_counts.min()} to {entity_counts.max()}")
            else:
                info("Balanced panel detected")

            return panel_df

    def extract_commodity_panel(self, df: pd.DataFrame, commodity: str) -> pd.DataFrame:
        """Extract 2D panel for a specific commodity (Tier 2).

        Parameters
        ----------
        df : pd.DataFrame
            Input 3D panel data
        commodity : str
            Commodity to extract

        Returns
        -------
        pd.DataFrame
            2D panel for the specified commodity
        """
        with timer(f"extract_commodity_panel_{commodity}"):
            # Filter for commodity
            commodity_df = df[df[self.commodity_col] == commodity].copy()

            if commodity_df.empty:
                error(f"No data found for commodity: {commodity}")
                return pd.DataFrame()

            # Sort by market and time
            commodity_df = commodity_df.sort_values([self.market_col, self.time_col])

            # Log extraction info
            n_markets = commodity_df[self.market_col].nunique()
            n_periods = commodity_df[self.time_col].nunique()
            n_obs = len(commodity_df)

            log_data_shape(f"commodity_panel_{commodity}", commodity_df)
            info(f"Extracted {commodity} panel: {n_markets} markets × {n_periods} periods = {n_obs} observations")

            # Check coverage for this commodity
            theoretical_obs = n_markets * n_periods
            coverage = (n_obs / theoretical_obs) * 100 if theoretical_obs > 0 else 0
            info(f"{commodity} coverage: {coverage:.1f}%")

            return commodity_df

    def create_wide_matrix(self, df: pd.DataFrame, value_col: str = None) -> pd.DataFrame:
        """Create wide matrix for factor analysis (Tier 3).

        Transforms data into wide format with time as rows and
        market-commodity pairs as columns.

        Parameters
        ----------
        df : pd.DataFrame
            Input panel data
        value_col : str, optional
            Column to use as values (default: usd_price)

        Returns
        -------
        pd.DataFrame
            Wide matrix suitable for factor analysis
        """
        with timer("create_wide_matrix"):
            if value_col is None:
                value_col = self.price_col

            # Create entity column if not exists
            if self.entity_col not in df.columns:
                df = self.create_entity_panel(df)

            # Pivot to wide format
            wide_df = df.pivot(
                index=self.time_col,
                columns=self.entity_col,
                values=value_col
            )

            # Log matrix info
            n_periods, n_series = wide_df.shape
            missing_pct = (wide_df.isna().sum().sum() / (n_periods * n_series)) * 100

            log_data_shape("wide_matrix", wide_df)
            info(f"Created wide matrix: {n_periods} periods × {n_series} series")
            info(f"Missing values: {missing_pct:.1f}%")

            # Check which series have too many missing values
            series_missing = wide_df.isna().sum() / n_periods
            high_missing = series_missing[series_missing > 0.5]
            if len(high_missing) > 0:
                warning(f"{len(high_missing)} series have >50% missing values")

            return wide_df

    def get_panel_info(self, df: pd.DataFrame) -> Dict[str, Union[int, float, List[str]]]:
        """Get comprehensive information about panel structure.

        Parameters
        ----------
        df : pd.DataFrame
            Input panel data

        Returns
        -------
        dict
            Dictionary with panel statistics
        """
        info_dict = {
            'n_observations': len(df),
            'n_markets': df[self.market_col].nunique(),
            'n_commodities': df[self.commodity_col].nunique(),
            'n_periods': df[self.time_col].nunique(),
            'date_range': (df[self.time_col].min(), df[self.time_col].max()),
            'markets': sorted(df[self.market_col].unique()),
            'commodities': sorted(df[self.commodity_col].unique()),
        }

        # Calculate coverage
        theoretical_obs = (info_dict['n_markets'] *
                          info_dict['n_commodities'] *
                          info_dict['n_periods'])
        info_dict['coverage_pct'] = (info_dict['n_observations'] / theoretical_obs * 100
                                     if theoretical_obs > 0 else 0)

        # Check balance by commodity
        commodity_obs = df.groupby(self.commodity_col).size()
        info_dict['min_obs_commodity'] = commodity_obs.idxmin()
        info_dict['max_obs_commodity'] = commodity_obs.idxmax()
        info_dict['balanced'] = commodity_obs.min() == commodity_obs.max()

        return info_dict

    def handle_missing_data(self, df: pd.DataFrame, method: str = 'forward_fill',
                           max_gap: int = 2) -> pd.DataFrame:
        """Handle missing data in panel.

        Parameters
        ----------
        df : pd.DataFrame
            Input panel data
        method : str
            Method to handle missing data ('forward_fill', 'interpolate', 'drop')
        max_gap : int
            Maximum gap size to fill

        Returns
        -------
        pd.DataFrame
            Panel with missing data handled
        """
        with timer(f"handle_missing_data_{method}"):
            initial_missing = df[self.price_col].isna().sum()
            info(f"Initial missing values: {initial_missing}")

            if method == 'drop':
                clean_df = df.dropna(subset=[self.price_col])

            elif method == 'forward_fill':
                # Forward fill within each entity
                clean_df = df.copy()
                clean_df[self.price_col] = (
                    clean_df.groupby([self.market_col, self.commodity_col])[self.price_col]
                    .ffill(limit=max_gap)
                )

            elif method == 'interpolate':
                # Linear interpolation within each entity
                clean_df = df.copy()
                clean_df[self.price_col] = (
                    clean_df.groupby([self.market_col, self.commodity_col])[self.price_col]
                    .apply(lambda x: x.interpolate(method='linear', limit=max_gap))
                )

            else:
                raise ValueError(f"Unknown method: {method}")

            final_missing = clean_df[self.price_col].isna().sum()
            info(f"Final missing values: {final_missing} (removed {initial_missing - final_missing})")

            return clean_df

    def validate_structure(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate panel data structure and return validation results.

        Parameters
        ----------
        df : pd.DataFrame
            Input dataframe to validate

        Returns
        -------
        tuple
            (is_valid, list_of_error_messages)
        """
        messages = []

        # Check required columns
        missing_cols = set(self.required_columns) - set(df.columns)
        if missing_cols:
            messages.extend([f"Missing required column: {col}" for col in missing_cols])

        # Check data types
        if self.time_col in df.columns:
            if not pd.api.types.is_datetime64_any_dtype(df[self.time_col]):
                try:
                    pd.to_datetime(df[self.time_col])
                except Exception:
                    messages.append(f"Cannot convert {self.time_col} to datetime")

        # Check minimum data requirements
        if len(df) == 0:
            messages.append("Empty dataset")
        elif self.market_col in df.columns and self.commodity_col in df.columns:
            n_entities = len(df.groupby([self.market_col, self.commodity_col]))
            n_periods = df[self.time_col].nunique() if self.time_col in df.columns else 0

            if n_entities < self.config.min_entities:
                messages.append(f"Too few entities: {n_entities} (minimum: {self.config.min_entities})")
            if n_periods < self.config.min_periods:
                messages.append(f"Too few periods: {n_periods} (minimum: {self.config.min_periods})")

        # Check missing data percentage
        if self.price_col in df.columns:
            missing_pct = df[self.price_col].isna().sum() / len(df)
            if missing_pct > self.config.max_missing_pct:
                messages.append(f"Too much missing data: {missing_pct:.1%} (maximum: {self.config.max_missing_pct:.1%})")

        is_valid = len(messages) == 0
        return is_valid, messages

    def create_entity_id(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create entity ID from market-commodity pairs.

        Parameters
        ----------
        df : pd.DataFrame
            Input dataframe

        Returns
        -------
        pd.DataFrame
            Dataframe with entity_id column added
        """
        result = df.copy()
        result['entity_id'] = (
            result[self.market_col].astype(str) + "_" +
            result[self.commodity_col].astype(str)
        )
        return result

    def transform_to_2d(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform to 2D panel with MultiIndex.

        Parameters
        ----------
        df : pd.DataFrame
            Input dataframe with entity_id column

        Returns
        -------
        pd.DataFrame
            2D panel with MultiIndex (entity_id, date)
        """
        if 'entity_id' not in df.columns:
            raise ValueError("entity_id column required. Call create_entity_id() first.")

        # Set MultiIndex
        result = df.set_index(['entity_id', self.time_col])

        # Sort index
        result = result.sort_index()

        return result

    def check_balance(self, panel: pd.DataFrame) -> Dict[str, Union[bool, int]]:
        """Check if panel is balanced.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with MultiIndex

        Returns
        -------
        dict
            Balance information
        """
        if not isinstance(panel.index, pd.MultiIndex):
            raise ValueError("Panel must have MultiIndex")

        entity_counts = panel.groupby(level=0).size()
        n_entities = len(entity_counts)  # Number of unique entities
        n_periods = len(panel.index.get_level_values(1).unique())

        is_balanced = entity_counts.nunique() == 1
        expected_obs = n_entities * n_periods
        missing_obs = expected_obs - len(panel)

        return {
            'is_balanced': is_balanced,
            'n_entities': n_entities,
            'n_periods': n_periods,
            'missing_observations': missing_obs,
            'expected_observations': expected_obs,
            'actual_observations': len(panel)
        }

    def balance_panel(self, panel: pd.DataFrame, method: str = 'drop') -> pd.DataFrame:
        """Balance the panel by handling missing observations.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with MultiIndex
        method : str
            Method to balance ('drop' or 'fill')

        Returns
        -------
        pd.DataFrame
            Balanced panel
        """
        if method == 'drop':
            # Keep only entities with complete time series
            entity_counts = panel.groupby(level=0).size()
            max_periods = entity_counts.max()
            complete_entities = entity_counts[entity_counts == max_periods].index
            return panel.loc[complete_entities]

        elif method == 'fill':
            # Fill missing observations with NaN
            # Create complete index
            entities = panel.index.get_level_values(0).unique()
            periods = panel.index.get_level_values(1).unique()
            complete_index = pd.MultiIndex.from_product([entities, periods], names=panel.index.names)

            # Reindex to complete index
            return panel.reindex(complete_index)

        else:
            raise ValueError(f"Unknown balance method: {method}")

    def add_time_features(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features to the panel.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with datetime index level

        Returns
        -------
        pd.DataFrame
            Panel with time features added
        """
        result = panel.copy()

        # Get time index
        if isinstance(panel.index, pd.MultiIndex):
            time_index = panel.index.get_level_values(1)
        else:
            time_index = panel.index

        # Convert to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(time_index):
            time_index = pd.to_datetime(time_index)

        # Add time features directly to the result DataFrame
        # Use .loc to avoid reindexing issues with MultiIndex
        result.loc[:, 'year'] = time_index.year.values
        result.loc[:, 'month'] = time_index.month.values
        result.loc[:, 'quarter'] = time_index.quarter.values
        result.loc[:, 'week'] = time_index.isocalendar().week.values

        return result

    def add_lags(self, panel: pd.DataFrame, variables: List[str], lags: List[int]) -> pd.DataFrame:
        """Add lagged variables to the panel.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with MultiIndex
        variables : list
            Variables to lag
        lags : list
            Lag periods

        Returns
        -------
        pd.DataFrame
            Panel with lag features added
        """
        result = panel.copy()

        for var in variables:
            if var not in panel.columns:
                continue

            for lag in lags:
                lag_col = f'{var}_lag_{lag}'
                result[lag_col] = result.groupby(level=0)[var].shift(lag)

        return result

    def add_differences(self, panel: pd.DataFrame, variables: List[str], periods: List[int]) -> pd.DataFrame:
        """Add differenced variables to the panel.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with MultiIndex
        variables : list
            Variables to difference
        periods : list
            Difference periods

        Returns
        -------
        pd.DataFrame
            Panel with difference features added
        """
        result = panel.copy()

        for var in variables:
            if var not in panel.columns:
                continue

            for period in periods:
                diff_col = f'{var}_diff_{period}'
                result[diff_col] = result.groupby(level=0)[var].diff(periods=period)

        return result

    def compute_summary_stats(self, panel: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Compute summary statistics for panel data.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with MultiIndex

        Returns
        -------
        dict
            Summary statistics (overall, between, within)
        """
        numeric_cols = panel.select_dtypes(include=[np.number]).columns

        stats = {
            'overall': {'mean': {}, 'std': {}, 'min': {}, 'max': {}},
            'between': {'mean': {}, 'std': {}, 'min': {}, 'max': {}},
            'within': {'mean': {}, 'std': {}, 'min': {}, 'max': {}}
        }

        for col in numeric_cols:
            # Overall statistics
            stats['overall']['mean'][col] = panel[col].mean()
            stats['overall']['std'][col] = panel[col].std()
            stats['overall']['min'][col] = panel[col].min()
            stats['overall']['max'][col] = panel[col].max()

            # Between statistics (across entities)
            entity_means = panel.groupby(level=0)[col].mean()
            stats['between']['mean'][col] = entity_means.mean()
            stats['between']['std'][col] = entity_means.std()
            stats['between']['min'][col] = entity_means.min()
            stats['between']['max'][col] = entity_means.max()

            # Within statistics (within entities over time)
            entity_deviations = panel.groupby(level=0)[col].apply(lambda x: x - x.mean())
            stats['within']['mean'][col] = entity_deviations.mean()
            stats['within']['std'][col] = entity_deviations.std()
            stats['within']['min'][col] = entity_deviations.min()
            stats['within']['max'][col] = entity_deviations.max()

        return stats

    def export_to_linearmodels(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Export panel to linearmodels-compatible format.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with MultiIndex

        Returns
        -------
        pd.DataFrame
            Panel in linearmodels format
        """
        # Ensure proper MultiIndex
        if not isinstance(panel.index, pd.MultiIndex):
            raise ValueError("Panel must have MultiIndex for linearmodels")

        # Remove any rows with missing index values
        result = panel.dropna(subset=[], how='all')  # Drop completely empty rows

        # Ensure index has no missing values
        result = result[~result.index.get_level_values(0).isna()]
        result = result[~result.index.get_level_values(1).isna()]

        return result

    def handle_missing_data(self, panel: pd.DataFrame, method: str = 'interpolate') -> pd.DataFrame:
        """Handle missing data in panel.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data with MultiIndex
        method : str
            Method to handle missing data

        Returns
        -------
        pd.DataFrame
            Panel with missing data handled
        """
        if method == 'drop':
            return panel.dropna()

        elif method == 'ffill':
            return panel.groupby(level=0).ffill()

        elif method == 'interpolate':
            result = panel.copy()
            numeric_cols = panel.select_dtypes(include=[np.number]).columns

            for col in numeric_cols:
                # Use transform to maintain the original index structure
                result[col] = result.groupby(level=0)[col].transform(
                    lambda x: x.interpolate(method='linear')
                )

            return result

        else:
            raise ValueError(f"Unknown method: {method}")

    def aggregate_by_dimension(self, panel: pd.DataFrame, dimension: str, agg_func: str = 'mean') -> pd.DataFrame:
        """Aggregate panel data by dimension.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data
        dimension : str
            Dimension to aggregate by ('market' or 'commodity')
        agg_func : str
            Aggregation function

        Returns
        -------
        pd.DataFrame
            Aggregated data
        """
        if dimension not in panel.columns:
            raise ValueError(f"Dimension '{dimension}' not found in panel")

        # Select only numeric columns for aggregation
        numeric_cols = panel.select_dtypes(include=[np.number]).columns
        panel_numeric = panel[numeric_cols]

        # Group by dimension and time
        if isinstance(panel.index, pd.MultiIndex):
            time_level = panel.index.get_level_values(1)
            grouped = panel_numeric.groupby([panel[dimension], time_level])
        else:
            grouped = panel_numeric.groupby([panel[dimension], panel.index])

        # Apply aggregation
        if agg_func == 'mean':
            return grouped.mean()
        elif agg_func == 'sum':
            return grouped.sum()
        elif agg_func == 'median':
            return grouped.median()
        else:
            return grouped.agg(agg_func)

    def get_panel_info(self, panel: pd.DataFrame) -> Dict[str, Union[int, bool, List[str], Dict[str, Any]]]:
        """Get comprehensive panel information.

        Parameters
        ----------
        panel : pd.DataFrame
            Panel data

        Returns
        -------
        dict
            Panel information
        """
        if isinstance(panel.index, pd.MultiIndex):
            entities = panel.index.get_level_values(0).unique()
            periods = panel.index.get_level_values(1).unique()
        else:
            entities = panel.index.unique()
            periods = [panel.index.name] if panel.index.name else ['index']

        # Check balance
        balance_info = self.check_balance(panel) if isinstance(panel.index, pd.MultiIndex) else {'is_balanced': True}

        # Missing data pattern
        missing_pattern = {}
        for col in panel.columns:
            missing_count = panel[col].isna().sum()
            missing_pattern[col] = {
                'count': missing_count,
                'percentage': missing_count / len(panel) * 100
            }

        # Summary statistics
        summary_stats = self.compute_summary_stats(panel) if isinstance(panel.index, pd.MultiIndex) else {}

        return {
            'n_entities': len(entities),
            'n_periods': len(periods),
            'n_observations': len(panel),
            'is_balanced': balance_info.get('is_balanced', True),
            'date_range': (periods.min(), periods.max()) if hasattr(periods, 'min') else None,
            'entities': list(entities),
            'missing_pattern': missing_pattern,
            'summary_stats': summary_stats
        }