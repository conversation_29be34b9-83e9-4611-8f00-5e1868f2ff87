"""Pooled panel regression implementation for Tier 1 analysis.

This module implements the pooled panel regression with multi-way fixed effects
using the linearmodels package. It handles the 3D panel structure by creating
market-commodity entities.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple, List, Union, Any
import warnings
import sys

try:
    from linearmodels import PanelOLS
    from linearmodels.panel.data import PanelData
    from linearmodels.panel.results import PanelEffectsResults
    LINEARMODELS_AVAILABLE = True
except ImportError as e:
    error_message = (
        f"Failed to import linearmodels from pooled_panel_model.py. "
        f"Original error: {e}. "
        f"Current sys.path: {sys.path}"
    )
    raise ImportError(error_message)

from yemen_market.utils.logging import (
    info, warning, error, timer, log_data_shape, bind, log_metric
)
from ..core.base_model import BaseThreeTierModel, TierType
from ..core.panel_data_handler import PanelDataHandler
from ..common import ResultsContainer, ModelMetadata
from ..core.data_validator import PanelDataValidator
from .fixed_effects import extract_estimated_effects
from pydantic import BaseModel, Field


# Set module context
bind(module=__name__)


class PooledPanelConfig(BaseModel):
    """Configuration for the PooledPanelModel."""
    entity_effects: bool = Field(True, description="Include entity (market-commodity) fixed effects.")
    time_effects: bool = Field(True, description="Include time fixed effects.")
    cluster_entity: bool = Field(True, description="Cluster standard errors by entity.")
    cluster_time: bool = Field(False, description="Cluster standard errors by time (for two-way clustering).")
    cov_type: str = Field('clustered', description="Covariance estimator type (e.g., 'clustered', 'robust', 'unadjusted').")
    drop_absorbed: bool = Field(True, description="Drop absorbed variables during fixed effect estimation.")
    dependent_var: str = Field('usd_price', description="Name of the dependent variable.")
    independent_vars: Optional[List[str]] = Field(None, description="List of independent variable names. If None, uses defaults.")
    
    # World Bank policy analysis fields
    strict_validation: bool = Field(True, description="Use strict validation for academic standards.")
    world_bank_config: Optional[Dict[str, Any]] = Field(None, description="World Bank-style configuration.")
    include_conflict_interactions: bool = Field(False, description="Include conflict interaction terms.")
    cluster_se: Optional[str] = Field(None, description="Type of cluster standard errors (e.g., 'twoway').")
    robust_se: bool = Field(False, description="Use robust standard errors.")

    class Config:
        extra = 'forbid'


class PooledPanelModel(BaseThreeTierModel):
    """Pooled panel regression with multi-way fixed effects.
    
    This model implements Tier 1 of the three-tier methodology, estimating:
    
    p_{it} = α_i + γ_t + β'X_{it} + ε_{it}
    
    where:
    - i represents market-commodity entities
    - t represents time periods
    - α_i are entity fixed effects
    - γ_t are time fixed effects
    - X_{it} are control variables
    """
    
    def __init__(self, config: Optional[Union[PooledPanelConfig, Dict]] = None):
        """Initialize pooled panel model.
        
        Parameters
        ----------
        config : PooledPanelConfig or dict, optional
            Configuration object or dictionary for the pooled panel model.
        """
        # Handle both dict and PooledPanelConfig inputs
        if isinstance(config, dict):
            self.config = PooledPanelConfig(**config)
        else:
            self.config = config or PooledPanelConfig()
            
        super().__init__(config=self.config) # Pass config object to parent
        
        # Set tier type
        self.tier = TierType.TIER1_POOLED
        
        # Check dependencies
        if not LINEARMODELS_AVAILABLE:
            raise ImportError("linearmodels package required for Tier 1 analysis")
        
        # Model configuration from PooledPanelConfig
        self.entity_effects = self.config.entity_effects
        self.time_effects = self.config.time_effects
        self.cluster_entity = self.config.cluster_entity
        self.cov_type = self.config.cov_type
        self.drop_absorbed = self.config.drop_absorbed
        
        # Components
        self.panel_handler = PanelDataHandler()
        # Check if we should use strict or flexible validation
        strict_validation = getattr(self.config, 'strict_validation', True)
        world_bank_config = getattr(self.config, 'world_bank_config', None)
        self.validator = PanelDataValidator(strict=strict_validation, world_bank_config=world_bank_config)
        self.model = None
        self.panel_data = None
        
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate input data for pooled panel requirements.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input data to validate
            
        Returns
        -------
        bool
            True if valid
        """
        is_valid, issues = self.validator.validate_for_tier1(data)
        
        if not is_valid:
            for issue in issues:
                error(f"Validation issue: {issue}")
        
        return is_valid
    
    def prepare_panel_data(self, data: pd.DataFrame, 
                          dependent_var: str = 'usd_price',
                          independent_vars: Optional[List[str]] = None) -> Tuple[pd.DataFrame, str, List[str]]:
        """Prepare data for panel regression.
        
        Parameters
        ----------
        data : pd.DataFrame
            Raw 3D panel data
        dependent_var : str
            Dependent variable name
        independent_vars : list, optional
            Independent variable names
            
        Returns
        -------
        tuple
            (panel_df, dependent_var, independent_vars)
        """
        with timer("prepare_panel_data"):
            # Create entity panel
            panel_df = self.panel_handler.create_entity_panel(data)
            
            # Set up variables
            if independent_vars is None:
                # Default: look for common control variables
                potential_vars = ['conflict_intensity', 'distance_to_capital', 
                                 'rainfall', 'temperature', 'fuel_price']
                independent_vars = [v for v in potential_vars if v in panel_df.columns]
                
                if not independent_vars:
                    warning("No independent variables found, running intercept-only model")
                    # Create constant for intercept-only model
                    panel_df['const'] = 1
                    independent_vars = ['const']
            elif len(independent_vars) == 0:
                # Empty list was explicitly passed
                raise ValueError("No independent variables provided. At least one variable is required for panel regression.")
            
            # Log variable setup
            info(f"Dependent variable: {dependent_var}")
            info(f"Independent variables: {independent_vars}")
            
            # Debug: check columns before subsetting
            info(f"Columns before subsetting: {list(panel_df.columns)}")
            
            # Handle missing data - ensure we keep entity column which is created by panel_handler
            required_cols = [dependent_var] + independent_vars
            # Keep all columns needed for panel structure
            structural_cols = ['entity', 'date', 'entity_id', 'time_id', 'governorate', 'commodity']
            # Create entity column name if not present (panel_handler uses 'entity')
            if 'entity' not in panel_df.columns and self.panel_handler.entity_col in panel_df.columns:
                structural_cols.append(self.panel_handler.entity_col)
            
            all_cols = list(set(required_cols + structural_cols))
            # Only keep columns that actually exist
            cols_to_keep = [col for col in all_cols if col in panel_df.columns]
            
            info(f"Columns to keep: {cols_to_keep}")
            
            panel_df_subset = panel_df[cols_to_keep]
            
            # For now, just drop missing values to avoid the MultiIndex issue
            # A more sophisticated approach would set index first then handle missing
            panel_df = panel_df_subset.dropna()
            
            return panel_df, dependent_var, independent_vars
    
    def fit(self, data: pd.DataFrame, 
            dependent_var: str = 'usd_price',
            independent_vars: Optional[List[str]] = None,
            **kwargs) -> 'PooledPanelModel':
        """Fit pooled panel regression.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data (can have MultiIndex or regular index)
        dependent_var : str
            Dependent variable
        independent_vars : list, optional
            Independent variables
        **kwargs
            Additional arguments passed to PanelOLS
            
        Returns
        -------
        self
            Fitted model
        """
        with timer("fit_pooled_panel"):
            self._log_estimation_start(data)
            
            # Handle MultiIndex data - reset index to get entity and date as columns
            if isinstance(data.index, pd.MultiIndex):
                info("Resetting MultiIndex to prepare data for validation")
                # Check if columns already exist to avoid duplication
                index_names = list(data.index.names)
                cols_to_reset = [name for name in index_names if name not in data.columns]
                if cols_to_reset:
                    data = data.reset_index(level=cols_to_reset)
                else:
                    # All index levels already exist as columns
                    data = data.reset_index(drop=True)
            
            # Validate data
            if not self.validate_data(data):
                raise ValueError("Data validation failed")
            
            # Prepare panel data - use passed parameters, not config defaults
            panel_df, dep_var, indep_vars = self.prepare_panel_data(
                data, dependent_var=dependent_var, independent_vars=independent_vars
            )
            
            # Debug: log columns available
            info(f"Columns after prepare_panel_data: {list(panel_df.columns)}")
            
            # Set index for panel
            panel_df = panel_df.set_index(['entity', 'date'])
            
            # Extract dependent and independent variables
            if dep_var not in panel_df.columns:
                raise ValueError(f"Dependent variable '{dep_var}' not found in data. Available columns: {list(panel_df.columns)}")
            
            y = panel_df[dep_var]
            X = panel_df[indep_vars]
            
            log_data_shape("dependent_var", y)
            log_data_shape("independent_vars", X)
            
            # Create PanelData object
            self.panel_data = PanelData(panel_df)
            
            # Initialize model
            self.model = PanelOLS(
                y, X,
                entity_effects=self.entity_effects,
                time_effects=self.time_effects,
                drop_absorbed=self.drop_absorbed
            )
            
            # Fit model
            info("Fitting pooled panel regression")
            try:
                self.fit_result = self.model.fit(**kwargs)
                info(f"Model fitting completed. R-squared: {self.fit_result.rsquared_overall:.4f}")
            except Exception as e:
                error(f"Error fitting model: {e}")
                raise
            
            # Store results
            self._store_results(panel_df, dep_var, indep_vars)
            
            self.is_fitted = True
            self._log_estimation_complete(0.0)  # Use placeholder time
            
            return self
    
    def _store_results(self, panel_df: pd.DataFrame, dep_var: str, indep_vars: List[str]):
        """Store estimation results in standardized container."""
        # Initialize results container with metadata
        metadata = ModelMetadata(
            model_type="PanelOLS",
            tier=1  # Tier 1
        )
        self.results = ResultsContainer(metadata=metadata)
        
        # Store coefficients
        for var in indep_vars:
            if var in self.fit_result.params.index:
                coef = self.fit_result.params[var]
                se = self.fit_result.std_errors[var]
                ci = (self.fit_result.conf_int().loc[var, 'lower'],
                      self.fit_result.conf_int().loc[var, 'upper'])
                
                self.results.add_parameter(var, coef, se)
        
        # Store model fit statistics
        n_obs = self.fit_result.nobs
        n_entities = panel_df.index.get_level_values(0).nunique()
        n_periods = panel_df.index.get_level_values(1).nunique()
        
        # Add statistics using the correct API
        self.results.add_statistic('n_observations', n_obs)
        self.results.add_statistic('n_entities', n_entities)
        self.results.add_statistic('n_periods', n_periods)
        self.results.add_statistic('r_squared', self.fit_result.rsquared)
        
        # Add R-squared measures
        if hasattr(self.fit_result, 'rsquared_within'):
            self.results.add_statistic('r_squared_within', self.fit_result.rsquared_within)
        if hasattr(self.fit_result, 'rsquared_between'):
            self.results.add_statistic('r_squared_between', self.fit_result.rsquared_between)
        if hasattr(self.fit_result, 'rsquared_overall'):
            self.results.add_statistic('r_squared_overall', self.fit_result.rsquared_overall)
        
        # Store fitted values and residuals using the correct API
        self.results.set_fitted_values(self.fit_result.fitted_values)
        self.results.set_residuals(self.fit_result.resids)
        
        # F-test for joint significance
        if hasattr(self.fit_result, 'f_statistic'):
            self.results.add_diagnostic(
                'f_statistic', 
                {'statistic': self.fit_result.f_statistic.stat, 
                 'p_value': self.fit_result.f_statistic.pval}
            )
        
        # Effects counts  
        if self.entity_effects:
            self.results.add_statistic('n_entity_effects', n_entities)
        if self.time_effects:
            self.results.add_statistic('n_time_effects', n_periods)
        
        # Log key metrics
        log_metric("r_squared_within", self.fit_result.rsquared_within)
        log_metric("r_squared_between", self.fit_result.rsquared_between)
        log_metric("n_parameters", len(self.fit_result.params))
    
    def predict(self, data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Generate predictions from fitted model.
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            New data for prediction
            
        Returns
        -------
        pd.Series
            Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        if data is None:
            # In-sample prediction
            return self.fit_result.fitted_values
        else:
            # Out-of-sample prediction
            # Prepare new data
            panel_df, _, indep_vars = self.prepare_panel_data(data)
            panel_df = panel_df.set_index(['entity', 'date'])
            X_new = panel_df[indep_vars]
            
            # Generate predictions
            predictions = self.fit_result.predict(X_new)
            
            return predictions
    
    def get_fixed_effects(self) -> Dict[str, pd.Series]:
        """Extract estimated fixed effects.
        
        Returns
        -------
        dict
            Dictionary with 'entity' and 'time' effects
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        # Use the extract_estimated_effects function
        return extract_estimated_effects(self.fit_result)
    
    def residual_diagnostics(self) -> pd.DataFrame:
        """Run diagnostic tests on residuals.
        
        Returns
        -------
        pd.DataFrame
            Diagnostic test results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        resids = self.fit_result.resids
        
        # Basic residual statistics
        diag_results = {
            'mean': resids.mean(),
            'std': resids.std(),
            'skewness': resids.skew(),
            'kurtosis': resids.kurtosis(),
            'min': resids.min(),
            'max': resids.max()
        }
        
        # Jarque-Bera test for normality
        from scipy import stats
        jb_stat, jb_pval = stats.jarque_bera(resids.dropna())
        diag_results['jarque_bera_stat'] = jb_stat
        diag_results['jarque_bera_pval'] = jb_pval
        
        # Store in results container
        self.results.add_diagnostic('jarque_bera', {'statistic': jb_stat, 'p_value': jb_pval})
        
        return pd.Series(diag_results).to_frame('value')
    
    def summary(self) -> str:
        """Get model summary.
        
        Returns
        -------
        str
            Formatted summary
        """
        if not self.is_fitted:
            return "Model not yet fitted"
        
        # Use linearmodels summary as base
        base_summary = str(self.fit_result.summary)
        
        # Add our custom summary
        custom_summary = self.results.summary()
        
        return f"{base_summary}\n\n{custom_summary}"
    
    def fit_with_corrections(self) -> 'PooledPanelModel':
        """
        Re-fit the model with diagnostic corrections applied.
        
        This method is called after diagnostic tests reveal issues that require
        correcting the standard errors or transforming the data.
        
        Returns
        -------
        self
            Re-fitted model with corrections
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted first before applying corrections")
        
        from yemen_market.utils.logging import info, timer
        
        with timer("fit_with_corrections"):
            info("Re-fitting model with diagnostic corrections")
            
            # Get the original data and settings
            original_data = self.panel_handler.data.copy()
            dep_var = self.results.specification['dependent_var']
            indep_vars = self.results.specification['independent_vars']
            
            # Apply data transformations if needed
            if self.config.get('transformation') == 'first_difference':
                info("Applying first-difference transformation")
                original_data = self._apply_first_difference(original_data, dep_var, indep_vars)
            
            # Prepare data
            panel_df, _, _ = self.prepare_panel_data(original_data, dep_var, indep_vars)
            panel_df = panel_df.set_index(['entity', 'date'])
            
            # Create model specification
            X = panel_df[indep_vars]
            y = panel_df[dep_var]
            
            # Determine which standard errors to use
            se_type = self.config.get('standard_errors', 'clustered')
            
            # Create new PanelOLS instance with corrections
            from linearmodels.panel import PanelOLS
            model = PanelOLS(
                dependent=y,
                exog=X,
                entity_effects=self.config.get('include_entity_effects', True),
                time_effects=self.config.get('include_time_effects', True)
            )
            
            # Fit with appropriate standard errors
            if se_type == 'driscoll_kraay':
                info("Using Driscoll-Kraay standard errors")
                self.fit_result = model.fit(
                    cov_type='kernel',  # Driscoll-Kraay
                    kernel='bartlett',
                    bandwidth=self.config.get('bandwidth', 'auto')
                )
            elif se_type == 'newey_west':
                info("Using Newey-West HAC standard errors")
                self.fit_result = model.fit(
                    cov_type='kernel',  # HAC
                    kernel='bartlett',
                    bandwidth=self.config.get('lags', 'auto')
                )
            elif se_type == 'cluster_robust':
                info("Using cluster-robust standard errors")
                self.fit_result = model.fit(cov_type='clustered')
            else:
                # Default clustered
                self.fit_result = model.fit(cov_type='clustered')
            
            # Update results with correction information
            self.results.add_metadata('corrections_applied', {
                'standard_errors': se_type,
                'transformation': self.config.get('transformation', 'none'),
                'timestamp': pd.Timestamp.now().isoformat()
            })
            
            # Extract and store corrected results
            self._extract_results(self.fit_result)
            
            info(f"Model re-fitted with {se_type} standard errors")
            
            return self
    
    def _apply_first_difference(self, data: pd.DataFrame, dep_var: str, indep_vars: List[str]) -> pd.DataFrame:
        """
        Apply first-difference transformation to handle unit roots.
        
        Parameters
        ----------
        data : pd.DataFrame
            Panel data
        dep_var : str
            Dependent variable
        indep_vars : List[str]
            Independent variables
            
        Returns
        -------
        pd.DataFrame
            First-differenced data
        """
        # Sort by entity and date
        data = data.sort_values(['entity', 'date'])
        
        # Variables to difference
        vars_to_diff = [dep_var] + indep_vars
        
        # Group by entity and difference
        for var in vars_to_diff:
            if var in data.columns:
                data[var] = data.groupby('entity')[var].diff()
        
        # Remove first observation per entity (NaN after differencing)
        data = data.dropna(subset=vars_to_diff)
        
        return data
