"""Econometric models for Yemen market integration analysis.

This package implements the three-tier methodology for analyzing market
integration in conflict-affected settings. The old dual-track approach
has been archived in favor of the unified three-tier framework.

Three-Tier Methodology:
- Tier 1: Pooled panel analysis with fixed effects
- Tier 2: Commodity-specific models with thresholds
- Tier 3: Factor analysis and external validation

For the main analysis, use:
>>> from yemen_market.models.three_tier.integration import ThreeTierAnalysis
>>> analysis = ThreeTierAnalysis(config)
>>> results = analysis.run_full_analysis(data)

For migration from old models:
>>> from yemen_market.models.three_tier.migration import ModelMigrationHelper
"""

# Import main three-tier components
from .three_tier.integration import ThreeTierAnalysis
from .three_tier.migration import ModelMigrationHelper
from .three_tier.core.base_model import BaseThreeTierModel
from .model_comparison import ModelComparison, run_model_comparison

__all__ = [
    # Three-tier base classes
    'BaseThreeTierModel',
    # Model comparison utilities
    'ModelComparison',
    'run_model_comparison',
    # New three-tier methodology
    'ThreeTierAnalysis',
    'ModelMigrationHelper'
]

# Deprecation notice for old imports
def __getattr__(name):
    """Provide helpful error messages for deprecated imports."""
    deprecated_modules = {
        'track1_complex': 'three_tier.tier3_validation',
        'track2_simple': 'three_tier.tier2_commodity',
        'worldbank_threshold_vecm': 'three_tier.tier2_commodity.threshold_vecm',
        'BaseEconometricModel': 'BaseThreeTierModel',
        'ModelResults': 'ResultsContainer'
    }
    
    if name == 'BaseEconometricModel':
        import warnings
        warnings.warn(
            "BaseEconometricModel has been deprecated. Use BaseThreeTierModel instead.",
            DeprecationWarning,
            stacklevel=2
        )
        return BaseThreeTierModel
    
    if name in deprecated_modules:
        raise ImportError(
            f"Module '{name}' has been deprecated and moved to archive.\n"
            f"Please use '{deprecated_modules[name]}' instead.\n"
            f"See MIGRATION_GUIDE.md for details."
        )
    
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")