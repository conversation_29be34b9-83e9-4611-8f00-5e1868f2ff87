"""Price transmission analysis module.

Analyzes price transmission between market pairs using World Bank methodology
to understand how price changes in one market affect prices in other markets,
particularly important for understanding market integration during conflict.
"""

from pathlib import Path
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
import json

import statsmodels.api as sm
from statsmodels.tsa.api import VAR
from statsmodels.tsa.stattools import grangercausalitytests, coint

from yemen_market.utils.logging import info, warning, error, bind


class PriceTransmissionAnalyzer:
    """Analyze price transmission between market pairs.
    
    This class implements price transmission analysis to understand:
    - How quickly price changes transmit between markets
    - Whether markets are integrated (cointegrated)
    - The degree of exchange rate pass-through to prices
    - Dynamic price relationships through VAR models
    
    Attributes
    ----------
    output_dir : Path
        Directory for saving analysis results
    results : dict
        Storage for analysis results
    """
    
    def __init__(self, output_dir: Optional[Path] = None):
        """Initialize the analyzer.
        
        Parameters
        ----------
        output_dir : Path, optional
            Directory for saving results. Defaults to results/price_transmission
        """
        bind(analyzer="PriceTransmissionAnalyzer")
        self.output_dir = output_dir or Path("results/price_transmission")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.results = {}
        
    def analyze_corridor(self, 
                        base_market: str, 
                        target_markets: List[str],
                        commodities: List[str],
                        panel_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze price transmission for a specific trade corridor.
        
        A corridor represents a trade route from a base market (typically
        a port or major distribution center) to target markets.
        
        Parameters
        ----------
        base_market : str
            Source market (e.g., port city like Aden or Al Hodeidah)
        target_markets : list of str
            Destination markets
        commodities : list of str
            Commodities to analyze
        panel_data : pd.DataFrame
            Full panel dataset with columns: market, commodity, date, log_price
            
        Returns
        -------
        dict
            Transmission analysis results including:
            - Transmission speeds by commodity and market pair
            - Cointegration test results
            - Granger causality tests
            - Summary statistics
        """
        info(f"\nAnalyzing corridor: {base_market} → {target_markets}")
        
        corridor_results = {
            'base_market': base_market,
            'target_markets': target_markets,
            'commodities': {}
        }
        
        for commodity in commodities:
            info(f"\n  Commodity: {commodity}")
            
            # Extract data for this commodity
            comm_data = panel_data[panel_data['commodity'] == commodity].copy()
            
            commodity_results = {
                'market_pairs': {},
                'summary': {}
            }
            
            for target in target_markets:
                pair_results = self._analyze_market_pair(
                    base_market, target, comm_data
                )
                if pair_results:
                    commodity_results['market_pairs'][f"{base_market}-{target}"] = pair_results
                    
            # Calculate summary statistics
            if commodity_results['market_pairs']:
                transmission_speeds = [
                    r['transmission_speed'] 
                    for r in commodity_results['market_pairs'].values() 
                    if r and 'transmission_speed' in r
                ]
                
                commodity_results['summary'] = {
                    'avg_transmission_speed': np.mean(transmission_speeds) if transmission_speeds else None,
                    'min_transmission_speed': np.min(transmission_speeds) if transmission_speeds else None,
                    'max_transmission_speed': np.max(transmission_speeds) if transmission_speeds else None,
                    'n_integrated_pairs': sum(
                        1 for r in commodity_results['market_pairs'].values()
                        if r and r.get('cointegrated', False)
                    )
                }
                
            corridor_results['commodities'][commodity] = commodity_results
            
        return corridor_results
    
    def _analyze_market_pair(self, 
                           source: str, 
                           target: str,
                           data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Analyze price transmission between two specific markets.
        
        Parameters
        ----------
        source : str
            Source market name
        target : str
            Target market name
        data : pd.DataFrame
            Price data for the commodity
            
        Returns
        -------
        dict or None
            Analysis results or None if insufficient data
        """
        try:
            # Get price series for both markets
            source_data = data[data['market'] == source].set_index('date')['log_price']
            target_data = data[data['market'] == target].set_index('date')['log_price']
            
            # Align series
            aligned = pd.DataFrame({
                'source': source_data,
                'target': target_data
            }).dropna()
            
            if len(aligned) < 30:
                warning(f"Insufficient data for {source}-{target} pair")
                return None
                
            results = {}
            
            # 1. Correlation analysis
            results['correlation'] = float(aligned['source'].corr(aligned['target']))
            
            # 2. Granger causality test
            try:
                granger_test = grangercausalitytests(
                    aligned[['target', 'source']], 
                    maxlag=4, 
                    verbose=False
                )
                # Extract p-value for lag 1
                results['granger_pvalue'] = float(granger_test[1][0]['ssr_ftest'][1])
                results['granger_causes'] = results['granger_pvalue'] < 0.05
            except:
                results['granger_pvalue'] = None
                results['granger_causes'] = None
                
            # 3. Cointegration test
            coint_test = coint(aligned['source'], aligned['target'])
            results['cointegrated'] = coint_test[1] < 0.05
            results['coint_pvalue'] = float(coint_test[1])
            
            # 4. Error Correction Model (if cointegrated)
            if results['cointegrated']:
                # Estimate ECM
                self._estimate_ecm(aligned, results)
                    
            # 5. VAR model for dynamic analysis
            if len(aligned) > 20:
                self._estimate_var(aligned, results)
                    
            return results
            
        except Exception as e:
            error(f"Error analyzing {source}-{target}: {e}")
            return None
    
    def _estimate_ecm(self, aligned: pd.DataFrame, results: Dict[str, Any]) -> None:
        """Estimate Error Correction Model for cointegrated series.
        
        Parameters
        ----------
        aligned : pd.DataFrame
            Aligned price series
        results : dict
            Dictionary to store results
        """
        try:
            # First difference
            d_source = aligned['source'].diff().dropna()
            d_target = aligned['target'].diff().dropna()
            
            # Error correction term (lagged residual)
            ols = sm.OLS(aligned['target'], sm.add_constant(aligned['source'])).fit()
            ect = (aligned['target'] - ols.predict()).shift(1)
            
            # ECM regression
            ecm_data = pd.DataFrame({
                'd_target': d_target,
                'd_source': d_source,
                'ect': ect[1:]
            }).dropna()
            
            if len(ecm_data) > 10:
                ecm_model = sm.OLS(
                    ecm_data['d_target'],
                    sm.add_constant(ecm_data[['d_source', 'ect']])
                ).fit()
                
                results['short_run_elasticity'] = float(ecm_model.params['d_source'])
                results['adjustment_speed'] = float(-ecm_model.params['ect'])
                results['transmission_speed'] = 1 / abs(ecm_model.params['ect']) if ecm_model.params['ect'] != 0 else np.inf
                
                info(f"    Transmission in {results['transmission_speed']:.1f} periods")
        except Exception as e:
            warning(f"ECM estimation failed: {e}")
    
    def _estimate_var(self, aligned: pd.DataFrame, results: Dict[str, Any]) -> None:
        """Estimate VAR model for dynamic analysis.
        
        Parameters
        ----------
        aligned : pd.DataFrame
            Aligned price series
        results : dict
            Dictionary to store results
        """
        try:
            var_model = VAR(aligned).fit(maxlags=4, ic='aic')
            results['var_lags'] = var_model.k_ar
            
            # Impulse response
            irf = var_model.irf(10)
            # Response of target to source shock
            results['impulse_response'] = irf.orth_irfs[1, 0, :].tolist()
        except Exception as e:
            warning(f"VAR estimation failed: {e}")
    
    def analyze_exchange_passthrough(self, panel_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze exchange rate pass-through to commodity prices.
        
        Estimates how much of exchange rate changes are passed through
        to local prices. Complete pass-through (coefficient = 1) means
        prices fully adjust to exchange rate changes.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Panel with prices and exchange rates
            
        Returns
        -------
        dict
            Pass-through analysis results by commodity including:
            - Pass-through coefficients
            - Statistical significance
            - Interpretation (complete/incomplete)
        """
        info("\nAnalyzing exchange rate pass-through...")
        
        passthrough_results = {}
        
        # Get exchange rate from data
        if 'exchange_rate' not in panel_data.columns:
            # Try to derive from USD prices
            if 'price' in panel_data.columns and 'usd_price' in panel_data.columns:
                panel_data['exchange_rate'] = panel_data['price'] / panel_data['usd_price']
                
        if 'exchange_rate' not in panel_data.columns:
            warning("No exchange rate data available")
            return passthrough_results
            
        # Analyze by commodity
        for commodity in panel_data['commodity'].unique():
            comm_data = panel_data[panel_data['commodity'] == commodity].copy()
            
            # Calculate log changes
            comm_data['d_log_price'] = comm_data.groupby('market')['log_price'].diff()
            comm_data['d_log_er'] = comm_data.groupby('market')['exchange_rate'].apply(lambda x: np.log(x)).diff()
            
            # Panel regression
            regression_data = comm_data[['d_log_price', 'd_log_er']].dropna()
            
            if len(regression_data) > 50:
                model = sm.OLS(
                    regression_data['d_log_price'],
                    sm.add_constant(regression_data['d_log_er'])
                ).fit()
                
                passthrough_results[commodity] = {
                    'passthrough_coefficient': float(model.params['d_log_er']),
                    'passthrough_se': float(model.bse['d_log_er']),
                    'passthrough_pvalue': float(model.pvalues['d_log_er']),
                    'r_squared': float(model.rsquared),
                    'n_obs': int(model.nobs),
                    'interpretation': 'complete' if abs(model.params['d_log_er'] - 1) < 0.1 else 'incomplete'
                }
                
                info(f"  {commodity}: {passthrough_results[commodity]['passthrough_coefficient']:.2%} pass-through")
                
        return passthrough_results
    
    def save_results(self) -> None:
        """Save all analysis results to JSON file."""
        output_file = self.output_dir / "price_transmission_results.json"
        
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
            
        info(f"\nResults saved to: {output_file}")