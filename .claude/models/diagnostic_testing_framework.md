# Diagnostic Testing Framework for Three-Tier Models

**Last Updated**: May 29, 2025

## Overview

The diagnostic testing framework provides a comprehensive suite of econometric tests for the three-tier modeling architecture. It ensures that all models meet World Bank publication standards by automatically detecting and correcting common econometric issues.

## Architecture

```
src/yemen_market/models/three_tier/diagnostics/
├── __init__.py                 # Package exports
├── panel_diagnostics.py        # Main orchestrator
├── test_implementations.py     # Econometric test implementations
├── diagnostic_adapters.py      # ResultsContainer compatibility
└── diagnostic_reports.py       # Report generation
```

## Key Components

### 1. ThreeTierPanelDiagnostics

The main orchestrator class that runs appropriate diagnostic tests based on the tier:

```python
class ThreeTierPanelDiagnostics:
    """
    Diagnostic test suite for three-tier panel models.
    
    This class orchestrates diagnostic testing for all three tiers of the
    modeling framework, applying appropriate tests based on the tier and
    model type.
    
    Parameters
    ----------
    tier : int
        The tier level (1, 2, or 3) to run diagnostics for
    config : Optional[Dict[str, Any]]
        Configuration options for diagnostic tests
        
    Attributes
    ----------
    tier : int
        The tier level being diagnosed
    config : Dict[str, Any]
        Configuration for test thresholds and options
    """
    
    def run_diagnostics(self, results: ResultsContainer, 
                        data: pd.DataFrame,
                        config: Optional[Dict[str, Any]] = None) -> DiagnosticReport:
        """
        Run all diagnostics appropriate for this tier.
        
        Parameters
        ----------
        results : ResultsContainer
            The results container from model estimation
        data : pd.DataFrame
            The original data used for estimation
        config : Optional[Dict[str, Any]]
            Configuration options for diagnostic tests
            
        Returns
        -------
        DiagnosticReport
            A report containing all test results and recommendations
        """
```

### 2. DiagnosticAdapter

Adapts the ResultsContainer interface to extract necessary components for testing:

```python
class DiagnosticAdapter:
    """
    Adapter for extracting diagnostic-relevant data from ResultsContainer.
    
    This class provides a consistent interface for diagnostic tests to
    access model results, regardless of the tier or model type.
    
    Parameters
    ----------
    results : ResultsContainer
        The results container from model estimation
        
    Attributes
    ----------
    results : ResultsContainer
        The results container being adapted
    """
    
    def get_residuals(self) -> np.ndarray:
        """Extract residuals from results container."""
        
    def get_panel_structure(self) -> Dict[str, Any]:
        """Extract panel structure information."""
        
    def get_design_matrix(self) -> np.ndarray:
        """Reconstruct design matrix if needed."""
        
    def get_fitted_values(self) -> np.ndarray:
        """Extract fitted values from results."""
```

### 3. Test Implementations

Core econometric test implementations:

```python
def wooldridge_serial_correlation(residuals: np.ndarray, 
                                 panel_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Wooldridge (2002) test for serial correlation in panel data.
    
    This test is critical for valid inference in panels. It tests the null
    hypothesis of no first-order serial correlation.
    
    Parameters
    ----------
    residuals : np.ndarray
        Model residuals
    panel_info : Dict[str, Any]
        Panel structure information
        
    Returns
    -------
    Dict[str, Any]
        Test results including statistic, p-value, and recommendation
    """

def pesaran_cd_test(residuals: np.ndarray,
                   panel_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Pesaran (2004) test for cross-sectional dependence.
    
    This test is essential given the spatial nature of Yemen markets. It tests
    the null hypothesis of cross-sectional independence.
    
    Parameters
    ----------
    residuals : np.ndarray
        Model residuals
    panel_info : Dict[str, Any]
        Panel structure information
        
    Returns
    -------
    Dict[str, Any]
        Test results including statistic, p-value, and recommendation
    """

def ips_unit_root_test(series: np.ndarray,
                      panel_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Im-Pesaran-Shin (2003) panel unit root test.
    
    This test is required before panel regression to avoid spurious results.
    It tests the null hypothesis that all panels contain unit roots.
    
    Parameters
    ----------
    series : np.ndarray
        Time series data to test
    panel_info : Dict[str, Any]
        Panel structure information
        
    Returns
    -------
    Dict[str, Any]
        Test results including statistic, p-value, and recommendation
    """
```

### 4. Diagnostic Reports

Generates structured reports from test results:

```python
class DiagnosticReport:
    """
    Structured report of diagnostic test results.
    
    This class organizes test results into a structured format and provides
    methods for generating reports in various formats.
    
    Parameters
    ----------
    results : Dict[str, Dict[str, Any]]
        Dictionary of test results
    tier : int
        The tier level being diagnosed
        
    Attributes
    ----------
    results : Dict[str, Dict[str, Any]]
        Dictionary of test results
    tier : int
        The tier level being diagnosed
    """
    
    def has_critical_failures(self) -> bool:
        """Check if any critical tests have failed."""
        
    def get_recommendations(self) -> List[str]:
        """Get list of recommendations based on test results."""
        
    def to_latex(self) -> str:
        """Generate LaTeX table of test results."""
        
    def to_markdown(self) -> str:
        """Generate Markdown table of test results."""
```

## Integration with Three-Tier Runner

The diagnostic framework is integrated with the three-tier runner for automatic execution:

```python
# In three_tier_runner.py
def run_tier1_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
    """Run Tier 1 with automatic diagnostics."""
    
    # Fit model
    model = PooledPanelModel(self.tier1_config)
    model.fit(data)
    
    # Run diagnostics automatically
    if self.run_diagnostics:  # Default True
        diagnostics = ThreeTierPanelDiagnostics(tier=1)
        diag_report = diagnostics.run_diagnostics(
            model.results, 
            data,
            self.diagnostic_config
        )
        
        # Check for critical failures
        if diag_report.has_critical_failures():
            warning("Critical diagnostic failures detected")
            # Apply automatic corrections
            self._apply_diagnostic_corrections(model, diag_report)
            # Re-run model with corrections
    
    return {
        'model': model,
        'results': model.results,
        'diagnostics': diag_report  # NEW
    }
```

## Tier-Specific Diagnostics

### Tier 1 (Pooled Panel)

- Wooldridge test for serial correlation
- Pesaran CD test for cross-sectional dependence
- Modified Wald test for heteroskedasticity
- Im-Pesaran-Shin test for panel unit roots
- Hausman test for random vs. fixed effects
- Breusch-Pagan LM test for random effects

### Tier 2 (Commodity-Specific)

- Wooldridge test for serial correlation
- Breusch-Godfrey test for higher-order serial correlation
- Jarque-Bera test for normality
- ARCH test for conditional heteroskedasticity
- Ramsey RESET test for functional form
- Johansen test for cointegration

### Tier 3 (Factor Analysis)

- Kaiser-Meyer-Olkin test for sampling adequacy
- Bartlett's test of sphericity
- Parallel analysis for factor retention
- Cronbach's alpha for reliability
- Factor loading significance tests

## Automatic Corrections

When diagnostic tests fail, the framework applies appropriate corrections:

### Serial Correlation

```python
def _apply_serial_correlation_correction(self, results: ResultsContainer) -> None:
    """
    Apply Newey-West HAC standard errors for serial correlation.
    
    Parameters
    ----------
    results : ResultsContainer
        The results container to correct
    """
    # Implementation details...
```

### Cross-Sectional Dependence

```python
def _apply_driscoll_kraay_correction(self, results: ResultsContainer) -> None:
    """
    Apply Driscoll-Kraay standard errors for cross-sectional dependence.
    
    Parameters
    ----------
    results : ResultsContainer
        The results container to correct
    """
    # Implementation details...
```

### Heteroskedasticity

```python
def _apply_robust_standard_errors(self, results: ResultsContainer) -> None:
    """
    Apply cluster-robust standard errors for heteroskedasticity.
    
    Parameters
    ----------
    results : ResultsContainer
        The results container to correct
    """
    # Implementation details...
```

## Test Implementation Details

### Wooldridge Test

The Wooldridge test for serial correlation in panel data is implemented as follows:

1. Estimate the model and obtain residuals
2. Compute first-differenced residuals
3. Regress first-differenced residuals on their lag
4. Test if the coefficient on the lagged residuals is -0.5

```python
def wooldridge_serial_correlation(residuals: np.ndarray, 
                                 panel_info: Dict[str, Any]) -> Dict[str, Any]:
    """Wooldridge test for serial correlation in panel data."""
    # Reshape residuals to panel format
    panel_residuals = _reshape_to_panel(residuals, panel_info)
    
    # First difference the residuals
    diff_residuals = {}
    for entity in panel_residuals:
        diff_residuals[entity] = np.diff(panel_residuals[entity])
    
    # Lag the differenced residuals
    lagged_diff_residuals = {}
    for entity in diff_residuals:
        lagged_diff_residuals[entity] = diff_residuals[entity][:-1]
        diff_residuals[entity] = diff_residuals[entity][1:]
    
    # Stack the data
    y = np.concatenate([diff_residuals[e] for e in diff_residuals])
    X = np.concatenate([lagged_diff_residuals[e] for e in lagged_diff_residuals])
    X = sm.add_constant(X)
    
    # Run regression
    model = sm.OLS(y, X)
    results = model.fit()
    
    # Test if coefficient is -0.5
    coef = results.params[1]
    se = results.bse[1]
    t_stat = (coef + 0.5) / se
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), results.df_resid))
    
    return {
        'test_name': 'Wooldridge Serial Correlation',
        'statistic': float(t_stat),
        'p_value': float(p_value),
        'null_hypothesis': 'No first-order serial correlation',
        'reject_null': p_value < 0.05,
        'recommendation': 'Use Newey-West HAC standard errors' if p_value < 0.05 else 'No correction needed'
    }
```

### Pesaran CD Test

The Pesaran CD test for cross-sectional dependence is implemented as follows:

1. Reshape residuals to wide format (entities × time)
2. Compute pairwise correlations between entities
3. Calculate the CD statistic
4. Test against the standard normal distribution

```python
def pesaran_cd_test(residuals: np.ndarray,
                   panel_info: Dict[str, Any]) -> Dict[str, Any]:
    """Pesaran CD test for cross-sectional dependence."""
    # Reshape residuals to wide format
    wide_residuals = _reshape_to_wide(residuals, panel_info)
    
    # Compute pairwise correlations
    N, T = wide_residuals.shape
    rho = np.zeros((N, N))
    for i in range(N):
        for j in range(i+1, N):
            # Compute correlation
            valid = ~np.isnan(wide_residuals[i]) & ~np.isnan(wide_residuals[j])
            if valid.sum() > 1:
                rho[i, j] = np.corrcoef(wide_residuals[i, valid], 
                                        wide_residuals[j, valid])[0, 1]
                rho[j, i] = rho[i, j]
    
    # Calculate CD statistic
    cd_stat = np.sqrt(2 * T / (N * (N - 1))) * np.sum(rho[np.triu_indices(N, k=1)])
    
    # Compute p-value
    p_value = 2 * (1 - stats.norm.cdf(abs(cd_stat)))
    
    return {
        'test_name': 'Pesaran CD Test',
        'statistic': float(cd_stat),
        'p_value': float(p_value),
        'null_hypothesis': 'Cross-sectional independence',
        'reject_null': p_value < 0.05,
        'recommendation': 'Use Driscoll-Kraay standard errors' if p_value < 0.05 else 'No correction needed'
    }
```

## Usage Examples

### Basic Usage

```python
# Create diagnostics for Tier 1
diagnostics = ThreeTierPanelDiagnostics(tier=1)

# Run diagnostics
report = diagnostics.run_diagnostics(model.results, data)

# Check for critical failures
if report.has_critical_failures():
    print("Critical diagnostic failures detected!")
    print(report.get_recommendations())
    
# Generate LaTeX table for publication
latex_table = report.to_latex()
```

### Custom Configuration

```python
# Custom configuration
config = {
    'significance_level': 0.01,  # Stricter threshold
    'tests': {
        'wooldridge': {'enabled': True},
        'pesaran_cd': {'enabled': True},
        'ips_unit_root': {'enabled': False},  # Disable this test
        'modified_wald': {'enabled': True}
    },
    'corrections': {
        'auto_apply': True,  # Automatically apply corrections
        'driscoll_kraay': {'max_lag': 4}
    }
}

# Run diagnostics with custom config
diagnostics = ThreeTierPanelDiagnostics(tier=1)
report = diagnostics.run_diagnostics(model.results, data, config)
```

### Integration with Analysis Script

```python
# In analysis script
def run_analysis():
    # Load data
    data = load_panel_data()
    
    # Run three-tier analysis
    runner = ThreeTierRunner(config={
        'run_diagnostics': True,
        'diagnostic_config': {
            'significance_level': 0.05,
            'auto_apply_corrections': True
        }
    })
    
    # Run all tiers
    tier1_results = runner.run_tier1_analysis(data)
    tier2_results = runner.run_tier2_analysis(data)
    tier3_results = runner.run_tier3_analysis(data)
    
    # Check diagnostic results
    print("Tier 1 Diagnostics:")
    print(tier1_results['diagnostics'].to_markdown())
    
    print("Tier 2 Diagnostics:")
    print(tier2_results['diagnostics'].to_markdown())
    
    print("Tier 3 Diagnostics:")
    print(tier3_results['diagnostics'].to_markdown())
```

## Testing

The diagnostic framework has a comprehensive test suite:

```
tests/unit/models/three_tier/diagnostics/
├── __init__.py
├── test_panel_diagnostics.py
├── test_diagnostic_implementations.py
├── test_diagnostic_adapters.py
└── test_diagnostic_reports.py
```

Example test for Wooldridge implementation:

```python
def test_wooldridge_with_known_data():
    """Test Wooldridge against known result."""
    # Create synthetic panel with known serial correlation
    np.random.seed(42)
    T, N = 10, 20
    panel_info = {'n_entities': N, 'n_periods': T, 'entity_ids': list(range(N))}
    
    # Generate residuals with AR(1) process
    residuals = np.zeros(N * T)
    for i in range(N):
        e = np.random.normal(0, 1, T)
        for t in range(1, T):
            e[t] = 0.7 * e[t-1] + np.random.normal(0, 0.5)
        residuals[i*T:(i+1)*T] = e
    
    # Run test
    result = wooldridge_serial_correlation(residuals, panel_info)
    
    # Check results
    assert result['reject_null'] == True
    assert result['p_value'] < 0.05
    assert 'Newey-West' in result['recommendation']
```

## Performance Considerations

The diagnostic framework is designed to be efficient, but some tests can be computationally intensive:

- Pesaran CD test: O(N²) complexity with number of entities
- IPS unit root test: Runs ADF test for each entity
- Bootstrap-based tests: Can be slow for large datasets

Optimization strategies:

1. **Parallel execution**: Use joblib for parallel test execution
2. **Caching**: Cache test results for unchanged data
3. **Sampling**: For very large panels, consider testing on a sample
4. **Early stopping**: Skip expensive tests if critical tests already fail

## Future Enhancements

1. **Performance Optimization**
   - Parallelize test execution
   - Implement caching of test results
   - Optimize matrix operations

2. **Enhanced Reporting**
   - Implement LaTeX table export
   - Create interactive diagnostic dashboard
   - Add visualization of test results

3. **Additional Tests**
   - Spatial autocorrelation tests (Moran's I, Geary's C)
   - Panel threshold tests
   - Structural break tests for panels

## References

- Wooldridge, J.M. (2002) "Econometric Analysis of Cross Section and Panel Data"
- Pesaran, M.H. (2004) "General Diagnostic Tests for Cross Section Dependence in Panels"
- Im, K.S., Pesaran, M.H., and Shin, Y. (2003) "Testing for Unit Roots in Heterogeneous Panels"
- Driscoll, J.C. and Kraay, A.C. (1998) "Consistent Covariance Matrix Estimation with Spatially Dependent Panel Data"
