# Briefing: Yemen Market Integration - Diagnostic Framework Migration

## Context for New Claude Instance

You are working on the Yemen Market Integration project, which analyzes food market dynamics in conflict-affected Yemen. A major diagnostic framework migration has just been completed. Here's what you need to know:

### Project Overview
- **Purpose**: Analyze market integration in Yemen using World Bank-standard econometric methods
- **Architecture**: Three-tier modeling framework (Pooled → Threshold → Factor Analysis)
- **Standards**: Must meet World Bank publication requirements

### Recent Work Completed (May 29, 2025)

#### Diagnostic Framework Migration
A comprehensive diagnostic testing framework has been migrated from legacy modules to integrate with the three-tier architecture:

1. **New Structure Created**:
   ```
   src/yemen_market/models/three_tier/diagnostics/
   ├── panel_diagnostics.py      # Main orchestrator (ThreeTierPanelDiagnostics)
   ├── test_implementations.py   # Econometric tests (Wooldridge, Pesaran CD, etc.)
   ├── diagnostic_adapters.py    # ResultsContainer compatibility layer
   └── diagnostic_reports.py     # Structured report generation
   ```

2. **Key Tests Implemented**:
   - Wooldridge Test (serial correlation)
   - Pesaran CD Test (cross-sectional dependence)
   - Im-Pesaran-Shin Test (panel unit roots)
   - Modified Wald Test (heteroskedasticity)
   - Breusch-Pagan LM Test (cross-sectional dependence)

3. **Integration Features**:
   - Automatic execution after model estimation
   - Automatic corrections (e.g., Driscoll-Kraay SEs for spatial correlation)
   - Tier-specific test configurations
   - Results stored in ResultsContainer format

4. **Legacy Modules Deprecated**:
   - `src/yemen_market/diagnostics/worldbank_diagnostics.py`
   - `src/yemen_market/diagnostics/test_battery.py`
   - Both will be removed in v2.0

### How to Gain Project Context

1. **Start with Core Documentation**:
   - `CLAUDE.md` - Project coding standards and overview
   - `METHODOLOGY.md` - Econometric methodology
   - `.claude/ACTIVE_CONTEXT.md` - Current project state

2. **Review Recent Work**:
   - `reports/diagnostic_migration_summary.md` - What was just completed
   - `.claude/models/diagnostic_testing_framework.md` - Technical details

3. **Understand the Architecture**:
   - Three-tier models: `src/yemen_market/models/three_tier/`
   - Integration runner: `src/yemen_market/models/three_tier/integration/three_tier_runner.py`
   - Results interface: `src/yemen_market/models/three_tier/core/results_container.py`

4. **Test the System**:
   ```bash
   # Verify diagnostic integration
   python scripts/test_diagnostic_integration.py
   
   # Run full analysis with diagnostics
   python scripts/analysis/run_three_tier_models.py
   ```

### Key Technical Details

1. **ResultsContainer Interface**:
   - All models return results in ResultsContainer format
   - Diagnostics extract data via DiagnosticAdapter
   - Uniform interface across all three tiers

2. **Automatic Corrections**:
   - Serial correlation → Driscoll-Kraay standard errors
   - Heteroskedasticity → Cluster-robust standard errors
   - Cross-sectional dependence → Driscoll-Kraay SEs

3. **Coding Standards**:
   - Enhanced logging only (no print statements)
   - Complete implementations (no placeholders)
   - Type hints required
   - NumPy-style docstrings
   - Test coverage > 90%

### Current Priorities

1. **Immediate Tasks**:
   - Monitor diagnostic performance in production
   - Fine-tune test thresholds for Yemen data
   - Update API documentation

2. **Next Phase**:
   - Performance optimization for large datasets
   - Enhanced reporting (LaTeX tables)
   - Interactive diagnostic dashboards

### Important Commands

```bash
# Run tests
pytest tests/unit/models/three_tier/diagnostics/

# Build panel datasets
python scripts/analysis/build_panel_datasets.py

# Run three-tier analysis
python scripts/analysis/run_three_tier_models.py

# Generate reports
python scripts/generate_report.py
```

### Key Questions to Ask Yourself

1. Does my code follow the standards in CLAUDE.md?
2. Am I using ResultsContainer for all model outputs?
3. Are diagnostics running automatically in my analysis?
4. Have I added appropriate logging with context?
5. Is my implementation complete (no TODOs or placeholders)?

### Where to Find Help

- **Diagnostic Framework**: `.claude/models/diagnostic_testing_framework.md`
- **Three-Tier Models**: `docs/models/yemen_panel_methodology.md`
- **Data Pipeline**: `docs/data/data_pipeline_detailed.md`
- **Recent Reports**: `reports/` directory

Remember: This is a World Bank publication-standard project. Quality, rigor, and completeness are non-negotiable.
