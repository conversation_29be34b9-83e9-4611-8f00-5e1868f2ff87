# Claude Code Prompts

This directory contains reusable prompts for working with <PERSON> on the Yemen Market Integration project.

## Available Prompts

### 1. `initial_project_prompt.md`

- **Purpose**: Original project requirements and full context
- **Use when**: Starting fresh or need complete project understanding
- **Contains**: 12-week plan, technical requirements, deliverables

### 2. `eda_phase_prompt.md`

- **Purpose**: Exploratory Data Analysis phase guidance
- **Use when**: Working on data visualization and initial analysis
- **Contains**: EDA objectives, visualization requirements, specific analyses

### 3. `update_claude_docs_prompt.md` ⭐ NEW

- **Purpose**: Comprehensive guide for updating .claude/ documentation
- **Use when**: Major updates, end of sprint, new features completed
- **Contains**: Full update checklist, specific file instructions, templates

### 4. `quick_update_template.md` ⭐ NEW

- **Purpose**: Quick documentation updates
- **Use when**: Daily updates, minor progress, quick status changes
- **Contains**: Short-form templates, copy-paste snippets

## How to Use

1. **Open the relevant prompt file**
2. **Copy the prompt text**
3. **Paste into Claude Code** with your specific updates
4. **<PERSON> will update the documentation** following project standards

## Quick Examples

### After completing a feature

```markdown
Use quick_update_template.md:
- Copy the "After Completing a Major Feature" section
- Fill in [FEATURE NAME] with your feature
- Paste into Claude Code
```

### Daily standup

```markdown
Use quick_update_template.md:
- Copy the "Daily Standup" template
- Fill in DONE/DOING/BLOCKERS/NEXT
- Paste into Claude Code
```

### Major milestone

```markdown
Use update_claude_docs_prompt.md:
- Copy the full update prompt
- Fill in detailed accomplishments
- Paste into Claude Code for comprehensive update
```

## Creating New Prompts

When creating new prompts:

1. Use descriptive filename: `[purpose]_prompt.md`
2. Include clear "Purpose" and "Use when" sections
3. Provide fill-in-the-blank templates where possible
4. Add to this README

## Best Practices

- **Keep prompts focused** - one purpose per file
- **Use placeholders** - [ITEM] for user to fill in
- **Include examples** - show expected format
- **Version prompts** - update as project evolves
- **Test prompts** - ensure Claude Code understands them
